import { AnimatePresence, motion, MotionProps } from "framer-motion";
import { useEffect, useState, type ReactNode } from "react";
import { WorldSettings as LegacyWorldSettings } from "../WorldSettings";

import ThreejsSceneHierarchyView from "../debugging/ThreejsSceneHierarchyView";
import { KeyboardShortcutsModal } from "../editor/help/KeyboardShortcutsModal";
import { DevSettingsPanel } from "../modals/DevSettingsPanel";
import { EnvironmentSettingsPanel } from "../modals/EnvironmentSettingsPanel";
import { FeedbackModal } from "@/components/editor/help/FeedbackModal";
import { Client } from "@/core/client";
import { useEditorUIState } from "@/contexts/EditorUIContext";

export function EditorUI() {
  const appIsReady = useIsAppReady();

  const {
    isEnvironmentSettingsOpen,
    isDevSettingsOpen,
    isLegacyWorldSettingsOpen,
  } = useEditorUIState();

  if (!appIsReady) {
    return null;
  }

  return (
    <>
      <div
        id="editor-ui"
        className="fixed inset-[1rem] top-[4.5rem] select-none pointer-events-none"
      >
        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="center"
          isOpen={isLegacyWorldSettingsOpen}
        >
          <LegacyWorldSettings />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="center"
          isOpen={isEnvironmentSettingsOpen}
        >
          <EnvironmentSettingsPanel />
        </ScreenDockedPanel>

        <ScreenDockedPanel
          alignmentHorizontal="left"
          alignmentVertical="center"
          isOpen={isDevSettingsOpen}
        >
          <DevSettingsPanel />
        </ScreenDockedPanel>

        <KeyboardShortcutsModal />
        <FeedbackModal />
      </div>

      <ThreejsSceneHierarchyView />
    </>
  );
}

function ScreenDockedPanel({
  children,
  alignmentHorizontal,
  alignmentVertical = "center",
  isOpen,
}: {
  children: ReactNode;
  alignmentHorizontal: "left" | "right";
  alignmentVertical: "top" | "bottom" | "center";
  isOpen: boolean;
}) {
  const SLIDE_DISTANCE = 400;

  const getHorizontalAnimation = (): MotionProps => {
    const transitionIn = {
      type: "spring",
      stiffness: 300,
      damping: 8,
      mass: 0.4,
    } as const;

    const transitionOut = {
      duration: 0.2,
      ease: "easeInOut",
    } as const;

    if (alignmentHorizontal === "left") {
      return {
        initial: {
          x: -SLIDE_DISTANCE,
          opacity: 0,
          transition: transitionIn,
        },
        exit: {
          x: -SLIDE_DISTANCE,
          opacity: 0,
          transition: transitionOut,
        },
      };
    } else {
      return {
        initial: {
          x: SLIDE_DISTANCE,
          opacity: 0,
          transition: transitionIn,
        },
        exit: {
          x: SLIDE_DISTANCE,
          opacity: 0,
          transition: transitionOut,
        },
      };
    }
  };

  const getVerticalAlignment = () => {
    switch (alignmentVertical) {
      case "top":
        return "justify-start";
      case "bottom":
        return "justify-end";
      case "center":
      default:
        return "justify-center";
    }
  };

  const getHorizontalAlignment = () => {
    return alignmentHorizontal === "left" ? "items-start" : "items-end";
  };

  const animationProps = getHorizontalAnimation();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          {...animationProps}
          animate={{ x: 0, opacity: 1 }}
          className={`absolute inset-0 flex flex-col ${getVerticalAlignment()} ${getHorizontalAlignment()}`}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

function useIsAppReady() {
  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    const onInit = () => {
      setAppIsReady(true);
    };

    Client.events.on("initialized", onInit);

    return () => {
      Client.events.off("initialized", onInit);
    };
  }, []);

  return appIsReady;
}
