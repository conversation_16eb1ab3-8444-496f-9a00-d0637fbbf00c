{"indexes": [{"collectionGroup": "Assets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "WorldPlaySessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "worldId", "order": "ASCENDING"}, {"fieldPath": "playTimeSeconds", "order": "ASCENDING"}]}, {"collectionGroup": "WorldPlaySessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "worldId", "order": "ASCENDING"}, {"fieldPath": "startedAt", "order": "DESCENDING"}]}, {"collectionGroup": "Worlds", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isFeatured", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "Worlds", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isFeatured", "order": "ASCENDING"}, {"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "Worlds", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "Worlds", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ownerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "Worlds", "queryScope": "COLLECTION", "fields": [{"fieldPath": "ownerId", "order": "ASCENDING"}, {"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "WorldScores", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "score", "order": "DESCENDING"}]}, {"collectionGroup": "WorldUserRelations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "kind", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "WorldPlaySessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "worldId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}