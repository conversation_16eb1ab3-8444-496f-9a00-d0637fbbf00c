import {
  Color,
  HalfFloatType,
  Mesh,
  MeshStandardMaterial,
  Object3D,
  Scene,
  ShaderMaterial,
  Texture,
  Vector2,
  WebGLRenderer,
  WebGLRenderTarget,
} from "three";

import { Pass, FullScreenQuad } from "three/examples/jsm/postprocessing/Pass";

import defaultVertexShader from "./shaders/vertex.glsl";
import edgeFragmentShader from "./shaders/edges/fragment.glsl";
import {
  GameEntity,
  localColor,
  MeshEntity,
  ParticleEntity,
  PrimitiveEntity,
  UserEntity,
} from "@/core/entity";
import { outlineLayers } from "@/core/util/RaycastUtils";

const color = new Color();
type IMaterial = {
  roughness: number;
  metalness: number;
  emissiveIntensity: number;
  emissive: Color;
  color: Color;
  emissiveMap: Texture | null;
};
const tempMaterialData: Omit<IMaterial, "emissive"> = {
  color: new Color(0, 0, 0),
  roughness: 1,
  metalness: 1,
  emissiveIntensity: 1,
  emissiveMap: null,
};

export function getEntityFromObject(object: Object3D): GameEntity | undefined {
  let obj: Object3D | null = object;
  while (obj) {
    if ("entity" in obj && obj.entity instanceof GameEntity) {
      return obj.entity;
    }
    obj = obj.parent;
  }
  return undefined;
}

class OutlinePass extends Pass {
  selectedEntities: Map<number, GameEntity[]>;
  selectedObjects: Map<number, Object3D[]>;
  colors: number[];
  objects: Object3D[][];
  localIndex: number = -1;
  localFlag: boolean = false;

  downSampleRatio: number;

  resolution: Vector2;

  renderTargetBuffer: WebGLRenderTarget;

  edgeDetectionMaterial: ShaderMaterial;

  oldClearColor: Color;

  oldClearAlpha: number;

  fsQuad: FullScreenQuad;

  private _originalMaterialProps = new Map<string, IMaterial>();
  private _originalHoverMaterialProps = new Map<string, IMaterial>();
  _randomCache = new Map<string, number>();

  hover = true;
  hoveredEntity: GameEntity | null = null;
  hoveredObjects: Object3D[] = [];
  hoveredColor: number = new Color()
    .lerpColors(new Color(localColor), new Color(0, 0, 0), 0.55)
    .getHex();

  constructor(
    resolution: Vector2,
    private readonly scene: Scene,
    private readonly lazyLocalUserEntity: () => UserEntity
  ) {
    super();
    this.selectedEntities = new Map();
    this.selectedObjects = new Map();
    this.colors = [];
    this.objects = [];
    this.downSampleRatio = 2 * (window.devicePixelRatio || 1);

    this.resolution =
      resolution !== undefined
        ? new Vector2(resolution.x, resolution.y)
        : new Vector2(256, 256);

    this.renderTargetBuffer = new WebGLRenderTarget(
      this.resolution.x,

      this.resolution.y,

      { type: HalfFloatType }
    );

    this.renderTargetBuffer.texture.name = "OutlinePass.depth";

    this.renderTargetBuffer.texture.generateMipmaps = false;

    this.edgeDetectionMaterial = this.getEdgeDetectionMaterial();

    this.enabled = true;

    this.needsSwap = true;

    this.oldClearColor = new Color();

    this.oldClearAlpha = 1;

    this.fsQuad = new FullScreenQuad();
  }

  setHoveredColor(color: number) {
    this.hoveredColor = color;
  }

  setHoverEnabled(enabled: boolean) {
    this.hover = enabled;
    if (!enabled) {
      this.setHoveredEntity(null);
    }
  }

  setHovered(entity: GameEntity | null, value: boolean) {
    if (
      entity &&
      (entity instanceof PrimitiveEntity ||
        entity instanceof MeshEntity ||
        entity instanceof ParticleEntity)
    ) {
      entity.setHovered(value);
    }
  }

  setHoveredEntity(entity: GameEntity | null) {
    const newEntity = this.hover ? entity : null;
    if (this.hoveredEntity === newEntity) return;

    const shouldShowGrabCursor = entity && !entity.getLocked();
    if (document.body.style.cursor !== "grabbing")
      document.body.style.cursor = shouldShowGrabCursor ? "grab" : "auto";

    this.setHovered(this.hoveredEntity, false);
    this.hoveredEntity = newEntity;

    this.hoveredObjects = this.getObjects(
      this.hoveredEntity ? [this.hoveredEntity] : []
    );

    this.setHovered(newEntity, true);
  }

  getEdgeDetectionMaterial() {
    return new ShaderMaterial({
      uniforms: {
        tex: { value: null },
        rawTex: { value: null },
        texSize: { value: new Vector2(0.5, 0.5) },
      },

      vertexShader: defaultVertexShader,

      fragmentShader: edgeFragmentShader,
    });
  }

  override dispose() {
    this.renderTargetBuffer.dispose();

    this.edgeDetectionMaterial.dispose();

    this.fsQuad.dispose();
  }

  setSelectedObjects(color: number, selectedEntities: GameEntity[]) {
    const objects = this.getObjects(selectedEntities);
    this.selectedEntities.set(color, selectedEntities);
    this.selectedObjects.set(color, objects);
    this.colors = Array.from(this.selectedObjects.keys());
    this.objects = Array.from(this.selectedObjects.values());
  }

  override setSize(width: number, height: number) {
    this.renderTargetBuffer.setSize(width, height);
  }

  private renderObjects = (renderer: WebGLRenderer) => {
    if (this.hover) this.saveHoverMeshes(this.hoveredObjects);
    const localUserColor = this.lazyLocalUserEntity().getColor();

    for (let i = 0; i < this.colors.length; i += 1) {
      this.saveSelectedMeshes(this.objects[i]);
    }
    this.localFlag = false;

    for (let i = 0; i < this.colors.length; i += 1) {
      if (this.colors[i] === localUserColor) {
        this.localIndex = i;
        this.localFlag = true;
      }
    }
    for (let i = 0; i < this.colors.length; i += 1) {
      if (this.localIndex !== i) {
        this.processSelectedMeshes(this.objects[i], this.colors[i]);
      }
    }
    if (this.localFlag)
      this.processSelectedMeshes(this.objects[this.localIndex], localColor);
    if (this.hover) this.processHoverMeshes(this.hoveredObjects);

    const camera = this.lazyLocalUserEntity().getCamera();
    if (!camera) {
      throw new Error("Camera not found");
    }
    const prevLayers = camera.layers;
    camera.layers = outlineLayers;
    const prevEnv = this.scene.environment;
    const prevBackground = this.scene.background;
    const prevFog = this.scene.fog;
    this.scene.environment = null;
    this.scene.background = null;
    this.scene.fog = null;
    renderer.render(this.scene, camera);
    camera.layers = prevLayers;
    this.scene.environment = prevEnv;
    this.scene.background = prevBackground;
    this.scene.fog = prevFog;

    for (let i = 0; i < this.colors.length; i += 1) {
      this.unprocessSelectedMeshes(this.objects[i]);
    }
    if (this.hover) this.unprocessHoverMeshes(this.hoveredObjects);
  };

  saveSelectedMeshes(objects: Object3D[]) {
    for (let k = 0; k < objects.length; k += 1) {
      objects[k].traverseVisible((obj) => {
        if ((obj as Mesh).isMesh) {
          const mesh = obj as Mesh;
          this.saveMaterialProps(mesh, this._originalMaterialProps);
        }
      });
    }
  }

  processSelectedMeshes(objects: Object3D[], color: number) {
    for (let k = 0; k < objects.length; k += 1) {
      objects[k].traverseVisible((obj) => {
        if ((obj as Mesh).isMesh) {
          const mesh = obj as Mesh;
          const entity = getEntityFromObject(mesh);
          if (entity) {
            this.applyOutlineProps(
              mesh,
              entity.id,
              entity?.getLocked?.() ? 0x808080 : color
            );
          }
        }
      });
    }
  }

  unprocessSelectedMeshes(objects: Object3D[]) {
    for (let k = 0; k < objects.length; k += 1) {
      objects[k].traverseVisible((obj) => {
        if ((obj as Mesh).isMesh) {
          const mesh = obj as Mesh;
          this.restoreMaterialProps(mesh, this._originalMaterialProps);
        }
      });
    }
  }

  saveHoverMeshes(objects: Object3D[]) {
    if (this.hover) {
      for (let k = 0; k < objects.length; k += 1) {
        objects[k].traverseVisible((obj) => {
          if ((obj as Mesh).isMesh) {
            const mesh = obj as Mesh;
            this.saveMaterialProps(mesh, this._originalHoverMaterialProps);
          }
        });
      }
    }
  }

  processHoverMeshes(objects: Object3D[]) {
    if (this.hover) {
      for (let k = 0; k < objects.length; k += 1) {
        objects[k].traverseVisible((obj) => {
          if ((obj as Mesh).isMesh) {
            const mesh = obj as Mesh;
            const entity = getEntityFromObject(mesh);
            if (entity) {
              this.applyOutlineProps(
                mesh,
                entity.id,
                entity?.getLocked?.() ? 0x808080 : this.hoveredColor
              );
            }
          }
        });
      }
    }
  }

  unprocessHoverMeshes(objects: Object3D[]) {
    if (this.hover) {
      for (let k = 0; k < objects.length; k += 1) {
        objects[k].traverseVisible((obj) => {
          if ((obj as Mesh).isMesh) {
            const mesh = obj as Mesh;
            this.restoreMaterialProps(mesh, this._originalHoverMaterialProps);
          }
        });
      }
    }
  }

  // Save original material properties
  private saveMaterialProps(mesh: Mesh, store: Map<string, IMaterial>) {
    const saveProps = (mat: MeshStandardMaterial) => {
      if (!store.has(mat.uuid)) {
        store.set(mat.uuid, {
          color: mat.color.clone(),
          roughness: mat.roughness,
          metalness: mat.metalness,
          emissive: mat.emissive?.clone(),
          emissiveIntensity: mat.emissiveIntensity,
          emissiveMap: mat.emissiveMap,
        });
      }
      // Store mesh reference for restoration
    };
    if (Array.isArray(mesh.material)) {
      for (let i = 0; i < mesh.material.length; i += 1) {
        saveProps(mesh.material[i] as MeshStandardMaterial);
      }
    } else {
      saveProps(mesh.material as MeshStandardMaterial);
    }
  }

  private applyProps = (
    mat: MeshStandardMaterial,
    id: string,
    color: number
  ) => {
    mat.color.copy(tempMaterialData.color);
    mat.roughness = tempMaterialData.roughness;
    mat.metalness = tempMaterialData.metalness;
    if (mat.emissive) {
      mat.emissive.set(color);
      // Add random offset if needed
      mat.emissive.r += this.getRandom(`${id}-r`) * 1e-2;
      mat.emissive.g += this.getRandom(`${id}-g`) * 1e-2;
      mat.emissive.b += this.getRandom(`${id}-b`) * 1e-2;
      mat.emissiveIntensity = tempMaterialData.emissiveIntensity;
      mat.emissiveMap = tempMaterialData.emissiveMap;
    }
  };
  // Apply outline properties in place
  private applyOutlineProps(mesh: Mesh, id: string, color: number) {
    if (Array.isArray(mesh.material)) {
      for (let i = 0; i < mesh.material.length; i += 1) {
        this.applyProps(mesh.material[i] as MeshStandardMaterial, id, color);
      }
    } else {
      this.applyProps(mesh.material as MeshStandardMaterial, id, color);
    }
  }

  // Restore original material properties
  private restoreMaterialProps(mesh: Mesh, store: Map<string, IMaterial>) {
    const restoreProps = (mat: MeshStandardMaterial) => {
      const original = store.get(mat.uuid);
      if (original) {
        mat.color.copy(original.color);
        mat.roughness = original.roughness;
        mat.metalness = original.metalness;
        if (original.emissive) {
          mat.emissive.copy(original.emissive);
        }
        mat.emissiveIntensity = original.emissiveIntensity;
        mat.emissiveMap = original.emissiveMap;
        store.delete(mat.uuid);
      }
    };
    if (Array.isArray(mesh.material)) {
      for (let i = 0; i < mesh.material.length; i += 1) {
        restoreProps(mesh.material[i] as MeshStandardMaterial);
      }
    } else {
      restoreProps(mesh.material as MeshStandardMaterial);
    }
  }

  getObjects(entities: GameEntity[]) {
    const objects: Object3D[] = [];

    for (let i = 0; i < entities.length; i += 1) {
      const e = entities[i];
      if (
        e instanceof MeshEntity ||
        e instanceof PrimitiveEntity ||
        e instanceof ParticleEntity
      ) {
        const mesh = e.getMesh();
        if (mesh) objects.push(mesh);
      }
      if (e instanceof MeshEntity) {
        for (let j = 0; j < e.extraChildren.length; j += 1) {
          objects.push(e.extraChildren[j]);
        }
      }
    }

    return objects;
  }

  colorsEquality(color1: Color, number2: number) {
    color.set(number2);
    return (
      Boolean(color1) &&
      this.colorComponentEquality(color1.r, color.r) &&
      this.colorComponentEquality(color1.g, color.g) &&
      this.colorComponentEquality(color1.b, color.b)
    );
  }

  colorComponentEquality(component1: number, component2: number) {
    return Math.abs(component1 - component2) < 1e-2;
  }

  getRandom(id: string) {
    const value = this._randomCache.get(id);
    if (value === undefined) {
      const rnd = Math.random();
      this._randomCache.set(id, rnd);
      return rnd;
    }
    return value;
  }

  restoreMaterial = (m: MeshStandardMaterial, data: IMaterial) => {
    m.color = data.color;
    m.roughness = data.roughness;
    m.metalness = data.metalness;
    m.emissive = data.emissive;
    m.emissiveIntensity = data.emissiveIntensity;
    m.emissiveMap = data.emissiveMap;
  };

  override render(
    renderer: WebGLRenderer,
    writeBuffer: WebGLRenderTarget,
    readBuffer: WebGLRenderTarget,
    deltaTime: number,
    maskActive: boolean
  ) {
    renderer.getClearColor(this.oldClearColor);

    this.oldClearAlpha = renderer.getClearAlpha();

    const oldAutoClear = renderer.autoClear;

    renderer.autoClear = false;

    if (maskActive) renderer.state.buffers.stencil.setTest(false);
    renderer.setRenderTarget(this.renderTargetBuffer);
    renderer.setClearColor(0x000000, 0);
    renderer.clear();

    this.renderObjects(renderer);

    renderer.setRenderTarget(this.renderToScreen ? null : writeBuffer);
    renderer.clear(true, true, true);

    this.fsQuad.material = this.edgeDetectionMaterial;

    this.edgeDetectionMaterial.uniforms["rawTex"].value = readBuffer.texture;
    this.edgeDetectionMaterial.uniforms["tex"].value =
      this.renderTargetBuffer.texture;
    this.edgeDetectionMaterial.uniforms["texSize"].value.x =
      1 / Math.round(this.renderTargetBuffer.width / this.downSampleRatio);
    this.edgeDetectionMaterial.uniforms["texSize"].value.y =
      1 / Math.round(this.renderTargetBuffer.height / this.downSampleRatio);
    this.fsQuad.render(renderer);

    if (maskActive) renderer.state.buffers.stencil.setTest(true);
    renderer.setClearColor(this.oldClearColor);
    renderer.setClearAlpha(this.oldClearAlpha);
    renderer.autoClear = oldAutoClear;
    renderer.setRenderTarget(null);
  }
}

export { OutlinePass };
