import {
  deleteField,
  DocumentReference,
  serverTimestamp,
  updateDoc,
  query,
  where,
  orderBy,
  getDocs,
  doc,
  setDoc,
} from "firebase/firestore";

import { worldPlaySessionsCollection } from "@/utils/firestoreCollections";
import { DbWorldPlaySession } from "@nilo/firebase-schema";
import { isPreview, isProduction } from "@/config/environment";

import { trackSession } from "@/utils/tracking/eventUtils";

// TODO: Enable "build" and "play" modes when ready.
export type SessionMode = "sandbox"; // | "build" | "play";

interface SessionTrackingParams {
  userId: string;
  worldId: string;
  sessionId: string;
  mode?: SessionMode;
  durationSeconds?: number;
  startTime?: number;
}

/**
 * Starts a session tracking by capturing a session_start event
 * @param params - Session tracking parameters
 * @returns Session data containing sessionId and startTime
 */
export function startSessionTracking(params: SessionTrackingParams): void {
  const { userId, worldId, sessionId, mode = "sandbox", startTime } = params;

  // Track session start using centralized tracking
  trackSession("start", sessionId, {
    user_id: userId,
    world_id: worldId,
    mode,
    session_start_time: startTime,
  });
}

/**
 * Ends a session tracking by capturing a session_end event
 * @param params - Session tracking parameters
 */
export function endSessionTracking(params: SessionTrackingParams): void {
  const {
    userId,
    worldId,
    sessionId,
    mode = "sandbox",
    durationSeconds,
  } = params;

  // Track session end using centralized tracking
  trackSession("end", sessionId, {
    user_id: userId,
    world_id: worldId,
    mode,
    session_duration: durationSeconds,
  });
}

/**
 * Tracks a play session for analytics and Firestore persistence
 * Handles session start/end events and periodic updates
 *
 * @param userId - The authenticated user's ID
 * @param worldId - The world being played
 * @returns Cleanup function to end the session
 */
export function trackPlaySession({
  userId,
  worldId,
}: {
  userId: string | undefined;
  worldId: string | undefined;
}) {
  if (!userId || !worldId) {
    return () => {};
  }

  // Idle timeout in milliseconds (5 minutes: production, 20 seconds: staging, 10 seconds: development)
  const IDLE_TIMEOUT_MS = isProduction
    ? 5 * 60 * 1000
    : isPreview
      ? 20 * 1000
      : 10 * 1000;

  const UPDATE_INTERVAL_MS = isProduction ? 60000 : 15000;

  let currentSessionData: {
    ref: DocumentReference<DbWorldPlaySession, DbWorldPlaySession> | null;
    startedMs: number;
    idleStartedMs?: number;
    lastActiveMs?: number;
  } | null = null;

  let idleTimeoutId: number | null = null;

  const playTimeSeconds = (startedMs: number) => {
    return (performance.now() - startedMs) / 1000;
  };

  // Cleanup orphaned idle sessions from previous visits.
  // This is necessary because the session might have not been ended properly as
  // a result of a browser refresh or closing the tab on mobile devices.
  const cleanupOrphanedSessions = async () => {
    try {
      // Find sessions from this user/world that are older than idle timeout.
      // We could filter only sessions that are idle, but this wouldn't be as
      // reliable, because, again on mobile (Safari), when closing the browser
      // app, we won't get a visibilitychange event to mark the session as idle.
      const orphanedSessionsQuery = query(
        worldPlaySessionsCollection,
        where("userId", "==", userId),
        where("worldId", "==", worldId),
        orderBy("updatedAt", "desc")
      );

      const orphanedSessions = await getDocs(orphanedSessionsQuery);

      for (const doc of orphanedSessions.docs) {
        const sessionData = doc.data();

        // Do not take into account current session and sessions that have already ended
        if (doc.id === currentSessionData?.ref?.id || sessionData.endedAt) {
          continue;
        }

        // We use the client active timestamp to determine if the session is idle.
        // This is because the client active timestamp is updated on the client
        // side, so it is more accurate than the updatedAt timestamp from the server.
        const lastActiveAt = sessionData.clientActiveAt || 0;
        const timeSinceLastActive = Date.now() - lastActiveAt;

        // If the session has not received any client active event for longer
        // than the idle timeout, end it.
        if (timeSinceLastActive > IDLE_TIMEOUT_MS) {
          console.debug("🧹 Cleaning up orphaned idle session", {
            sessionId: doc.id,
            timeSinceLastActive,
          });

          endSessionTracking({
            sessionId: doc.id,
            userId,
            worldId,
            mode: "sandbox",
            // Override the duration with the play time seconds.
            // This is because the play time is the total effective time the
            // the session has been played for, not the total time the session
            // remained open, which might be longer than the play time due to
            // inactivity (e.g. closing the tab on mobile devices, or keep tabs
            // open for a long time but not in focus).
            durationSeconds: sessionData.playTimeSeconds,
          });

          await updateDoc(doc.ref, {
            updatedAt: serverTimestamp(),
            endedAt: serverTimestamp(),
            isIdle: false,
            idleStartAt: deleteField(),
            clientActiveAt: deleteField(),
          });
        }
      }
    } catch (error) {
      console.debug("Failed to cleanup orphaned sessions:", error);
    }
  };

  const handleStartSession = async () => {
    // First, cleanup any orphaned sessions from previous visits
    await cleanupOrphanedSessions();

    // Check if we have a session that might have missed idle events (mobile case)
    if (
      currentSessionData &&
      currentSessionData.lastActiveMs &&
      !currentSessionData.idleStartedMs
    ) {
      const timeSinceLastActive =
        performance.now() - currentSessionData.lastActiveMs;

      if (timeSinceLastActive > IDLE_TIMEOUT_MS) {
        // We've been away for longer than idle timeout but never got the idle event
        console.debug("⚠️ Detected missed idle event, ending stale session", {
          timeSinceLastActive,
        });
        await handleForceEndSession();
      }
    }

    // If we have an idle session, check if we're within the idle timeout
    if (currentSessionData && currentSessionData.idleStartedMs) {
      const idleTime = performance.now() - currentSessionData.idleStartedMs;

      if (idleTime < IDLE_TIMEOUT_MS) {
        // We're returning within the idle timeout, cancel the timeout and resume
        if (idleTimeoutId) {
          clearTimeout(idleTimeoutId);
          idleTimeoutId = null;
        }

        // Remove idle state and update last active
        delete currentSessionData.idleStartedMs;
        currentSessionData.lastActiveMs = performance.now();
        console.debug("▶️ Resuming session within idle timeout", {
          id: currentSessionData.ref?.id,
          idleTime,
        });

        // Update the database to mark session as active again
        if (currentSessionData.ref) {
          await updateDoc(currentSessionData.ref, {
            updatedAt: serverTimestamp(),
            isIdle: false,
            idleStartAt: deleteField(),
            playTimeSeconds: playTimeSeconds(currentSessionData.startedMs),
            clientActiveAt: Date.now(),
          });
        }

        // For Safari iOS, sometimes we need to be more aggressive
        // Force a user activity update to ensure we're tracking
        handleUserActivity();
        return;
      } else {
        // We've exceeded the idle timeout, end the old session before starting new
        console.debug("⏰ Idle timeout exceeded, ending old session", {
          idleTime,
        });
        await handleForceEndSession();
      }
    }

    // Don't start a new session if one is already active (and not idle)
    if (currentSessionData && !currentSessionData.idleStartedMs) {
      // Update last active time when trying to start an already active session
      currentSessionData.lastActiveMs = performance.now();
      return;
    }

    // NOTE: This is important to happen as soon as possible, so that we can
    // mark a session started while waiting for the DB document to be created.
    const now = performance.now();
    currentSessionData = {
      startedMs: now,
      lastActiveMs: now,
      ref: null,
    };

    // Create the session document
    const sessionId = `${userId}-${worldId}-${Date.now()}`;
    const sessionDocRefToCreate = doc(worldPlaySessionsCollection, sessionId);
    setDoc(sessionDocRefToCreate, {
      startedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      worldId,
      userId,
      playTimeSeconds: 0,
      isIdle: false,
    });
    currentSessionData.ref = sessionDocRefToCreate;

    startSessionTracking({
      sessionId,
      userId,
      worldId,
      mode: "sandbox",
      startTime: now,
    });

    console.debug("🟢 Session started", sessionId);
  };

  const handleForceEndSession = async () => {
    if (idleTimeoutId) {
      clearTimeout(idleTimeoutId);
      idleTimeoutId = null;
    }

    if (currentSessionData) {
      const { ref, startedMs } = currentSessionData;

      currentSessionData = null;

      if (ref) {
        endSessionTracking({
          sessionId: ref.id,
          userId,
          worldId,
          mode: "sandbox",
          durationSeconds: playTimeSeconds(startedMs),
        });
        await updateDoc(ref, {
          updatedAt: serverTimestamp(),
          endedAt: serverTimestamp(),
          playTimeSeconds: playTimeSeconds(startedMs),
          isIdle: false,
          idleStartAt: deleteField(),
          clientActiveAt: deleteField(),
        });
        console.debug("🔴 Session ended", ref.id);
      }
    }
  };

  const handleIdleSession = async () => {
    if (!currentSessionData || currentSessionData.idleStartedMs) {
      // No active session or already idle
      return;
    }

    // Mark session as idle
    currentSessionData.idleStartedMs = performance.now();
    console.debug("⏸️ Session marked as idle");

    // Update the database to mark session as idle
    if (currentSessionData.ref) {
      await updateDoc(currentSessionData.ref, {
        updatedAt: serverTimestamp(),
        isIdle: true,
        idleStartAt: serverTimestamp(),
        playTimeSeconds: playTimeSeconds(currentSessionData.startedMs),
        clientActiveAt: Date.now(),
      });
    }

    // Set timeout to actually end the session after idle period
    idleTimeoutId = window.setTimeout(() => {
      console.debug("⏱️ Idle timeout reached, ending session");
      handleForceEndSession();
    }, IDLE_TIMEOUT_MS);
  };

  const handleVisibilityChange = () => {
    if (document.visibilityState === "hidden") {
      handleIdleSession();
    } else if (document.visibilityState === "visible") {
      // On mobile, visibility change might be our only event
      // Force a check for stale sessions
      handleStartSession();
    }
  };

  // Additional handler for when page becomes visible (mobile Safari)
  const handlePageShow = (event: PageTransitionEvent) => {
    if (!event.persisted) {
      // Fresh page load, session will be started by initial call
      return;
    }
    // Page was restored from bfcache (back/forward cache)
    handleStartSession();
  };

  // Enhanced pagehide handler that might catch mobile browser closing
  const handlePageHide = (event: PageTransitionEvent) => {
    // If not persisted, the page is being unloaded (closing)
    if (!event.persisted) {
      handleForceEndSession();
    } else {
      // Page is being cached, just mark as idle
      handleIdleSession();
    }
  };

  // Track user activity to maintain accurate lastActiveMs (throttled)
  let lastActivityUpdate = 0;
  const handleUserActivity = () => {
    const now = performance.now();
    if (
      currentSessionData &&
      !currentSessionData.idleStartedMs &&
      now - lastActivityUpdate > 1000 // Throttle to once per second
    ) {
      currentSessionData.lastActiveMs = now;
      lastActivityUpdate = now;
    }

    if (!currentSessionData) {
      handleStartSession();
    }
  };

  /**
   * Set up multiple event listeners for reliable session tracking
   * - beforeunload: Traditional page unload event (force end)
   * - unload: Last resort for mobile browsers
   * - pagehide: More reliable in modern browsers, especially mobile (enhanced)
   * - pageshow: Handle page restoration from cache (mobile Safari)
   * - visibilitychange: Mark idle/resume sessions based on tab visibility
   * - webkitvisibilitychange: Safari-specific visibility
   * - focus/blur: Mark idle/resume sessions when browser gains/loses focus from OS
   * - freeze/resume: Handle page freezing on mobile when app is backgrounded
   * - user activity: Track actual user interactions
   */
  window.addEventListener("beforeunload", handleForceEndSession);
  window.addEventListener("unload", handleForceEndSession);
  window.addEventListener("pagehide", handlePageHide);
  window.addEventListener("pageshow", handlePageShow);
  window.addEventListener("visibilitychange", handleVisibilityChange);
  window.addEventListener("focus", handleStartSession);
  window.addEventListener("blur", handleIdleSession);

  // Mobile-specific events (types not yet in TypeScript lib)
  // Feature detection for Page Lifecycle API events
  const supportsFreeze = "onfreeze" in document;
  const supportsResume = "onresume" in document;

  if (supportsFreeze) {
    window.addEventListener("freeze", handleIdleSession);
  }
  if (supportsResume) {
    window.addEventListener("resume", handleStartSession);
  }

  // Track user activity
  window.addEventListener("mousemove", handleUserActivity);
  window.addEventListener("keydown", handleUserActivity);
  window.addEventListener("touchstart", handleUserActivity);
  window.addEventListener("touchend", handleUserActivity);
  window.addEventListener("click", handleUserActivity);
  window.addEventListener("scroll", handleUserActivity);

  const handleTimerTick = async () => {
    // Don't update if session is idle or doesn't exist
    if (
      currentSessionData &&
      currentSessionData.ref &&
      !currentSessionData.idleStartedMs
    ) {
      // Check if the session is still active
      if (
        currentSessionData.lastActiveMs &&
        Date.now() - currentSessionData.lastActiveMs > IDLE_TIMEOUT_MS
      ) {
        console.debug("🤙 No user activity for too long, ending session");
        handleForceEndSession();
        return;
      }

      await updateDoc(currentSessionData.ref, {
        updatedAt: serverTimestamp(),
        playTimeSeconds: playTimeSeconds(currentSessionData.startedMs),
        clientActiveAt: Date.now(),
      });
    }
  };
  const timerId = setInterval(handleTimerTick, UPDATE_INTERVAL_MS);

  // Start the initial session
  handleStartSession();

  return () => {
    window.removeEventListener("beforeunload", handleForceEndSession);
    window.removeEventListener("unload", handleForceEndSession);
    window.removeEventListener("pagehide", handlePageHide);
    window.removeEventListener("pageshow", handlePageShow);
    window.removeEventListener("visibilitychange", handleVisibilityChange);
    window.removeEventListener("focus", handleStartSession);
    window.removeEventListener("blur", handleIdleSession);

    // Only remove freeze/resume listeners if they were added
    if (supportsFreeze) {
      window.removeEventListener("freeze", handleIdleSession);
    }
    if (supportsResume) {
      window.removeEventListener("resume", handleStartSession);
    }
    window.removeEventListener("mousemove", handleUserActivity);
    window.removeEventListener("keydown", handleUserActivity);
    window.removeEventListener("touchstart", handleUserActivity);
    window.removeEventListener("touchend", handleUserActivity);
    window.removeEventListener("click", handleUserActivity);
    window.removeEventListener("scroll", handleUserActivity);
    clearInterval(timerId);
    handleForceEndSession();
  };
}
