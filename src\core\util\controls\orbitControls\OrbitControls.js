import {
  <PERSON>3,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  MOUS<PERSON>,
  Q<PERSON><PERSON><PERSON>,
  <PERSON>pherical,
  TOUCH,
  Vector2,
  <PERSON>ector3,
  <PERSON><PERSON>,
  Ray,
  MathUtils,
} from "three";
import { Client } from "../../../client";

// OrbitControls performs orbiting, dollying (zooming), and panning.
// Unlike TrackballControls, it maintains the "up" direction object.up (+Y by default).
//
//    Orbit - left mouse / touch: one-finger move
//    Zoom - middle mouse, or mousewheel / touch: two-finger spread or squish
//    Pan - right mouse, or left mouse + ctrl/meta, or arrow keys / touch: two-finger move

const _changeEvent = { type: "change" };
const _startEvent = { type: "start" };
const _moveEvent = { type: "move" };
const _endEvent = { type: "end" };
const _ray = new Ray();
const _plane = new Plane();
const TILT_LIMIT = Math.cos(70 * MathUtils.DEG2RAD);
const _intersections = [];

class OrbitControls extends EventDispatcher {
  constructor(object, domElement) {
    super();
    this.currentControlType =
      Client.playerControls.getActivePlayerControlsType();
    this.object = object;
    this.domElement = domElement;
    this.domElement.style.touchAction = "none"; // disable touch scroll

    // Set to false to disable this control
    this.enabled = true;

    // "target" sets the location of focus, where the object orbits around
    this.target = new Vector3();

    // Sets the 3D cursor (similar to Blender), from which the maxTargetRadius takes effect
    this.cursor = new Vector3();

    // How far you can dolly in and out ( PerspectiveCamera only )
    this.minDistance = 0;
    this.maxDistance = Infinity;

    // How far you can zoom in and out ( OrthographicCamera only )
    this.minZoom = 0;
    this.maxZoom = Infinity;

    // Limit camera target within a spherical area around the cursor
    this.minTargetRadius = 0;
    this.maxTargetRadius = Infinity;

    // How far you can orbit vertically, upper and lower limits.
    // Range is 0 to Math.PI radians.
    this.minPolarAngle = 0; // radians
    this.maxPolarAngle = Math.PI; // radians

    // How far you can orbit horizontally, upper and lower limits.
    // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )
    this.minAzimuthAngle = -Infinity; // radians
    this.maxAzimuthAngle = Infinity; // radians

    // Set to true to enable damping (inertia)
    // If damping is enabled, you must call controls.update() in your animation loop
    this.enableDamping = false;
    this.dampingFactor = 0.05;

    // This option actually enables dollying in and out; left as "zoom" for backwards compatibility.
    // Set to false to disable zooming
    this.enableZoom = true;
    this.zoomSpeed = 1.0;

    // Set to false to disable rotating
    this.enableRotate = true;
    this.rotateSpeed = 1.0;

    // Set to false to disable panning
    this.enablePan = true;
    this.panSpeed = 1.0;
    this.screenSpacePanning = true; // if false, pan orthogonal to world-space direction camera.up
    this.keyPanSpeed = 5.0; // pixels moved per arrow key push
    this.zoomToCursor = false;

    // Set to true to automatically rotate around the target
    // If auto-rotate is enabled, you must call controls.update() in your animation loop
    this.autoRotate = false;
    this.autoRotateSpeed = 2.0; // 30 seconds per orbit when fps is 60

    this.maxOrbitDistance = 25;

    // The four arrow keys
    this.keys = {
      LEFT: "ArrowLeft",
      FRONT: "ArrowUp",
      RIGHT: "ArrowRight",
      BACK: "ArrowDown",
    };

    this.pan = {
      LEFT: false,
      RIGHT: false,
      FRONT: false,
      BACK: false,
      TOP: false,
      BOTTOM: false,
    };

    this.rotate = {
      LEFT: false,
      RIGHT: false,
      TOP: false,
      BOTTOM: false,
    };

    // Mouse buttons
    this.mouseButtons = {
      LEFT: MOUSE.NONE,
      MIDDLE: MOUSE.PAN,
      RIGHT: MOUSE.ROTATE,
    };

    this.speedModifierActive = false;

    // Touch fingers
    this.touches = { ONE: TOUCH.ROTATE, TWO: TOUCH.DOLLY_PAN };

    // for reset
    this.target0 = this.target.clone();
    this.position0 = this.object.position.clone();
    this.zoom0 = this.object.zoom;

    // the target DOM element for key events
    this._domElementKeyEvents = null;

    //
    // public methods
    //

    this.getPolarAngle = function () {
      return spherical.phi;
    };

    this.getAzimuthalAngle = function () {
      return spherical.theta;
    };

    this.getDistance = function () {
      return this.object.position.distanceTo(this.target);
    };

    this.listenToKeyEvents = function (domElement) {
      domElement.addEventListener("keydown", onKeyDown);
      domElement.addEventListener("keyup", onKeyUp);
      this._domElementKeyEvents = domElement;
    };

    this.stopListenToKeyEvents = function () {
      this._domElementKeyEvents.removeEventListener("keydown", onKeyDown);
      this._domElementKeyEvents.removeEventListener("keyup", onKeyUp);
      this._domElementKeyEvents = null;
    };

    this.saveState = function () {
      scope.target0.copy(scope.target);
      scope.position0.copy(scope.object.position);
      scope.zoom0 = scope.object.zoom;
    };

    this.reset = function () {
      scope.target.copy(scope.target0);

      scope.object.position.copy(scope.position0);
      scope.object.zoom = scope.zoom0;

      scope.object.updateProjectionMatrix();
      scope.dispatchEvent(_changeEvent);

      scope.update();

      state = STATE.NONE;

      updateCursorStyle();
    };

    // this method is exposed, but perhaps it would be better if we can make it private...
    this.update = (function () {
      const offset = new Vector3();

      // so camera.up is the orbit axis
      const quat = new Quaternion().setFromUnitVectors(
        object.up,
        new Vector3(0, 1, 0)
      );
      const quatInverse = quat.clone().invert();

      const lastPosition = new Vector3();
      const lastQuaternion = new Quaternion();
      const lastTargetPosition = new Vector3();

      const twoPI = 2 * Math.PI;

      return function update(deltaTime = null) {
        const position = scope.object.position;

        const panSpeed = deltaTime * this.keyPanSpeed * getSpeedMultiplier();
        // const rotateSpeed = deltaTime * 0.1;
        if (
          Client.playerControls.getActivePlayerControlsType() &&
          !scope.currentControlType
        ) {
          scope.pan.TOP = false;
          scope.pan.BOTTOM = false;
          scope.pan.FRONT = false;
          scope.pan.BACK = false;
          scope.pan.LEFT = false;
          scope.pan.RIGHT = false;

          scope.rotate.TOP = false;
          scope.rotate.BOTTOM = false;
          scope.rotate.LEFT = false;
          scope.rotate.RIGHT = false;
        }

        if (
          scope.pan.FRONT ||
          scope.pan.BACK ||
          scope.pan.LEFT ||
          scope.pan.RIGHT ||
          scope.pan.TOP ||
          scope.pan.BOTTOM
        ) {
          setPivotToCameraPosition();
          pan(
            panSpeed * (scope.pan.LEFT - scope.pan.RIGHT),
            panSpeed * (scope.pan.BOTTOM - scope.pan.TOP),
            panSpeed * (scope.pan.BACK - scope.pan.FRONT)
          );
        }

        offset.copy(position).sub(scope.target);

        // Check if camera is at target position (distance near zero)
        const isAtTarget = offset.length() < 0.0001;

        // normal orbit behavior
        if (!isAtTarget) {
          // rotate offset to "y-axis-is-up" space
          offset.applyQuaternion(quat);

          // angle from z-axis around y-axis
          spherical.setFromVector3(offset);
        } else {
          // when camera is at target, skip orbit calculations and handle rotation directly below
          spherical.set(1, Math.PI / 2, 0);
        }

        if (scope.autoRotate && state === STATE.NONE) {
          rotateLeft(getAutoRotationAngle(deltaTime));
        }

        if (scope.enableDamping) {
          spherical.theta += sphericalDelta.theta * scope.dampingFactor;
          spherical.phi += sphericalDelta.phi * scope.dampingFactor;
        } else {
          spherical.theta += sphericalDelta.theta;
          spherical.phi += sphericalDelta.phi;
        }

        // restrict theta to be between desired limits

        let min = scope.minAzimuthAngle;
        let max = scope.maxAzimuthAngle;

        if (isFinite(min) && isFinite(max)) {
          if (min < -Math.PI) min += twoPI;
          else if (min > Math.PI) min -= twoPI;

          if (max < -Math.PI) max += twoPI;
          else if (max > Math.PI) max -= twoPI;

          if (min <= max) {
            spherical.theta = Math.max(min, Math.min(max, spherical.theta));
          } else {
            spherical.theta =
              spherical.theta > (min + max) / 2
                ? Math.max(min, spherical.theta)
                : Math.min(max, spherical.theta);
          }
        }

        // restrict phi to be between desired limits
        spherical.phi = Math.max(
          scope.minPolarAngle,
          Math.min(scope.maxPolarAngle, spherical.phi)
        );

        spherical.makeSafe();

        // move target to panned location

        if (scope.enableDamping === true) {
          scope.target.addScaledVector(panOffset, scope.dampingFactor);
        } else {
          scope.target.add(panOffset);
        }

        // Limit the target distance from the cursor to create a sphere around the center of interest
        scope.target.sub(scope.cursor);
        scope.target.clampLength(scope.minTargetRadius, scope.maxTargetRadius);
        scope.target.add(scope.cursor);

        let zoomChanged = false;
        // adjust the camera position based on zoom only if we're not zooming to the cursor or if it's an ortho camera
        // we adjust zoom later in these cases
        if (
          (scope.zoomToCursor && performCursorZoom) ||
          scope.object.isOrthographicCamera
        ) {
          spherical.radius = clampDistance(spherical.radius);
        } else {
          const prevRadius = spherical.radius;
          spherical.radius = clampDistance(spherical.radius * scale);
          zoomChanged = prevRadius != spherical.radius;
        }

        // normal orbit behavior
        if (!isAtTarget) {
          offset.setFromSpherical(spherical);

          // rotate offset back to "camera-up-vector-is-up" space
          offset.applyQuaternion(quatInverse);

          position.copy(scope.target).add(offset);

          scope.object.lookAt(scope.target);
        } else {
          // When camera is at target, handle first-person movement
          position.copy(scope.target);

          // Handle zoom as forward/backward movement in first-person mode
          if (scale !== 1) {
            const forward = new Vector3(0, 0, 1);
            forward.applyQuaternion(scope.object.quaternion);
            forward.normalize();

            // Calculate movement distance based on scale
            // Scale < 1 means zoom in (move forward), scale > 1 means zoom out (move backward)
            const moveDistance = (1 - scale) * scope.keyPanSpeed;
            const movement = forward.multiplyScalar(moveDistance);
            position.add(movement);
            scope.target.add(movement);
            zoomChanged = true;
          }

          // Apply rotation limits to first-person rotation deltas
          let constrainedThetaDelta = sphericalDelta.theta;
          let constrainedPhiDelta = sphericalDelta.phi;

          // Get current euler angles from quaternion to check limits
          const currentEuler = new Euler().setFromQuaternion(
            scope.object.quaternion,
            "YXZ"
          );
          const currentYaw = currentEuler.y;
          const currentPitch = currentEuler.x;

          // Apply azimuth (horizontal) limits
          if (
            isFinite(scope.minAzimuthAngle) &&
            isFinite(scope.maxAzimuthAngle)
          ) {
            const newYaw = currentYaw + constrainedThetaDelta;
            if (newYaw < scope.minAzimuthAngle) {
              constrainedThetaDelta = scope.minAzimuthAngle - currentYaw;
            } else if (newYaw > scope.maxAzimuthAngle) {
              constrainedThetaDelta = scope.maxAzimuthAngle - currentYaw;
            }
          }

          // Apply polar (vertical) limits
          const newPitch = currentPitch + constrainedPhiDelta;
          if (newPitch < scope.minPolarAngle - Math.PI / 2) {
            constrainedPhiDelta =
              scope.minPolarAngle - Math.PI / 2 - currentPitch;
          } else if (newPitch > scope.maxPolarAngle - Math.PI / 2) {
            constrainedPhiDelta =
              scope.maxPolarAngle - Math.PI / 2 - currentPitch;
          }

          // Apply rotation deltas as first-person rotations in world space
          const tempQuaternion = new Quaternion();

          // Horizontal rotation (theta) - yaw around world Y axis
          tempQuaternion.setFromAxisAngle(
            new Vector3(0, 1, 0),
            constrainedThetaDelta
          );
          scope.object.quaternion.premultiply(tempQuaternion);

          // Vertical rotation (phi) - pitch around local X axis
          tempQuaternion.setFromAxisAngle(
            new Vector3(1, 0, 0),
            constrainedPhiDelta
          );
          scope.object.quaternion.multiply(tempQuaternion);
        }

        if (scope.enableDamping === true) {
          sphericalDelta.theta *= 1 - scope.dampingFactor;
          sphericalDelta.phi *= 1 - scope.dampingFactor;

          panOffset.multiplyScalar(1 - scope.dampingFactor);
        } else {
          sphericalDelta.set(0, 0, 0);

          panOffset.set(0, 0, 0);
        }

        // adjust camera position
        if (scope.zoomToCursor && performCursorZoom) {
          let newRadius = null;
          if (scope.object.isPerspectiveCamera) {
            // move the camera down the pointer ray
            // this method avoids floating point error
            const prevRadius = offset.length();
            newRadius = clampDistance(prevRadius * scale);

            const radiusDelta = prevRadius - newRadius;
            scope.object.position.addScaledVector(dollyDirection, radiusDelta);
            scope.object.updateMatrixWorld();

            zoomChanged = !!radiusDelta;
          } else if (scope.object.isOrthographicCamera) {
            // adjust the ortho camera position based on zoom changes
            const mouseBefore = new Vector3(mouse.x, mouse.y, 0);
            mouseBefore.unproject(scope.object);

            const prevZoom = scope.object.zoom;
            scope.object.zoom = Math.max(
              scope.minZoom,
              Math.min(scope.maxZoom, scope.object.zoom / scale)
            );
            scope.object.updateProjectionMatrix();

            zoomChanged = prevZoom !== scope.object.zoom;

            const mouseAfter = new Vector3(mouse.x, mouse.y, 0);
            mouseAfter.unproject(scope.object);

            scope.object.position.sub(mouseAfter).add(mouseBefore);
            scope.object.updateMatrixWorld();

            newRadius = offset.length();
          } else {
            console.warn(
              "WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."
            );
            scope.zoomToCursor = false;
          }

          // handle the placement of the target
          if (newRadius !== null) {
            if (this.screenSpacePanning) {
              // position the orbit target in front of the new camera position
              scope.target
                .set(0, 0, -1)
                .transformDirection(scope.object.matrix)
                .multiplyScalar(newRadius)
                .add(scope.object.position);
            } else {
              // get the ray and translation plane to compute target
              _ray.origin.copy(scope.object.position);
              _ray.direction
                .set(0, 0, -1)
                .transformDirection(scope.object.matrix);

              // if the camera is 20 degrees above the horizon then don't adjust the focus target to avoid
              // extremely large values
              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {
                object.lookAt(scope.target);
              } else {
                _plane.setFromNormalAndCoplanarPoint(
                  scope.object.up,
                  scope.target
                );
                _ray.intersectPlane(_plane, scope.target);
              }
            }
          }
        } else if (scope.object.isOrthographicCamera) {
          const prevZoom = scope.object.zoom;
          scope.object.zoom = Math.max(
            scope.minZoom,
            Math.min(scope.maxZoom, scope.object.zoom / scale)
          );

          if (prevZoom !== scope.object.zoom) {
            scope.object.updateProjectionMatrix();
            zoomChanged = true;
          }
        }

        scale = 1;
        performCursorZoom = false;

        scope.currentControlType =
          Client.playerControls.getActivePlayerControlsType();

        // update condition is:
        // min(camera displacement, camera rotation in radians)^2 > EPS
        // using small-angle approximation cos(x/2) = 1 - x^2 / 8
        if (
          zoomChanged ||
          lastPosition.distanceToSquared(scope.object.position) > EPS ||
          8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS ||
          lastTargetPosition.distanceToSquared(scope.target) > EPS
        ) {
          scope.dispatchEvent(_changeEvent);

          lastPosition.copy(scope.object.position);
          lastQuaternion.copy(scope.object.quaternion);
          lastTargetPosition.copy(scope.target);

          return true;
        }

        return false;
      };
    })();

    this.dispose = function () {
      scope.domElement.removeEventListener("contextmenu", onContextMenu);

      scope.domElement.removeEventListener("pointerdown", onPointerDown);
      scope.domElement.removeEventListener("pointercancel", onPointerUp);
      scope.domElement.removeEventListener("wheel", onMouseWheel);

      scope.domElement.removeEventListener("pointermove", onPointerMove);
      scope.domElement.removeEventListener("pointerup", onPointerUp);

      const document = scope.domElement.getRootNode(); // offscreen canvas compatibility

      document.removeEventListener("keydown", interceptControlDown, {
        capture: true,
      });

      if (scope._domElementKeyEvents !== null) {
        scope._domElementKeyEvents.removeEventListener("keydown", onKeyDown);
        scope._domElementKeyEvents = null;
      }

      //scope.dispatchEvent( { type: 'dispose' } ); // should this be added here?
    };

    //
    // internals
    //

    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const scope = this;

    const STATE = {
      NONE: -1,
      ROTATE: 0,
      DOLLY: 1,
      PAN: 2,
      TOUCH_ROTATE: 3,
      TOUCH_PAN: 4,
      TOUCH_DOLLY_PAN: 5,
      TOUCH_DOLLY_ROTATE: 6,
    };

    let state = STATE.NONE;

    const EPS = 0.000001;

    // current position in spherical coordinates
    const spherical = new Spherical();
    const sphericalDelta = new Spherical();

    let scale = 1;
    const panOffset = new Vector3();

    const rotateStart = new Vector2();
    const rotateEnd = new Vector2();
    const rotateDelta = new Vector2();

    const panStart = new Vector2();
    const panEnd = new Vector2();
    const panDelta = new Vector2();

    const dollyStart = new Vector2();
    const dollyEnd = new Vector2();
    const dollyDelta = new Vector2();

    const dollyDirection = new Vector3();
    const mouse = new Vector2();
    let performCursorZoom = false;

    const pointers = [];
    const pointerPositions = {};

    let controlActive = false;

    function getSpeedMultiplier() {
      return scope.speedModifierActive ? 3.0 : 1.0;
    }

    function setPivotToCameraPosition() {
      scope.target.copy(scope.object.position);
    }

    function updateCursorStyle() {
      if (!scope.domElement) return;

      switch (state) {
        case STATE.ROTATE:
        case STATE.TOUCH_ROTATE:
          scope.domElement.style.cursor = "grab";
          break;
        case STATE.PAN:
        case STATE.TOUCH_PAN:
          scope.domElement.style.cursor = "all-scroll";
          break;
        case STATE.DOLLY:
          scope.domElement.style.cursor = "zoom-in";
          break;
        default:
          scope.domElement.style.cursor = "";
          break;
      }
    }

    function getAutoRotationAngle(deltaTime) {
      if (deltaTime !== null) {
        return ((2 * Math.PI) / 60) * scope.autoRotateSpeed * deltaTime;
      } else {
        return ((2 * Math.PI) / 60 / 60) * scope.autoRotateSpeed;
      }
    }

    function getZoomScale(delta) {
      const normalizedDelta = Math.abs(delta * 0.01);
      return Math.pow(0.95, scope.zoomSpeed * normalizedDelta);
    }

    function rotateLeft(angle) {
      sphericalDelta.theta -= angle;
    }

    function rotateUp(angle) {
      sphericalDelta.phi -= angle;
    }

    const panLeft = (function () {
      const v = new Vector3();

      return function panLeft(distance, objectMatrix) {
        v.setFromMatrixColumn(objectMatrix, 0); // get X column of objectMatrix
        v.multiplyScalar(-distance);

        panOffset.add(v);
      };
    })();

    const panUp = (function () {
      const v = new Vector3();

      return function panUp(distance, objectMatrix) {
        v.setFromMatrixColumn(objectMatrix, 1);
        v.multiplyScalar(distance);

        panOffset.add(v);
      };
    })();

    const panFront = (function () {
      const v = new Vector3();

      return function panFront(distance, objectMatrix) {
        if (scope.screenSpacePanning === true) {
          v.setFromMatrixColumn(objectMatrix, 2);
        }
        const l = v.length();

        v.normalize().multiplyScalar(distance * l);

        panOffset.add(v);
      };
    })();

    // deltaX and deltaY are in pixels; right and down are positive
    const pan = (function () {
      const offset = new Vector3();

      return function pan(deltaX, deltaY, deltaZ = 0) {
        const element = scope.domElement;

        if (scope.object.isPerspectiveCamera) {
          // perspective
          const position = scope.object.position;
          offset.copy(position).sub(scope.target);
          let targetDistance = offset.length();

          // If camera is at target position, use a default distance for panning
          if (targetDistance < 0.0001) {
            targetDistance = 1.0;

            // workaround: override target distance for mouse panning to be faster, this only happens when we don't have a target
            if (
              (state === STATE.PAN || state === STATE.TOUCH_PAN) &&
              !scope.pan.LEFT &&
              !scope.pan.RIGHT &&
              !scope.pan.TOP &&
              !scope.pan.BOTTOM &&
              !scope.pan.FRONT &&
              !scope.pan.BACK
            ) {
              targetDistance = 10;
            }
          }
          // half of the fov is center to top of screen
          targetDistance *= Math.tan(
            ((scope.object.fov / 2) * Math.PI) / 180.0
          );

          // we use only clientHeight here so aspect ratio does not distort speed
          panLeft(
            (2 * deltaX * targetDistance) / element.clientHeight,
            scope.object.matrix
          );
          panUp(
            (2 * deltaY * targetDistance) / element.clientHeight,
            scope.object.matrix
          );
          panFront(
            (2 * deltaZ * targetDistance) / element.clientHeight,
            scope.object.matrix
          );
        } else if (scope.object.isOrthographicCamera) {
          // orthographic
          panLeft(
            (deltaX * (scope.object.right - scope.object.left)) /
              scope.object.zoom /
              element.clientWidth,
            scope.object.matrix
          );
          panUp(
            (deltaY * (scope.object.top - scope.object.bottom)) /
              scope.object.zoom /
              element.clientHeight,
            scope.object.matrix
          );
        } else {
          // camera neither orthographic nor perspective
          console.warn(
            "WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."
          );
          scope.enablePan = false;
        }
      };
    })();

    function dollyOut(dollyScale) {
      if (
        scope.object.isPerspectiveCamera ||
        scope.object.isOrthographicCamera
      ) {
        scale /= dollyScale;
      } else {
        console.warn(
          "WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."
        );
        scope.enableZoom = false;
      }
    }

    function dollyIn(dollyScale) {
      if (
        scope.object.isPerspectiveCamera ||
        scope.object.isOrthographicCamera
      ) {
        scale *= dollyScale;
      } else {
        console.warn(
          "WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."
        );
        scope.enableZoom = false;
      }
    }

    function updateZoomParameters(x, y) {
      if (!scope.zoomToCursor) {
        return;
      }

      performCursorZoom = true;

      const rect = scope.domElement.getBoundingClientRect();
      const dx = x - rect.left;
      const dy = y - rect.top;
      const w = rect.width;
      const h = rect.height;

      mouse.x = (dx / w) * 2 - 1;
      mouse.y = -(dy / h) * 2 + 1;

      dollyDirection
        .set(mouse.x, mouse.y, 1)
        .unproject(scope.object)
        .sub(scope.object.position)
        .normalize();
    }

    function clampDistance(dist) {
      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist));
    }

    function raycastAndSetTarget() {
      // Check if currently selected object position is the same as target
      // jm todo: cache this to avoid needing to copy paste most of this code from UserEntity?
      const entities = Client.userEntity.getSelectedEntities();

      if (entities.length > 0) {
        const box = new Box3().setFromObject(entities[0].getRootNode());

        for (let i = 1; i < entities.length; i++) {
          box.expandByObject(entities[i].getRootNode());
        }

        if (box.isEmpty()) {
          return;
        }

        const center = new Vector3();
        box.getCenter(center);

        if (center.distanceTo(scope.target) < 0.001) {
          // Skip raycast if selected object is at the same position as target
          return;
        }
      }

      if (Client.raycastObjectAndGround(new Vector2(0, 0), _intersections)) {
        const intersect = _intersections[0];

        // Clamp the intersection point maximum distance from camera
        const cameraPos = scope.object.position;
        const intersectPoint = intersect.point;
        const direction = new Vector3().subVectors(intersectPoint, cameraPos);
        const distance = direction.length();

        if (distance > scope.maxOrbitDistance) {
          direction.normalize().multiplyScalar(scope.maxOrbitDistance);
          intersectPoint.copy(cameraPos).add(direction);
        }

        const offset = new Vector3();
        offset.copy(scope.object.position).sub(intersectPoint);
        scope.target.copy(intersectPoint);

        const quat = new Quaternion().setFromUnitVectors(
          scope.object.up,
          new Vector3(0, 1, 0)
        );
        offset.applyQuaternion(quat);
        spherical.setFromVector3(offset);
        scope.dispatchEvent(_changeEvent);
      }
    }

    //
    // event callbacks - update the object state
    //

    function handleMouseDownRotate(event) {
      rotateStart.set(event.clientX, event.clientY);
    }

    function handleMouseDownDolly(event) {
      updateZoomParameters(event.clientX, event.clientX);
      dollyStart.set(event.clientX, event.clientY);
    }

    function handleMouseDownPan(event) {
      panStart.set(event.clientX, event.clientY);
    }

    function handleMouseMoveRotate(event) {
      rotateEnd.set(event.clientX, event.clientY);

      rotateDelta
        .subVectors(rotateEnd, rotateStart)
        .multiplyScalar(scope.rotateSpeed);

      const element = scope.domElement;

      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight); // yes, height

      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);

      rotateStart.copy(rotateEnd);

      scope.update();
    }

    function handleMouseMoveDolly(event) {
      dollyEnd.set(event.clientX, event.clientY);

      dollyDelta.subVectors(dollyEnd, dollyStart);

      if (dollyDelta.y > 0) {
        dollyOut(getZoomScale(dollyDelta.y));
      } else if (dollyDelta.y < 0) {
        dollyIn(getZoomScale(dollyDelta.y));
      }

      dollyStart.copy(dollyEnd);

      scope.update();
    }

    function handleMouseMovePan(event) {
      panEnd.set(event.clientX, event.clientY);

      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);

      pan(panDelta.x, panDelta.y);

      panStart.copy(panEnd);

      scope.update();
    }

    function handleMouseWheel(event) {
      const panSpeed = scope.keyPanSpeed * getSpeedMultiplier();
      setPivotToCameraPosition();
      pan(0, 0, panSpeed * event.deltaY);
      raycastAndSetTarget();
    }

    function handleKeyDown(event) {
      let needsUpdate = false;

      if (
        scope.enabled &&
        !Client.playerControls.getActivePlayerControlsType() &&
        !event.ctrlKey &&
        !event.altKey &&
        !event.metaKey
      ) {
        switch (event.code) {
          case "MetaLeft":
          case "MetaRight":
          case "AltLeft":
          case "AltRight":
            //ControlLeft and ControlRight are removed because updating them updated selected entities
            // for pending multiselection which is not suitable and intended
            needsUpdate = true;
            break;

          case "ShiftLeft":
          case "ShiftRight":
            scope.speedModifierActive = true;
            break;

          case scope.keys.TOP:
            scope.pan.TOP = true;
            needsUpdate = true;
            scope.enableZoom = false;
            break;

          case scope.keys.BOTTOM:
            scope.pan.BOTTOM = true;
            needsUpdate = true;
            scope.enableZoom = false;
            break;

          case scope.keys.FRONT:
            scope.pan.FRONT = true;
            needsUpdate = true;
            scope.enableZoom = false;
            break;

          case scope.keys.BACK:
            scope.pan.BACK = true;
            needsUpdate = true;
            scope.enableZoom = false;
            break;

          case scope.keys.LEFT:
            scope.pan.LEFT = true;
            needsUpdate = true;
            scope.enableZoom = false;
            break;

          case scope.keys.RIGHT:
            scope.pan.RIGHT = true;
            needsUpdate = true;
            scope.enableZoom = false;
            break;
        }
      }

      if (needsUpdate) {
        // prevent the browser from scrolling on cursor keys
        event.preventDefault();
        scope.update();
      }
    }

    function handleKeyUp(event) {
      let needsUpdate = false;

      switch (event.code) {
        case "MetaLeft":
        case "MetaRight":
        case "AltLeft":
        case "AltRight":
          //ControlLeft and ControlRight are removed because updating them updated selected entities
          // for pending multiselection which is not suitable and intended
          needsUpdate = true;
          break;

        case "ShiftLeft":
        case "ShiftRight":
          scope.speedModifierActive = false;
          break;

        case scope.keys.TOP:
          scope.pan.TOP = false;
          scope.rotate.TOP = false;
          needsUpdate = true;
          break;

        case scope.keys.BOTTOM:
          scope.pan.BOTTOM = false;
          scope.rotate.BOTTOM = false;
          needsUpdate = true;
          break;

        case scope.keys.FRONT:
          scope.pan.FRONT = false;
          scope.rotate.FRONT = false;
          needsUpdate = true;
          break;

        case scope.keys.BACK:
          scope.pan.BACK = false;
          scope.rotate.BACK = false;
          needsUpdate = true;
          break;

        case scope.keys.LEFT:
          scope.pan.LEFT = false;
          scope.rotate.LEFT = false;
          needsUpdate = true;
          break;

        case scope.keys.RIGHT:
          scope.pan.RIGHT = false;
          scope.rotate.RIGHT = false;
          needsUpdate = true;
          break;
      }

      if (needsUpdate) {
        raycastAndSetTarget();

        // prevent the browser from scrolling on cursor keys
        event.preventDefault();
        scope.update();
      }
    }

    function handleTouchStartRotate(event) {
      if (pointers.length === 1) {
        rotateStart.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        rotateStart.set(x, y);
      }
    }

    function handleTouchStartPan(event) {
      if (pointers.length === 1) {
        panStart.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        panStart.set(x, y);
      }
    }

    function handleTouchStartDolly(event) {
      const position = getSecondPointerPosition(event);

      const dx = event.pageX - position.x;
      const dy = event.pageY - position.y;

      const distance = Math.sqrt(dx * dx + dy * dy);

      dollyStart.set(0, distance);
    }

    function handleTouchStartDollyPan(event) {
      if (scope.enableZoom) handleTouchStartDolly(event);

      if (scope.enablePan) handleTouchStartPan(event);
    }

    function handleTouchStartDollyRotate(event) {
      if (scope.enableZoom) handleTouchStartDolly(event);

      if (scope.enableRotate) handleTouchStartRotate(event);
    }

    function handleTouchMoveRotate(event) {
      if (pointers.length == 1) {
        rotateEnd.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        rotateEnd.set(x, y);
      }

      rotateDelta
        .subVectors(rotateEnd, rotateStart)
        .multiplyScalar(scope.rotateSpeed);

      const element = scope.domElement;

      rotateLeft((2 * Math.PI * rotateDelta.x) / element.clientHeight); // yes, height

      rotateUp((2 * Math.PI * rotateDelta.y) / element.clientHeight);

      rotateStart.copy(rotateEnd);
    }

    function handleTouchMovePan(event) {
      if (pointers.length === 1) {
        panEnd.set(event.pageX, event.pageY);
      } else {
        const position = getSecondPointerPosition(event);

        const x = 0.5 * (event.pageX + position.x);
        const y = 0.5 * (event.pageY + position.y);

        panEnd.set(x, y);
      }

      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);

      pan(panDelta.x, panDelta.y);

      panStart.copy(panEnd);
    }

    function handleTouchMoveDolly(event) {
      const position = getSecondPointerPosition(event);

      const dx = event.pageX - position.x;
      const dy = event.pageY - position.y;

      const distance = Math.sqrt(dx * dx + dy * dy);

      dollyEnd.set(0, distance);

      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed));

      dollyOut(dollyDelta.y);

      dollyStart.copy(dollyEnd);

      const centerX = (event.pageX + position.x) * 0.5;
      const centerY = (event.pageY + position.y) * 0.5;

      updateZoomParameters(centerX, centerY);
    }

    function handleTouchMoveDollyPan(event) {
      if (scope.enableZoom) handleTouchMoveDolly(event);

      if (scope.enablePan) handleTouchMovePan(event);
    }

    function handleTouchMoveDollyRotate(event) {
      if (scope.enableZoom) handleTouchMoveDolly(event);

      if (scope.enableRotate) handleTouchMoveRotate(event);
    }

    //
    // event handlers - FSM: listen for events and reset state
    //

    function onPointerDown(event) {
      if (scope.enabled === false) return;

      if (pointers.length === 0) {
        scope.domElement.setPointerCapture(event.pointerId);

        scope.domElement.addEventListener("pointermove", onPointerMove);
        scope.domElement.addEventListener("pointerup", onPointerUp);
      }

      //

      if (isTrackingPointer(event)) return;

      //

      addPointer(event);

      // Handle pen with button pressed as pan
      if (
        event.pointerType === "pen" &&
        (event.buttons === 2 || event.buttons === 32)
      ) {
        if (scope.enablePan === false) return;

        handleMouseDownPan(event);
        state = STATE.PAN;
        scope.dispatchEvent(_startEvent);
        updateCursorStyle();
      } else if (event.pointerType === "mouse") {
        onMouseDown(event);
      } else {
        onTouchStart(event);
      }
    }

    function onPointerMove(event) {
      if (scope.enabled === false) return;

      // Handle pen with button pressed as pan
      if (event.pointerType === "pen" && state === STATE.PAN) {
        handleMouseMovePan(event);
        scope.dispatchEvent(_moveEvent);
      } else if (event.pointerType === "mouse") {
        onMouseMove(event);
      } else {
        onTouchMove(event);
      }
    }

    function onPointerUp(event) {
      removePointer(event);

      switch (pointers.length) {
        case 0:
          scope.domElement.releasePointerCapture(event.pointerId);

          scope.domElement.removeEventListener("pointermove", onPointerMove);
          scope.domElement.removeEventListener("pointerup", onPointerUp);

          scope.dispatchEvent(_endEvent);

          state = STATE.NONE;

          break;

        case 1:
          {
            const pointerId = pointers[0];
            const position = pointerPositions[pointerId];

            // minimal placeholder event - allows state correction on pointer-up
            onTouchStart({
              pointerId: pointerId,
              pageX: position.x,
              pageY: position.y,
            });
          }
          break;
      }

      raycastAndSetTarget();
      updateCursorStyle();
    }

    function onMouseDown(event) {
      let mouseAction;

      switch (event.button) {
        case 0:
          mouseAction = scope.mouseButtons.LEFT;
          break;

        case 1:
          mouseAction = scope.mouseButtons.MIDDLE;
          break;

        case 2:
          mouseAction = scope.mouseButtons.RIGHT;
          break;

        default:
          mouseAction = -1;
      }
      switch (mouseAction) {
        case MOUSE.DOLLY:
          if (scope.enableZoom === false) return;

          handleMouseDownDolly(event);

          state = STATE.DOLLY;

          break;

        case MOUSE.ROTATE:
          if (event.ctrlKey || event.metaKey) {
            return;
            // if (scope.enablePan === false) return;

            // handleMouseDownPan(event);

            // state = STATE.PAN;
          } else {
            if (scope.enableRotate === false) return;

            handleMouseDownRotate(event);

            state = STATE.ROTATE;
          }

          break;

        case MOUSE.PAN:
          if (event.ctrlKey || event.metaKey) {
            if (scope.enableRotate === false) return;

            handleMouseDownRotate(event);

            state = STATE.ROTATE;
          } else {
            if (scope.enablePan === false) return;

            handleMouseDownPan(event);

            state = STATE.PAN;
          }

          break;

        default:
          state = STATE.NONE;
      }

      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent);
      }

      updateCursorStyle();
    }

    function onMouseMove(event) {
      switch (state) {
        case STATE.ROTATE:
          if (scope.enableRotate === false) return;

          handleMouseMoveRotate(event);

          break;

        case STATE.DOLLY:
          if (scope.enableZoom === false) return;

          handleMouseMoveDolly(event);

          break;

        case STATE.PAN:
          if (scope.enablePan === false) return;

          handleMouseMovePan(event);

          break;
      }

      if (state !== STATE.NONE) {
        scope.dispatchEvent(_moveEvent);
      }
    }

    function onMouseWheel(event) {
      if (scope.enabled === false) {
        return;
      }

      if (scope.enableZoom === false || state !== STATE.NONE) {
        changePanSpeed(event);
        return;
      }

      event.preventDefault();

      scope.dispatchEvent(_startEvent);

      handleMouseWheel(customWheelEvent(event));

      scope.dispatchEvent(_endEvent);
    }

    function customWheelEvent(event) {
      const mode = event.deltaMode;

      // minimal wheel event altered to meet delta-zoom demand
      const newEvent = {
        clientX: event.clientX,
        clientY: event.clientY,
        deltaY: event.deltaY,
      };

      switch (mode) {
        case 1: // LINE_MODE
          newEvent.deltaY *= 16;
          break;

        case 2: // PAGE_MODE
          newEvent.deltaY *= 100;
          break;
      }

      // detect if event was triggered by pinching
      if (event.ctrlKey && !controlActive) {
        newEvent.deltaY *= 10;
      }

      return newEvent;
    }

    function changePanSpeed(event) {
      const offset = new Vector3()
        .copy(scope.object.position)
        .sub(scope.target);
      const isAtTarget = offset.length() < 0.0001;

      if (isAtTarget) {
        scope.keyPanSpeed += event.deltaY < 0 ? 0.5 : -0.5;
        scope.keyPanSpeed = Math.min(Math.max(scope.keyPanSpeed, 0.2), 20);
        scope.dispatchEvent({
          type: "panSpeedChange",
          panSpeed: scope.keyPanSpeed,
          percentage: (scope.keyPanSpeed / 20) * 100,
        });
      }
    }

    function interceptControlDown(event) {
      if (event.key === "Control") {
        controlActive = true;

        const document = scope.domElement.getRootNode(); // offscreen canvas compatibility

        document.addEventListener("keyup", interceptControlUp, {
          passive: true,
          capture: true,
        });
      }
    }

    function interceptControlUp(event) {
      if (event.key === "Control") {
        controlActive = false;

        const document = scope.domElement.getRootNode(); // offscreen canvas compatibility

        document.removeEventListener("keyup", interceptControlUp, {
          passive: true,
          capture: true,
        });
      }
    }

    function onKeyDown(event) {
      if (scope.enabled === false || scope.enablePan === false) return;

      handleKeyDown(event);
    }

    function onKeyUp(event) {
      handleKeyUp(event);

      // Only re-enable zoom if no movement keys are still pressed
      const anyMovementKeyPressed =
        scope.pan.LEFT ||
        scope.pan.RIGHT ||
        scope.pan.FRONT ||
        scope.pan.BACK ||
        scope.pan.TOP ||
        scope.pan.BOTTOM;

      if (!anyMovementKeyPressed) {
        scope.enableZoom = true;
      }
    }

    function onTouchStart(event) {
      trackPointer(event);

      switch (pointers.length) {
        case 1:
          switch (scope.touches.ONE) {
            case TOUCH.ROTATE:
              if (scope.enableRotate === false) return;

              handleTouchStartRotate(event);

              state = STATE.TOUCH_ROTATE;

              break;

            case TOUCH.PAN:
              if (scope.enablePan === false) return;

              handleTouchStartPan(event);

              state = STATE.TOUCH_PAN;

              break;

            default:
              state = STATE.NONE;
          }

          break;

        case 2:
          switch (scope.touches.TWO) {
            case TOUCH.DOLLY_PAN:
              if (scope.enableZoom === false && scope.enablePan === false)
                return;

              handleTouchStartDollyPan(event);

              state = STATE.TOUCH_DOLLY_PAN;

              break;

            case TOUCH.DOLLY_ROTATE:
              if (scope.enableZoom === false && scope.enableRotate === false)
                return;

              handleTouchStartDollyRotate(event);

              state = STATE.TOUCH_DOLLY_ROTATE;

              break;

            default:
              state = STATE.NONE;
          }

          break;

        default:
          state = STATE.NONE;
      }

      if (state !== STATE.NONE) {
        scope.dispatchEvent(_startEvent);
      }

      updateCursorStyle();
    }

    function onTouchMove(event) {
      trackPointer(event);

      switch (state) {
        case STATE.TOUCH_ROTATE:
          if (scope.enableRotate === false) return;

          handleTouchMoveRotate(event);

          scope.update();

          break;

        case STATE.TOUCH_PAN:
          if (scope.enablePan === false) return;

          handleTouchMovePan(event);

          scope.update();

          break;

        case STATE.TOUCH_DOLLY_PAN:
          if (scope.enableZoom === false && scope.enablePan === false) return;

          handleTouchMoveDollyPan(event);

          scope.update();

          break;

        case STATE.TOUCH_DOLLY_ROTATE:
          if (scope.enableZoom === false && scope.enableRotate === false)
            return;

          handleTouchMoveDollyRotate(event);

          scope.update();

          break;

        default:
          state = STATE.NONE;
      }
    }

    function onContextMenu(event) {
      if (scope.enabled === false) return;

      event.preventDefault();
    }

    function addPointer(event) {
      pointers.push(event.pointerId);
    }

    function removePointer(event) {
      delete pointerPositions[event.pointerId];

      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) {
          pointers.splice(i, 1);
          return;
        }
      }
    }

    function isTrackingPointer(event) {
      for (let i = 0; i < pointers.length; i++) {
        if (pointers[i] == event.pointerId) return true;
      }

      return false;
    }

    function trackPointer(event) {
      let position = pointerPositions[event.pointerId];

      if (position === undefined) {
        position = new Vector2();
        pointerPositions[event.pointerId] = position;
      }

      position.set(event.pageX, event.pageY);
    }

    function getSecondPointerPosition(event) {
      const pointerId =
        event.pointerId === pointers[0] ? pointers[1] : pointers[0];

      return pointerPositions[pointerId];
    }

    //

    scope.domElement.addEventListener("contextmenu", onContextMenu);
    //TODO : return this later for only when we have not all data synced with remote
    // window.addEventListener(
    //   "beforeunload",
    //   (e) => {
    //     e.stopPropagation();
    //     e.preventDefault();
    //     return false;
    //   },
    //   true
    // );
    scope.domElement.addEventListener("pointerdown", onPointerDown);
    scope.domElement.addEventListener("pointercancel", onPointerUp);
    scope.domElement.addEventListener("wheel", onMouseWheel, {
      passive: false,
    });

    const document = scope.domElement.getRootNode(); // offscreen canvas compatibility

    document.addEventListener("keydown", interceptControlDown, {
      passive: true,
      capture: true,
    });

    // force an update at start

    this.update();
  }
}

export { OrbitControls };
