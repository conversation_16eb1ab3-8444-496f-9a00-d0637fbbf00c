import {
  addDoc,
  doc,
  <PERSON>Value,
  getDoc,
  onSnapshot,
  serverTimestamp,
} from "firebase/firestore";
import { modelGenerationTasksCollection } from "../firestoreCollections";
import { auth } from "@/config/firebase";
import {
  DbModelGenerationTask,
  ModelGenerationProvider,
  ModelGenerationProviderKeys,
  ModelGenerationTaskBody,
  ModelGenerationTaskId,
  WorldId,
} from "@nilo/firebase-schema";

export class ModelGenerationTaskError extends Error {
  suggestion: string;

  constructor(message: string, suggestion: string) {
    super(message);
    this.suggestion = suggestion;
  }
}

export interface ModelGenerationTaskResult {
  url: string;
  provider: ModelGenerationProvider;
  cacheKey: string;
  rigged?: boolean;
}

function extractModelGenerationTaskResult(
  data: DbModelGenerationTask
): ModelGenerationTaskResult | undefined {
  if (data.modelUrl === undefined || data.provider === undefined) {
    return undefined;
  }
  return {
    url: data.modelUrl,
    provider: data.provider,
    cacheKey: data.cacheKey,
    rigged: data.rigged,
  };
}

export async function runModelGenerationTask({
  taskBody,
  cacheKey,
  providerVersion,
  worldId,
}: {
  taskBody: ModelGenerationTaskBody;
  cacheKey: string;
  providerVersion?: ModelGenerationProviderKeys | null | undefined;
  worldId?: WorldId | undefined;
}): Promise<ModelGenerationTaskId> {
  const user = auth.currentUser;
  if (!user) {
    console.error("❌ Not authenticated");
    throw new ModelGenerationTaskError("Not authenticated", "Please sign in");
  }

  const taskDocument: Omit<DbModelGenerationTask, "createdAt"> & {
    createdAt: DbModelGenerationTask["createdAt"] | FieldValue;
  } = {
    userId: user.uid,
    taskBody,
    createdAt: serverTimestamp(),
    cacheKey: cacheKey ?? "",
  };
  if (providerVersion && providerVersion.length > 0) {
    taskDocument.requestedProviderVersion = providerVersion;
  }
  if (worldId) {
    taskDocument.worldId = worldId;
  }
  const ref = await addDoc(modelGenerationTasksCollection, taskDocument);
  console.debug("🚀 Model generation task created", ref.id);
  return ref.id;
}

export async function trackModelGenerationTask({
  onProgress,
  taskId,
}: {
  taskId: string;
  onProgress?: (progress: number, etaSeconds: number | undefined) => void;
}): Promise<ModelGenerationTaskResult> {
  const ref = doc(modelGenerationTasksCollection, taskId);
  const taskDoc = await getDoc(ref);
  const rawData = taskDoc.data();
  let taskResult: ModelGenerationTaskResult | undefined = undefined;
  let error: ModelGenerationTaskError | undefined = undefined;
  handleTaskProgress(
    taskId,
    rawData,
    onProgress,
    (err) => {
      error = err;
    },
    (result) => {
      taskResult = result;
    }
  );
  if (taskResult) {
    return taskResult;
  }
  if (error) {
    throw error;
  }

  return new Promise((resolve, reject) => {
    const unsubscribe = onSnapshot(
      ref,
      (doc) =>
        handleTaskProgress(
          taskId,
          doc.data(),
          onProgress,
          (err) => {
            unsubscribe();
            reject(err);
          },
          (result) => {
            unsubscribe();
            resolve(result);
          }
        ),
      reject
    );
  });
}

function handleTaskProgress(
  taskId: string,
  task: DbModelGenerationTask | undefined,
  onProgress:
    | ((progress: number, etaSeconds: number | undefined) => void)
    | undefined,
  onError: (error: ModelGenerationTaskError) => void,
  onSuccess: (taskResult: ModelGenerationTaskResult) => void
) {
  if (!task) {
    console.warn("❌ No task found", taskId);
    onError(
      new ModelGenerationTaskError(
        "No task found",
        "Programming error? Outdated API?"
      )
    );
    return;
  }

  // Check moderation status first
  if (task.moderation && task.moderation.status === "flagged") {
    console.error("❌ Task flagged by moderation:", task.moderation.flagReason);
    onError(
      new ModelGenerationTaskError(
        task.message ?? "Content not allowed",
        task.suggestion ??
          "Please modify your content to comply with our policy"
      )
    );
    return;
  }

  if (task.status === "failed" || task.status === "cancelled") {
    console.error("❌ Task failed:", task.message, task.suggestion);
    onError(
      new ModelGenerationTaskError(
        task.message ?? `Task ${task.status}`,
        task.suggestion ?? "Try again later"
      )
    );
  } else if (task.status === "success") {
    // success -> check if we have all the data we need AND moderation passed
    const taskResult = extractModelGenerationTaskResult(task);
    if (taskResult === undefined) {
      // still waiting for the upload to complete
      return;
    }

    // Check if moderation is still pending
    if (task.moderation?.status === "pending") {
      console.debug("⏳ Waiting for moderation to complete", taskId);
      return;
    }

    // Check if moderation flagged - treat as overall failure even if generation succeeded
    if (task.moderation && task.moderation.status === "flagged") {
      console.error(
        "❌ Generation succeeded but moderation flagged:",
        task.moderation.flagReason
      );
      onError(
        new ModelGenerationTaskError(
          "Content not allowed",
          "Please modify your content to comply with our policy"
        )
      );
      return;
    }

    // Both generation and moderation succeeded
    console.debug("✅ Task completed successfully", task);
    onSuccess(taskResult);
  } else if (task.progress !== undefined) {
    const eta = task.eta?.toDate().getTime();
    const requestedAt = task.requestedAt?.toDate().getTime();
    const etaSeconds =
      eta && requestedAt && eta > requestedAt
        ? (eta - requestedAt) / 1000
        : undefined;
    try {
      onProgress?.(task.progress, etaSeconds);
    } catch (error) {
      console.error("❌ Error calling onProgress:", error);
    }
  }
}
