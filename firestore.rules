rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    function isLoggedIn() {
      return request.auth != null;
    }
    function isUser(userId) {
      return isLoggedIn() && request.auth.uid == userId;
    }
    function isAdmin() {
      return isLoggedIn() && request.auth.token.admin == true;
    }

    match /MultiTabGuard/{userId} {
      // users can read and write their own multi-tab guard
      allow read, write: if isUser(userId);
    }

    match /Users/<USER>
      // users can read and write their own user data
      allow read: if isLoggedIn();
      allow write: if isUser(userId);

      match /LeakyBuckets/{resource=**} {
        // users can only read their own leaky buckets
        allow read: if isUser(userId);
      }
    }

    match /TripoTasks/{document=**} {
      allow read: if isLoggedIn();
      allow create: if isUser(request.resource.data.userId);
    }

    match /ModelGenerationTasks/{document=**} {
      allow read: if isLoggedIn();
      allow create: if isUser(request.resource.data.userId);
    }

    match /CompoundModelTasks/{document=**} {
      allow read: if isLoggedIn();
      allow create: if isUser(request.resource.data.userId);
    }

    match /WorldScores/{worldId} {
      allow read: if isLoggedIn();
    }

    match /Worlds/{worldId} {
      allow read, create, update: if isUser(resource.data.ownerId);
      allow delete: if isUser(resource.data.ownerId) && !resource.data.isPublic;
      allow read: if resource.data.isPublic == true;
    }

    match /DeletedWorlds/{worldId} {
      allow read, write: if isUser(resource.data.ownerId);
      allow create: if isUser(request.resource.data.ownerId);
    }

    function worldUserRelationDataMatchesId(data, id) {
      return (data.kind == "like" || data.kind == "star" || data.kind == "save") && data.kind + '-' + data.worldId + '-' + data.userId == id;
    }
    match /WorldUserRelations/{kindAndWorldIdAndUserId} {
      allow read: if isLoggedIn();
      allow create: if isUser(request.resource.data.userId) && worldUserRelationDataMatchesId(request.resource.data, kindAndWorldIdAndUserId);
      allow update, delete: if isUser(resource.data.userId) && worldUserRelationDataMatchesId(resource.data, kindAndWorldIdAndUserId);
    }

    match /WorldPlaySessions/{sessionId} {
      allow read: if isLoggedIn();
      allow create: if isUser(request.resource.data.userId);
      allow update, delete: if isUser(resource.data.userId);
    }

    match /Assets/{assetId} {
      allow read: if isLoggedIn();
      allow create: if isUser(request.resource.data.userId);
      allow update, delete: if isUser(resource.data.userId);
    }

    match /RoomData/{document=**} {
      // TODO: restrict access
      allow read, write: if request.auth != null;
    }

    match /WhitelistedEmails/{email} {
      allow read, write: if isAdmin();
    }

    match /{document=**} {
      // admins can read and write all documents
      allow read, write: if isAdmin();
    }
  }
}
