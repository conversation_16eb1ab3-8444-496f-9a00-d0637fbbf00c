import { Vector3, Quaternion } from "three";
import { rescueObjectsFromTheVoid } from "./helpers/rescueObjectsFromTheVoid";
import { addPhysicsBodyInfoToEntity } from "./helpers/addPhysicsBodyInfoToEntity";
import { Client } from "@/core/client";
import { globalDelegates } from "@/core/client/globalDelegates";
import { urlParams } from "@/utils/urlParams";

import {
  createPhysicsDebuggingService,
  type JoltPhysicsServiceWithWebworker,
  validatePhysicsOptions,
} from "@nilo/physics-with-jolt";
import { GameEntity, MeshEntity, PrimitiveEntity } from "@/core/entity";
import { AnimatorComponent, TriggerComponent } from "@/core/components";
import setPhysicsShape from "@/core/command/helpers/setPhysicsShape";
import { PhysicsBodyWithGeometry } from "@nilo/physics-with-jolt/src/types/Body";
import { RagdollSettings } from "@nilo/physics-with-jolt/src/types/Ragdoll";
import { PhysicsBodyInfoComponent } from "@/core/ecs/behaviors/physics";
import { CharacterPhysicsSettings } from "@nilo/physics-with-jolt/src/types/character";
import { BodyControlWrapper } from "@nilo/physics-with-jolt/src/webworker/wrappers/BodyControlWrapper";

import { CharacterControlWrapper } from "@nilo/physics-with-jolt/src/webworker/wrappers/CharacterControlWrapper";
import { RagdollControlWrapper } from "@nilo/physics-with-jolt/src/webworker/wrappers/RagdollControlWrapper";

export class PhysicsSimulation {
  private bodyIDToEntity = new Map<number, GameEntity>();
  private characterIDToControl = new Map<number, CharacterControlWrapper>();
  private service: JoltPhysicsServiceWithWebworker;
  private restartPhysicsTimeout: ReturnType<typeof setTimeout> | null = null;
  private _physicsOffset = new Vector3();
  private currentPhysicsStep = -1;
  constructor(service: JoltPhysicsServiceWithWebworker) {
    this.service = service;
  }

  public async syncWithPhysics(_deltaTime: number): Promise<void> {
    if (!this.service || !this.service.isPhysicsLoopRunning()) {
      return;
    }
    await rescueObjectsFromTheVoid(this.service);

    const position = new Vector3();
    const rotation = new Quaternion();
    const physicsStepInfo = await this.service.syncPhysicsState();
    if (this.currentPhysicsStep >= physicsStepInfo.stepNumber) {
      //Outdated physics step, skip it
      return;
    }
    this.currentPhysicsStep = physicsStepInfo.stepNumber;
    const dirtyBodies = physicsStepInfo.bodies;
    dirtyBodies.forEach((dirtyBody) => {
      const id = dirtyBody.id;
      if (!id) {
        return;
      }
      const entity = Client.getEntity(id);
      if (!entity) {
        return;
      }
      position.set(
        dirtyBody.position.x,
        dirtyBody.position.y,
        dirtyBody.position.z
      );
      rotation.set(
        dirtyBody.rotation.x,
        dirtyBody.rotation.y,
        dirtyBody.rotation.z,
        dirtyBody.rotation.w
      );
      entity.setPhysicsState(position, rotation, dirtyBody.dynamicProperties);
    });

    const dirtyCharacters = physicsStepInfo.characters;
    dirtyCharacters.forEach((dirtyCharacter) => {
      const id = dirtyCharacter.id;
      const characterControl = this.characterIDToControl.get(id);
      if (!characterControl) {
        return;
      }
      characterControl.fromJSON(dirtyCharacter);
    });
  }

  public async start(): Promise<JoltPhysicsServiceWithWebworker> {
    // Apply physics to scene objects
    // Only grid(ground for now)
    console.debug("🏀 PhysicsSimulation: Starting physics");
    this.currentPhysicsStep = -1;
    const grid = Client.grid;
    const initPromises = [];
    if (grid) {
      initPromises.push(grid.initPhysics(this.service));
    }

    Client.getAllEntities().forEach((entity) => {
      initPromises.push(this.addPhysicsToEntity(entity));
    });

    const objectsWithPhysicsCount = (
      await this.service.getObjectBodyControlPairs()
    ).length;
    console.debug(
      `🏀 PhysicsSimulation: Physics applied to ${objectsWithPhysicsCount} objects`
    );

    await Promise.allSettled(initPromises);
    this.service.startPhysicsLoop();

    if (urlParams.debugPhysics) {
      createPhysicsDebuggingService(this.service, Client.scene);
    }

    Object.assign(window, {
      physics: this.service,
      objs: this.service.getObjectBodyControlPairs(),
    });

    this.enablePhysicsPauseDuringInteraction();

    this.service.onContactsAdded.on((body1, body2) => {
      const entity1 = this.bodyIDToEntity.get(body1);
      const entity2 = this.bodyIDToEntity.get(body2);

      if (!entity1 && !entity2) return;

      const entity1IsTrigger =
        entity1?.data().hasComponent(TriggerComponent) ?? false;
      const entity2IsTrigger =
        entity2?.data().hasComponent(TriggerComponent) ?? false;

      if (entity1IsTrigger || entity2IsTrigger) {
        // This is a trigger interaction - emit OnTriggerEnter events
        entity1?.emitTriggerEnterEvent(entity2 || null);
        entity2?.emitTriggerEnterEvent(entity1 || null);
      } else {
        // This is a regular collision - emit OnCollision events
        entity1?.emitCollisionEvent(entity2 || null);
        entity2?.emitCollisionEvent(entity1 || null);
      }
    });

    this.service.onContactsRemoved.on((_body1, _body2) => {
      const entity1 = this.bodyIDToEntity.get(_body1);
      const entity2 = this.bodyIDToEntity.get(_body2);

      if (!entity1 && !entity2) return;

      // Check if either entity is a trigger (has TriggerComponent)
      const entity1IsTrigger =
        entity1?.data().hasComponent(TriggerComponent) ?? false;
      const entity2IsTrigger =
        entity2?.data().hasComponent(TriggerComponent) ?? false;

      if (entity1IsTrigger || entity2IsTrigger) {
        // This is a trigger interaction - emit OnTriggerExit events
        entity1?.emitTriggerExitEvent(entity2 || null);
        entity2?.emitTriggerExitEvent(entity1 || null);
      }
    });

    globalDelegates.addEntity.on((entity) => {
      this.addPhysicsToEntity(entity);
    });

    globalDelegates.removeEntity.on((entity) => {
      this.removePhysicsFromEntity(entity);
    });

    Client.events.on("entity-created", async (entity) => {
      if (!Client.composer.options.smartShapeOnCreate) {
        return;
      }
      if (entity instanceof MeshEntity || entity instanceof PrimitiveEntity) {
        const mesh = entity.getMesh();

        if (
          !mesh ||
          // if it is MeshEntity loaded from user model and
          // we apply shapeType from model userData we should not optimize/change it
          (entity instanceof MeshEntity &&
            Client.composer.options.eachMeshSeparateOnCreate &&
            Client.composer.options.checkModelCustomProperties &&
            entity.getPhysicsUserData()?.isUserImported &&
            mesh.userData?.physics?.shapeType)
        ) {
          return;
        }

        const physicsBodyInfo = entity
          .data()
          .getComponent(PhysicsBodyInfoComponent);
        if (!physicsBodyInfo) {
          return;
        }
        const bestShape = await Client.physics.findBestShape(
          physicsBodyInfo.geometry
        );
        const currentShape = entity.getPhysicsUserData()?.physics?.shapeType;
        if (!currentShape) {
          return;
        }
        if (bestShape !== currentShape) {
          await setPhysicsShape(entity, bestShape);
        }
      }
    });

    return this.service;
  }

  private enablePhysicsPauseDuringInteraction(): void {
    //// There are two approaches we may go with, in order to
    //// let the user freely interact with objects without the
    //// physics engine interfering. Uncomment only one.

    //// VARIANT ONE:
    globalDelegates.startInteractingWithTransformControls.on((objs) => {
      console.debug("🖱️ Start interacting", objs);
      this.service.stopPhysicsLoop();

      // EDIT: Cancel any pending restart
      if (this.restartPhysicsTimeout) {
        clearTimeout(this.restartPhysicsTimeout);
        this.restartPhysicsTimeout = null;
      }
    });

    globalDelegates.stopInteractingWithTransformControls.on((_objs) => {
      console.debug("✋ Stop interacting");
      const DEBOUNCE_TIME = 10;
      this.restartPhysicsTimeout = setTimeout(() => {
        console.debug("🔄 Restarting physics loop");
        this.currentPhysicsStep = -1;
        this.service.startPhysicsLoop();
        this.restartPhysicsTimeout = null;
      }, DEBOUNCE_TIME);
    });
  }

  private async addPhysicsObject(
    physicsBody: PhysicsBodyWithGeometry
  ): Promise<BodyControlWrapper | undefined> {
    const validationResult = validatePhysicsOptions(
      physicsBody.physicsOptions,
      true
    );
    if (!validationResult.isValid) {
      physicsBody.physicsOptions = validationResult.options;
    }
    const newBodyControl = await this.service.registerObjectForPhysics(
      physicsBody,
      physicsBody.geometry
    );

    if (!newBodyControl) {
      throw new Error("Error adding physics to object");
    }
    await newBodyControl.initialize();
    return newBodyControl;
  }

  public async addPhysicsToRagdoll(
    ragdollSettings: RagdollSettings
  ): Promise<RagdollControlWrapper | undefined> {
    const physics = Client.physics!;
    const ragdollControl =
      await physics.registerRagdollForPhysics(ragdollSettings);
    if (!ragdollControl) {
      return undefined;
    }
    const bodyControls = await ragdollControl.getBodyControls();
    for (const boneName in bodyControls) {
      const bodyIndex = bodyControls[boneName].bodyId ?? null;
      if (bodyIndex !== null) {
        //TODO:: Add body to map, so we can get the entity from the body index
        //this.bodyIDToEntity.set(bodyIndex, entity);
      }
    }
    return ragdollControl;
  }

  public async addPhysicsToEntity(entity: GameEntity): Promise<void> {
    {
      const bodyControl = entity.getPhysicsBodyControl();
      if (bodyControl) {
        const bodyIndex = await bodyControl.bodyId;
        if (bodyIndex !== -1) {
          if (this.bodyIDToEntity.has(bodyIndex)) {
            return;
          }
          this.bodyIDToEntity.set(bodyIndex, entity);
          return;
        }
      }
    }

    if (entity.data().hasComponent(AnimatorComponent)) {
      // No need to add physics to ragdoll. It already has a different physics body
      return;
    }

    if (!(entity instanceof MeshEntity || entity instanceof PrimitiveEntity)) {
      return;
    }
    if (entity.getPhysicsUserData()?.physics?.isDisabled) {
      return;
    }

    const physicsBody = addPhysicsBodyInfoToEntity(entity);
    if (!physicsBody) {
      // If it's an error, it should already be logged in addPhysicsBodyInfoToEntity
      return;
    }

    if (physicsBody.physicsOptions.shapeType === undefined) {
      throw new Error("Physics shape type is undefined");
    }

    const newBodyControl = await this.addPhysicsObject(physicsBody);
    if (!newBodyControl) {
      return;
    }
    const bodyIndex = newBodyControl.bodyId;
    if (bodyIndex !== -1) {
      this.bodyIDToEntity.set(bodyIndex, entity);
    } else {
      console.error("Error getting jolt body index", entity.id);
      return;
    }
  }

  public async addCharacterVirtualBody(
    entity: GameEntity,
    characterSettings: Partial<CharacterPhysicsSettings>,
    service: JoltPhysicsServiceWithWebworker
  ): Promise<CharacterControlWrapper | null> {
    const characterControl =
      await service.createCharacterPhysics(characterSettings);
    const bodyIndex = await characterControl.characterControlApi.getBodyID();
    this.characterIDToControl.set(bodyIndex, characterControl);
    if (bodyIndex !== -1) {
      this.bodyIDToEntity.set(bodyIndex, entity);
    } else {
      console.error("Error adding mapping from physics to entity", entity.id);
      return null;
    }
    return characterControl;
  }

  public async removeCharacterControl(
    characterControl: CharacterControlWrapper
  ): Promise<void> {
    this.characterIDToControl.delete(characterControl.id);
    return this.service.unregisterCharacterFromPhysics(characterControl);
  }

  public removePhysicsFromEntity(entity: GameEntity): void {
    const physicsBodyInfo = entity
      .data()
      .getComponent(PhysicsBodyInfoComponent);
    if (!physicsBodyInfo) {
      return;
    }

    const bodyControl = entity.getPhysicsBodyControl();
    if (bodyControl) {
      const bodyIndex = bodyControl.bodyId;
      if (bodyIndex !== -1) {
        this.bodyIDToEntity.delete(bodyIndex);
      }
    }
    this.service.unregisterObjectFromPhysics(physicsBodyInfo.id);
  }

  public getBodyIDToEntityMap(): Map<number, GameEntity> {
    return this.bodyIDToEntity;
  }
}

export async function addPhysicsToRagdoll(
  ragdollSettings: RagdollSettings
): Promise<RagdollControlWrapper | undefined> {
  if (!Client.physics) {
    throw new Error("Physics service not initialized");
  }
  return Client.physics.registerRagdollForPhysics(ragdollSettings);
}

export async function addPhysicsToEntity(entity: GameEntity): Promise<void> {
  return Client.physicsSimulation.addPhysicsToEntity(entity);
}

export function removePhysicsFromEntity(entity: GameEntity): void {
  return Client.physicsSimulation.removePhysicsFromEntity(entity);
}

export async function addCharacterVirtualBody(
  entity: GameEntity,
  characterSettings: Partial<CharacterPhysicsSettings>
): Promise<CharacterControlWrapper | null> {
  return Client.physicsSimulation.addCharacterVirtualBody(
    entity,
    characterSettings,
    Client.physics
  );
}

export async function removeCharacterControl(
  characterControl: CharacterControlWrapper
): Promise<void> {
  return Client.physicsSimulation.removeCharacterControl(characterControl);
}
