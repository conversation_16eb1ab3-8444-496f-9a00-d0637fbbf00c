import { useEffect, useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { User } from "firebase/auth";
import { auth } from "@/config/firebase";

function isInternalEmail(email: string | null | undefined) {
  if (!email) {
    return false;
  }
  return email.endsWith("@nilo.io") || email.endsWith("@nilo.xyz");
}

function isInternalUser(user: User) {
  return isInternalEmail(user.email) && user.emailVerified;
}

export const useIsAdmin = () => {
  const [user] = useAuthState(auth);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (user) {
      // TODO: remove this once everyone's tokens are updated
      if (isInternalUser(user)) {
        setIsAdmin(true);
        return;
      }
      user.getIdTokenResult().then((result) => {
        if (result.claims.admin) {
          setIsAdmin(true);
        }
      });
    } else {
      setIsAdmin(false);
    }
  }, [user]);

  return isAdmin;
};
