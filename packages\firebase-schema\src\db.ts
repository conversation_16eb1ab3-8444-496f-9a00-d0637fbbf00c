/**
 * Firestore DB types
 */

/* eslint-disable no-redeclare */
import { z } from "zod";
import { LeakyBucket } from "./leakyBucket";

type Timestamp =
  | InstanceType<(typeof import("firebase/firestore"))["Timestamp"]>
  | InstanceType<(typeof import("firebase-admin/firestore"))["Timestamp"]>;

const isTimestamp = (value: {
  seconds: number;
  nanoseconds: number;
  toDate: () => Date;
}): value is Timestamp => {
  return (
    typeof value.seconds === "number" &&
    typeof value.nanoseconds === "number" &&
    typeof value.toDate === "function"
  );
};
export const TimestampType = z.custom<Timestamp>((value) => {
  return isTimestamp(value);
});

export enum DbCollections {
  users = "Users",
  /** sub-collection of `users` */
  leakyBuckets = "LeakyBuckets",
  multiTabGuard = "MultiTabGuard",
  roomData = "RoomData", // TODO: add schema and fix current usage
  tripoTasks = "TripoTasks",
  modelGenerationTasks = "ModelGenerationTasks",
  compoundModelTasks = "CompoundModelTasks",
  worldScores = "WorldScores",
  worlds = "Worlds",
  deletedWorlds = "DeletedWorlds",
  worldUserRelations = "WorldUserRelations",
  worldPlaySessions = "WorldPlaySessions",
  assets = "Assets",
  internalDataJobs = "InternalDataJobs",
  /** sub-collection of `internalDataJobs` */
  internalSubJobs = "SubJobs",
  /** sub-collection of `internalDataJobs` and `internalSubJobs` */
  internalJobLogs = "Logs",
  whitelistedEmails = "WhitelistedEmails",
}

const InternalExtensionsFields = {
  internalCreatedByCloning: z
    .boolean()
    .optional()
    .describe("Whether the document was created by cloning"),
};
export const InternalExtensions = z.object(InternalExtensionsFields);
export type InternalExtensions = z.infer<typeof InternalExtensions>;

export const UserId = z.string().describe("Firebase auth provided user id");
export type UserId = z.infer<typeof UserId>;

export const WorldId = z.string().describe("id of the World document");
export type WorldId = z.infer<typeof WorldId>;

export const LiveblocksRoomId = z.string().describe("The liveblocks room id");
export type LiveblocksRoomId = z.infer<typeof LiveblocksRoomId>;

export const AssetId = z
  .string()
  .brand("AssetId")
  .describe("id of the asset document");
export type AssetId = z.infer<typeof AssetId>;

export const DbUser = z
  .object({
    updatedAt: TimestampType.optional(),
    username: z.string().optional(),
    displayName: z.string().optional(),
    avatarUrl: z.string().optional(),
    bio: z.string().optional(),
    // Signup tracking fields
    signupMethod: z.enum(["email", "google", "discord"]).optional(),
    referrerTag: z
      .string()
      .max(50)
      .regex(/^[a-zA-Z0-9_.-]+$/, "Invalid referrer tag format")
      .optional(),
  })
  .extend(InternalExtensionsFields);
export type DbUser = z.infer<typeof DbUser>;

export const DbLeakyBucket = LeakyBucket;
export type DbLeakyBucket = LeakyBucket;

export type DbMultiTabGuard = Record<`sessionId@${string}`, string>;

export const ModelGenerationTaskId = z
  .string()
  .describe("id of the ModelGenerationTask document");
export type ModelGenerationTaskId = z.infer<typeof ModelGenerationTaskId>;

export enum ModelGenerationTaskType {
  "text_to_model" = "text_to_model",
  "image_to_model" = "image_to_model",
  "animate_rig" = "animate_rig",
}

export const ModelGenerationTaskBody = z.discriminatedUnion("type", [
  z.object({
    type: z.literal(ModelGenerationTaskType.text_to_model),
    prompt: z.string(),
    negativePrompt: z.string().optional(),
  }),
  z.object({
    type: z.literal(ModelGenerationTaskType.image_to_model),
    imageUrl: z.string(),
    imageType: z.string().optional(),
  }),
  z.object({
    type: z.literal(ModelGenerationTaskType.animate_rig),
    taskId: ModelGenerationTaskId,
    outFormat: z.enum(["glb", "fbx"]).optional(),
  }),
]);
export type ModelGenerationTaskBody = z.infer<typeof ModelGenerationTaskBody>;

export enum ModelGenerationProviders {
  "Tripo" = "Tripo",
  "Meshy" = "Meshy",
  "Fal-Rodin" = "Fal-Rodin",
}

export const ModelGenerationProvider = z.object({
  name: z.nativeEnum(ModelGenerationProviders),
  version: z.string(),
});
export type ModelGenerationProvider = z.infer<typeof ModelGenerationProvider>;

export enum ModelGenerationProviderKeys {
  tripo1_4 = "tripo1_4",
  tripo2 = "tripo2",
  tripo2_5 = "tripo2_5",
  tripo3_0 = "tripo3_0",
  turbo1 = "turbo1",
  meshy4 = "meshy4",
  meshy5 = "meshy5",
  rodin1 = "rodin1",
}
export const ModelGenerationProviderKeysSchema = z.nativeEnum(
  ModelGenerationProviderKeys
);

export const ModelGenerationTaskStatus = z.enum([
  // Ongoing statuses
  "queued", // Task is waiting to be processed
  "running", // Task is currently in progress

  // Finalized statuses
  "success", // Task completed successfully
  "failed", // Task encountered an error
  "cancelled", // Task was deliberately stopped
  "unknown", // Task status cannot be determined
]);
export type ModelGenerationTaskStatus = z.infer<
  typeof ModelGenerationTaskStatus
>;

export const ModerationStatus = z.enum([
  "pending", // Moderation is in progress
  "passed", // Content passed moderation
  "flagged", // Content flagged as inappropriate
  "error", // Moderation system error occurred
]);
export type ModerationStatus = z.infer<typeof ModerationStatus>;

export const ModerationInfo = z.object({
  status: ModerationStatus,
  flagReason: z.string().optional().describe("Reason for moderation failure"),
  startedAt: TimestampType.optional(),
  completedAt: TimestampType.optional(),
});
export type ModerationInfo = z.infer<typeof ModerationInfo>;

export const DbModelGenerationTask = z
  .object({
    userId: UserId,
    provider: ModelGenerationProvider.optional().describe(
      "The actual provider used for the task"
    ),
    requestedProviderVersion:
      ModelGenerationProviderKeysSchema.optional().describe(
        "The provider and version requested by the user"
      ),
    taskBody: ModelGenerationTaskBody,
    cacheKey: z.string(),
    createdAt: TimestampType.optional(),
    requestedAt: TimestampType.optional(),
    message: z.string().optional(),
    suggestion: z.string().optional(),
    finishedAt: TimestampType.optional(),
    uploadedAt: TimestampType.optional(),
    status: ModelGenerationTaskStatus.optional(),
    progress: z.number().optional(),
    modelUrl: z.string().optional(),
    rigged: z.boolean().optional(),
    providerTaskId: z.string().optional().describe("Provider-specific task id"),
    providerCode: z
      .number()
      .optional()
      .describe("Provider-specific error code"),
    eta: TimestampType.optional().describe("Estimated time of arrival"),
    assets: z.array(AssetId).optional(),
    worldId: WorldId.optional().describe(
      "The world id of the world where the task originated"
    ),
    moderation: ModerationInfo.optional().describe(
      "Moderation status and information"
    ),
  })
  .extend(InternalExtensionsFields);
export type DbModelGenerationTask = z.infer<typeof DbModelGenerationTask>;

// Compound Model Task Schema
export const CompoundModelTaskId = z
  .string()
  .describe("id of the CompoundModelTask document");
export type CompoundModelTaskId = z.infer<typeof CompoundModelTaskId>;

export enum CompoundModelTaskType {
  "text_to_compound_model" = "text_to_compound_model",
}

export const CompoundModelTaskBody = z.discriminatedUnion("type", [
  z.object({
    type: z.literal(CompoundModelTaskType.text_to_compound_model),
    prompt: z.string(),
    maxShapes: z.number().optional().default(20),
    style: z
      .enum(["realistic", "abstract", "minimalist"])
      .optional()
      .default("realistic"),
    service: z
      .discriminatedUnion("provider", [
        z.object({
          provider: z.literal("google"),
          model: z.enum(["gemini-2.5-flash", "gemini-2.5-pro"]),
        }),
        z.object({
          provider: z.literal("openai"),
          model: z.enum([
            "gpt-4.1",
            "gpt-4o",
            "o4-mini",
            "gpt-5",
            "gpt-5-mini",
          ]),
        }),
        z.object({
          provider: z.literal("anthropic"),
          model: z.enum([
            "claude-4-opus-20250514",
            "claude-4-sonnet-20250514",
            "claude-3-5-haiku-20241022",
          ]),
        }),
        z.object({
          provider: z.literal("groq"),
          model: z.enum([
            "moonshotai/kimi-k2-instruct",
            "meta-llama/llama-4-scout-17b-16e-instruct",
            "gemma2-9b-it",
            "deepseek-r1-distill-llama-70b",
            "deepseek-r1-distill-qwen-32b",
          ]),
        }),
        z.object({
          provider: z.literal("cerebras"),
          model: z.enum(["llama-4-scout-17b-16e-instruct", "llama-3.3-70b"]),
        }),
        z.object({
          provider: z.literal("togetherai"),
          model: z.enum([
            "moonshotai/Kimi-K2-Instruct",
            "deepseek-ai/DeepSeek-V3",
          ]),
        }),
      ])
      .optional()
      .describe("AI service provider and model used for generation"),
  }),
]);
export type CompoundModelTaskBody = z.infer<typeof CompoundModelTaskBody>;

export type CompoundModelTaskService = NonNullable<
  z.infer<typeof CompoundModelTaskBody>["service"]
>;

export const PrimitiveShapeSpec = z.object({
  id: z.string().describe("Unique identifier for this shape"),
  primitiveType: z.enum([
    "Cube",
    "Sphere",
    "Cylinder",
    "Cone",
    "Plane",
    "Pipe",
    "Torus",
    "TorusKnot",
    "Ring",
    "Capsule",
    "Wedge",
    "Octahedron",
    "Tetrahedron",
  ]),
  position: z
    .object({ x: z.number(), y: z.number(), z: z.number() })
    .optional(),
  rotation: z
    .object({ x: z.number(), y: z.number(), z: z.number() })
    .optional(),
  scale: z.object({ x: z.number(), y: z.number(), z: z.number() }).optional(),
  color: z.string().optional().describe("Hex color string (e.g., '0xFF0000')"),
  tags: z
    .array(z.string())
    .optional()
    .describe("Tags for grouping and identification"),
  parentId: z.string().optional().describe("Parent shape ID for hierarchy"),
  modifiers: z
    .array(
      z.object({
        type: z.enum([
          "Twist",
          "Taper",
          "Bend",
          "Array",
          "Wave",
          "Stretch",
          "Shear",
          "Noise",
        ]),
        params: z.record(z.number()),
      })
    )
    .optional(),
  physics: z
    .object({
      isStatic: z.boolean().optional(),
      motionType: z.enum(["dynamic", "kinematic"]).optional(),
      shapeType: z
        .enum(["boundingBox", "convexHull", "sphere", "cylinder", "plane"])
        .optional(),
      restitution: z.number().optional(),
      friction: z.number().optional(),
      isDisabled: z.boolean().optional(),
      isSensor: z.boolean().optional(),
      gravityFactor: z.number().optional(),
    })
    .optional(),
});
export type PrimitiveShapeSpec = z.infer<typeof PrimitiveShapeSpec>;

export const CompoundModelTaskStatus = z.enum([
  // Ongoing statuses
  "queued", // Task is waiting to be processed
  "running", // Task is currently generating compound model
  "streaming", // Task is actively streaming shapes

  // Finalized statuses
  "success", // Task completed successfully
  "failed", // Task encountered an error
  "cancelled", // Task was deliberately stopped
]);
export type CompoundModelTaskStatus = z.infer<typeof CompoundModelTaskStatus>;

export const DbCompoundModelTask = z.object({
  userId: UserId,
  taskBody: CompoundModelTaskBody,
  cacheKey: z.string(),
  createdAt: TimestampType.optional(),
  requestedAt: TimestampType.optional(),
  message: z.string().optional(),
  suggestion: z.string().optional(),
  finishedAt: TimestampType.optional(),
  status: CompoundModelTaskStatus.optional(),
  progress: z.number().optional().describe("Progress from 0 to 100"),
  totalShapes: z
    .number()
    .optional()
    .describe("Total number of shapes to generate"),
  completedShapes: z.number().optional().describe("Number of shapes completed"),
  shapes: z
    .array(PrimitiveShapeSpec)
    .optional()
    .describe("Array of generated shapes"),
  currentShapeIndex: z
    .number()
    .optional()
    .describe("Index of currently generating shape"),
  modelTitle: z.string().optional().describe("Generated title for the model"),
  modelDescription: z
    .string()
    .optional()
    .describe("Generated description for the model"),
  generationTimeMs: z
    .number()
    .optional()
    .describe("Time taken to generate the model in milliseconds"),
  worldId: WorldId.optional().describe(
    "The world id of the world where the task originated"
  ),
});
export type DbCompoundModelTask = z.infer<typeof DbCompoundModelTask>;

// internal score for each world, key is worldId
export const DbWorldScore = z.object({
  isPublic: z.boolean(),
  updatedAt: TimestampType,
  score: z.number(),
  likes: z.number(),
  totalPlayTimeSeconds: z.number(),
});
export type DbWorldScore = z.infer<typeof DbWorldScore>;

export const DbWorld = z
  .object({
    createdAt: TimestampType,
    updatedAt: TimestampType,
    name: z.string(),
    ownerId: UserId.describe("The user id of the owner of the world"),
    isPublic: z.boolean(),
    isFeatured: z.boolean().optional(),
    liveblocksRoomId: LiveblocksRoomId.describe(
      "The liveblocks room id backing this world"
    ),
    fromWorldId: WorldId.optional().describe(
      "The world id of the world that was cloned to create this world"
    ),
    displayName: z.string().optional(),
  })
  .extend(InternalExtensionsFields);
export type DbWorld = z.infer<typeof DbWorld>;

export const DbDeletedWorld = DbWorld;
export type DbDeletedWorld = z.infer<typeof DbDeletedWorld>;

// key is userId and worldId, path: `WorldUserRelations/{kind}-{worldId}-{userId}`
export const DbWorldUserRelation = z.object({
  createdAt: TimestampType,
  kind: z.enum(["like", "star", "save"]), // TODO: add more kinds (remove unused ones)
  worldId: WorldId,
  userId: UserId,
});
export type DbWorldUserRelation = z.infer<typeof DbWorldUserRelation>;

// key is sessionId, path: `WorldPlaySessions/{sessionId}`
export const DbWorldPlaySession = z.object({
  worldId: WorldId,
  userId: UserId,
  startedAt: TimestampType,
  updatedAt: TimestampType,
  endedAt: TimestampType.optional(),
  playTimeSeconds: z
    .number()
    .describe("The total effective play time of the session"),
  isIdle: z
    .boolean()
    .optional()
    .describe("Whether the session is currently in idle state"),
  idleStartAt: TimestampType.optional().describe(
    "Timestamp when the session became idle"
  ),
  clientActiveAt: z
    .number()
    .optional()
    .describe("The last client active timestamp of the session"),
});
export type DbWorldPlaySession = z.infer<typeof DbWorldPlaySession>;

export const AssetUserUploadProvider = z.literal("UserUpload");
export type AssetUserUploadProvider = z.infer<typeof AssetUserUploadProvider>;

export const Model3dAssetType = z.literal("3d-model");
export type Model3dAssetType = z.infer<typeof Model3dAssetType>;

export const Model3dAsset = z.object({
  type: Model3dAssetType,
  rigged: z.boolean(),
  provider: z.union([ModelGenerationProvider, AssetUserUploadProvider]),
  taskId: ModelGenerationTaskId.optional(),
  url: z.string().url().optional(),
  cacheKey: z.string().optional(), // TODO: remove once we have a proper lookup
});
export type Model3dAsset = z.infer<typeof Model3dAsset>;

export const AnimationAssetType = z.literal("animation");
export type AnimationAssetType = z.infer<typeof AnimationAssetType>;

export const AnimationSkeletonType = z.enum([
  "Humanoid",
  "Quadruped",
  "Custom",
]);
export type AnimationSkeletonType = z.infer<typeof AnimationSkeletonType>;

export const AnimationProvider = z.enum(["Mixamo", "Uthana"]);
export type AnimationProvider = z.infer<typeof AnimationProvider>;

export const AnimationAsset = z.object({
  type: AnimationAssetType,
  provider: z.union([AnimationProvider, AssetUserUploadProvider]),
  skeletonType: AnimationSkeletonType,
  url: z.string().url().optional(),
});
export type AnimationAsset = z.infer<typeof AnimationAsset>;

export const DbAsset = z.intersection(
  z
    .object({
      createdAt: TimestampType,
      updatedAt: TimestampType,
      userId: UserId,
      name: z.string(),
      isDeleted: z.boolean(),
      status: z.enum(["pending", "processing", "completed", "failed"]),
    })
    .extend(InternalExtensionsFields),
  z.discriminatedUnion("type", [Model3dAsset, AnimationAsset])
);
export type DbAsset = z.infer<typeof DbAsset>;

export const DbInternalDatabaseId = z
  .string()
  .nullish()
  .describe(
    "The id of the database (null or undefined or '(default)' for main database)"
  );
export type DbInternalDatabaseId = z.infer<typeof DbInternalDatabaseId>;

export const DbInternalDataJob = z.object({
  createdAt: TimestampType,
  updatedAt: TimestampType,
  userId: UserId,
  status: z.enum(["pending", "processing", "completed", "failed"]),
  message: z.string().optional(),
  subJobs: z.number().optional(),
  completedSubJobs: z.number().optional(),
  failedSubJobs: z.number().optional(),
  config: z.discriminatedUnion("type", [
    z.object({
      type: z.literal("noop"),
      durationMs: z.number().optional(),
    }),
    z.object({
      type: z.literal("clone_worlds"),
      worldIds: z.array(WorldId),
      fromDatabase: DbInternalDatabaseId,
      overwrite: z.boolean(),
    }),
    z.object({
      type: z.literal("clone_all_worlds"),
      fromDatabase: DbInternalDatabaseId,
      overwrite: z.boolean(),
      skipOwnedByAnonymousUsers: z.boolean(),
    }),
    z.object({
      type: z.literal("clone_all_users"),
      fromDatabase: DbInternalDatabaseId,
      skipAnonymousUsers: z.boolean(),
    }),
    z.object({
      type: z.literal("migrate"),
      script: z.enum(["nuke_worlds_tasks_assets"]),
    }),
  ]),
});
export type DbInternalDataJob = z.infer<typeof DbInternalDataJob>;

export type DbInternalDataJobMigrateScript = Extract<
  DbInternalDataJob["config"],
  { type: "migrate" }
>["script"];

export const DbInternalDataJobLog = z.object({
  createdAt: TimestampType,
  level: z.enum(["info", "warn", "error"]),
  message: z.string(),
});
export type DbInternalDataJobLog = z.infer<typeof DbInternalDataJobLog>;

export const DbWhitelistedEmails = z.object({
  allow: z.boolean(),
});
export type DbWhitelistedEmails = z.infer<typeof DbWhitelistedEmails>;
