import { Matrix4 } from "three";
import { convertThreeJSGeometryToBodyGeometry } from "../../../packages/physics-with-jolt/src/debug/bodyToJoltBody";
import { calculatePhysicsBodyOffsetMatrix } from "./calculatePhysicsBodyOffsetMatrix";
import {
  PhysicsBodyInfoComponent,
  PhysicsUserDataComponent,
} from "@/core/ecs/behaviors/physics";
import { MeshEntity } from "@/core/entity/MeshEntity";
import { PrimitiveEntity } from "@/core/entity/PrimitiveEntity";
import { assertTrue } from "@/core/util/Assert";
import { PhysicsBodyWithGeometry } from "@nilo/physics-with-jolt/src/types/Body";
import { Matrix4Array } from "@nilo/physics-with-jolt/src/types/PhysicsOptions";
import {
  PhysicsError,
  PhysicsInvalidGeometryError,
} from "@nilo/physics-with-jolt/src/types/errors";
import { assertValidGeometry } from "@nilo/physics-with-jolt/src/util/isValidGeometry";

export function addPhysicsBodyInfoToEntity(
  entity: MeshEntity | PrimitiveEntity
): PhysicsBodyWithGeometry | undefined {
  let physicsBodyInfo: PhysicsBodyWithGeometry | undefined;
  try {
    if (entity instanceof MeshEntity) {
      if (entity.getIsGenerating()) {
        return undefined;
      }
      physicsBodyInfo = createFromMesh(entity);
    } else if (entity instanceof PrimitiveEntity) {
      physicsBodyInfo = createFromPrimitive(entity);
    }
    if (physicsBodyInfo) {
      entity.data().addComponent(PhysicsBodyInfoComponent, physicsBodyInfo);
    }
    return physicsBodyInfo;
  } catch (error) {
    if (error instanceof PhysicsError) {
      //Expected error because mesh is invalid for physics
      console.error(
        error.message,
        entity instanceof MeshEntity
          ? "MeshEntity: - " + entity.getModelData()?.url
          : "Primitive: - " + entity.getPrimitiveType()
      );
      return undefined;
    }

    console.warn(
      `Error adding physics body info to entity: ${entity.id}`,
      error
    );
    //Throw unexpected error
    throw error;
  }
}

function createFromMesh(meshEntity: MeshEntity) {
  const mesh = meshEntity.getMesh();
  if (!mesh) {
    throw new Error(`Mesh not found for entity: ${meshEntity.id}`);
  }
  const meshGeometry = mesh.geometry;
  if (!meshGeometry) {
    throw new PhysicsInvalidGeometryError(
      `Geometry not found for mesh: entityId ${meshEntity.id}`
    );
  }

  const geometry = convertThreeJSGeometryToBodyGeometry(
    meshGeometry,
    meshEntity.getModelData()?.url || meshEntity.id
  );
  assertValidGeometry(geometry);
  const physicsUserData = meshEntity
    .data()
    .getComponent(PhysicsUserDataComponent);
  if (!physicsUserData || !physicsUserData.physics) {
    throw new Error(
      `Physics user data not found for mesh: entityId ${meshEntity.id}`
    );
  }

  mesh.updateMatrixWorld();

  return {
    id: meshEntity.id,
    geometry: geometry,
    physicsOptions: physicsUserData.physics,
    matrix: mesh.matrixWorld.toArray() as Matrix4Array,
    offsetMatrix: calculatePhysicsBodyOffsetMatrix(
      meshEntity
    ).toArray() as Matrix4Array,
  } as PhysicsBodyWithGeometry;
}

function createFromPrimitive(
  primitive: PrimitiveEntity
): PhysicsBodyWithGeometry {
  assertTrue(
    primitive.isInWorld,
    "Primitive entity must be fully spawned before creating mesh body"
  );

  const mesh = primitive.getMesh();
  if (!mesh) {
    throw new Error(`Mesh not found for primitive: ${primitive.id}`);
  }
  const geometry = mesh.geometry;
  if (!geometry) {
    throw new Error(`Geometry not found for mesh: ${mesh.id}`);
  }
  const vertices = geometry.getAttribute("position")?.array;
  const indices =
    geometry.getAttribute("index")?.array || geometry.index?.array;

  const physicsUserData = primitive
    .data()
    .getComponent(PhysicsUserDataComponent);
  if (!physicsUserData || !physicsUserData.physics) {
    throw new Error(`Physics user data not found for mesh: ${mesh.id}`);
  }

  const parent = mesh.parent;
  let matrixWorld, offsetMatrix;

  if (!parent) {
    //Mesh should always have a parent, but just in case. If it has no parent, we don't have an offsetMatrix
    matrixWorld = mesh.matrixWorld;
    offsetMatrix = new Matrix4();
  } else {
    matrixWorld = parent.matrixWorld;
    offsetMatrix = calculatePhysicsBodyOffsetMatrix(primitive);
  }

  const physicsBody = {
    id: primitive.id,
    geometry: {
      geometryId: primitive.getPrimitiveType(),
      vertices: new Float32Array(vertices).buffer,
      indices: new Uint32Array(indices).buffer,
    },
    physicsOptions: physicsUserData.physics,
    matrix: matrixWorld.toArray() as Matrix4Array,
    offsetMatrix: offsetMatrix.toArray() as Matrix4Array,
  } as PhysicsBodyWithGeometry;
  return physicsBody;
}
