import { DbCollections } from "@nilo/firebase-schema";
import { isolatedEnvironment } from "../isolatedEnv";
import { Logger } from "../logger";
import { captureEvent } from "../posthog";
import { isEmulator } from "../utils";
import { onDocumentCreated } from "./utils";

/**
 * Server-side validation for referrer tag to ensure security
 */
function validateReferrerTag(tag: string | undefined): string | undefined {
  if (!tag) return undefined;

  // Additional server-side validation
  const validFormat = /^[a-zA-Z0-9_.-]+$/.test(tag);
  const validLength = tag.length <= 50;

  if (validFormat && validLength) {
    return tag;
  }

  // Log security concern - will use logger in actual function context
  return undefined;
}

/**
 * Triggered when a new user document is created in Firestore.
 * This happens after user signup when the frontend creates the user profile.
 */
export const onUserDocumentCreated = onDocumentCreated(
  `${DbCollections.users}/{userId}`,
  async (event) => {
    const logger = Logger.create({ userId: event.params.userId });
    const userId = event.params.userId;
    const userData = event.data?.data();

    if (!userData) {
      return;
    }

    // Extract signup method and referrer tag from user data with validation
    const method = userData.signupMethod || "email";
    const rawReferrerTag = userData.referrerTag;
    const referrer_tag = validateReferrerTag(rawReferrerTag);

    // Log if referrer tag was rejected for security reasons
    if (rawReferrerTag && !referrer_tag) {
      logger.warn("🔒 Invalid referrer tag rejected", {
        userId,
        rejectedTag: rawReferrerTag,
      });
    }

    logger.info("👋 User signed up", {
      userId,
      method,
      referrer_tag,
    });

    // Only capture PostHog events in production (non-emulator) and from main environment
    const isProduction =
      !isEmulator &&
      process.env.NODE_ENV === "production" &&
      (isolatedEnvironment.name === "main" ||
        isolatedEnvironment.name === "prod");

    if (isProduction) {
      logger.debug("📊 Capturing PostHog event", {
        userId,
        method,
        referrer_tag,
      });

      // Capture the user_signed_up event
      captureEvent(userId, "user_signed_up", {
        user_id: userId,
        method,
        referrer_tag,
        timestamp: new Date().toISOString(),
      });
    } else {
      logger.debug("🚫 Skipping PostHog event capture", {
        production: isProduction,
        environment: isolatedEnvironment.name,
      });
    }
  }
);
