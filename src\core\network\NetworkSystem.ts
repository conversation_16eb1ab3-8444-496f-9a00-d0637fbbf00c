import EventEmitter from "eventemitter3";
import { UserEntity } from "../entity";
import { globalDelegates } from "../client/globalDelegates";
import { Client } from "../client";
import { makeNetworkSystemRunner } from "./systems";
import { NetworkClientService } from "./NetworkClientService";
import { DeserializeService } from "./DeserializeService";
import { urlParams } from "@/utils/urlParams";
import {
  CertificateHash,
  ChannelStats,
  ChannelType,
  Client as NetworkClient,
  ClientStats,
  ConnectionCloseInfo,
  ProviderType,
  WebSocketClient,
  WebTransportClient,
  MessageSchema,
} from "@nilo/network";
import {
  EnsureWorldServerIsRunningRequest,
  EnsureWorldServerIsRunningResponse,
  WorldId,
} from "@nilo/firebase-schema";
import { bindFunction } from "@/config/firebase";
import { isolatedEnvironment } from "@/config/isolatedEnv";
import { SystemRun<PERSON>, <PERSON> } from "@nilo/ecs";
import { getFeatureFlag } from "@/utils/feature-flags";

const parseHash = (hash: string) =>
  Uint8Array.from(
    hash
      .toString()
      .split(":")
      .map((i) => parseInt(i, 16))
  );

const forceWebsocket = !!urlParams.forceWs;
export const networkEnabled = getFeatureFlag("useServerNetworking");
const hostname =
  window.location.hostname === "localhost"
    ? "127.0.0.1"
    : window.location.hostname;
const devServerUrlOverride = process.env.USE_SERVER_URL
  ? process.env.USE_SERVER_URL.replace("__HOSTNAME__", hostname)
  : undefined;

// picking a server fleet based on the isolated environment name
const baseServerConfig: Partial<EnsureWorldServerIsRunningRequest> = {
  serverFleet: isolatedEnvironment.fleet,
};

const ensureWorldServerIsRunning = bindFunction<
  EnsureWorldServerIsRunningRequest,
  EnsureWorldServerIsRunningResponse
>("ensureWorldServerIsRunning");

interface NetworkEvents {
  connected: (worldId: WorldId, serverUrl: URL) => void;
  close: (info: ConnectionCloseInfo) => void;
}

export class NetworkSystem extends EventEmitter<NetworkEvents> {
  private client: NetworkClient | null = null;
  private _isConnected: boolean = false;
  private worldId: WorldId | null = null;

  public static UPDATES_PER_SECOND = 60;

  constructor(private _userEntity: UserEntity) {
    super();

    globalDelegates.roomJoin.on((roomId, worldId) => {
      if (!worldId) {
        console.warn("🔌 Room joined but no world id provided", roomId);
        return;
      }
      this.connectToWorld(worldId);
    });

    globalDelegates.roomLeave.on((roomId, worldId) => {
      if (!worldId) {
        console.warn("🔌 Room left but no world id provided", roomId);
        return;
      }
      if (this.worldId === worldId) {
        this.close();
      }
    });

    this.updateLoop = this.updateLoop.bind(this);
    setInterval(this.updateLoop, 1000 / NetworkSystem.UPDATES_PER_SECOND);
  }

  public get isEnabled(): boolean {
    return networkEnabled;
  }

  public get isConnected(): boolean {
    return this._isConnected;
  }

  public getStats():
    | {
        provider: ProviderType;
        clientStats: ClientStats;
        channelStats: { type: ChannelType; stats: ChannelStats }[];
      }
    | undefined {
    if (!this.client) {
      return undefined;
    }
    return {
      provider: this.client.provider,
      clientStats: this.client.stats,
      channelStats: Object.values(this.client.channels).map((channel) => ({
        type: channel.type,
        stats: channel.stats,
      })),
    };
  }

  private async connectToWorld(worldId: WorldId) {
    if (!networkEnabled) {
      console.warn("🔌 Tried to connect but network is disabled");
      return;
    }

    if (this.worldId === worldId) {
      return;
    }

    this.worldId = worldId;
    const result = await ensureWorldServerIsRunning({
      ...baseServerConfig,
      ...(devServerUrlOverride ? { devServerUrlOverride } : {}),
      worldId,
    });
    if (this.worldId !== worldId) {
      console.warn(
        `🔌 Got server allocation (for ${worldId}) but already connecting to another world (${this.worldId})`
      );
      return;
    }
    console.debug("🔌 Got server allocation", result.data);
    // double check if the server fleet is the same as the one we requested (might be different inside ISE that hasn't been deployed yet)
    if (result.data.serverFleet !== baseServerConfig.serverFleet) {
      if (
        !process.env.VERCEL_GIT_PULL_REQUEST_ID &&
        isolatedEnvironment.name !== "main"
      ) {
        console.warn(
          "🔌 Server fleet mismatch: No pull request detected for non-main isolated environment.",
          result.data.serverFleet,
          baseServerConfig.serverFleet
        );
      } else {
        console.error(
          "🔌 Server fleet mismatch",
          result.data.serverFleet,
          baseServerConfig.serverFleet
        );
      }
    }
    await this.connect(worldId, result.data.serverUrl);
  }

  private async connect(worldId: WorldId, serverUrl: string | URL) {
    serverUrl = new URL(serverUrl);

    let hashes: CertificateHash[] | undefined;
    if (WebTransportClient.isSupported() && !forceWebsocket) {
      console.debug("🔌 Creating WebTransport client");
      // note: we use "http"/"https" to distinguish between secure/unsecure (not the protocol)
      if (serverUrl.protocol === "http:") {
        console.debug("🔌 Fetching fingerprint for WebTransport");
        const fingerprintJson = await fetch(
          `${serverUrl.origin}/fingerprint.json`
        );
        const fingerprintJsonData = await fingerprintJson.json();
        console.debug("🔌 Fingerprint for WebTransport", fingerprintJsonData);
        hashes = [
          {
            algorithm: "sha-256",
            value: parseHash(fingerprintJsonData.fingerprint),
          },
        ];
      }
      // check if the world id has changed while we were connecting
      if (this.worldId !== worldId) {
        console.warn(
          `🔌 Target world changed while connecting to ${worldId} (already connecting to ${this.worldId})`
        );
        return;
      }
      // close the old client if it exists
      if (this.client) {
        console.warn("🔌 Closing old network client");
        this.client.close();
      }
      this.client = new WebTransportClient({
        hashes,
        host: serverUrl.hostname,
        port: Number(serverUrl.port),
      });
    } else {
      console.debug("🔌 Creating WebSocket client");
      // check if the world id has changed while we were connecting
      if (this.worldId !== worldId) {
        console.warn(
          `🔌 Target world changed while connecting to ${worldId} (already connecting to ${this.worldId})`
        );
        return;
      }
      // close the old client if it exists
      if (this.client) {
        console.warn("🔌 Closing old network client");
        this.client.close();
      }
      this.client = new WebSocketClient({
        host: serverUrl.hostname,
        port: Number(serverUrl.port),
        // note: we use "http"/"https" to distinguish between secure/unsecure (not the protocol)
        secure: serverUrl.protocol === "https:",
      });
    }

    console.debug("🔌 Waiting for network client to be ready");
    await this.client.ready;
    // check if the world id has changed while we were connecting
    if (this.worldId !== worldId) {
      console.warn(
        `🔌 Target world changed while connecting to ${worldId} (already connected to ${this.worldId})`
      );
      return;
    }
    console.debug("🔌 Network client is ready");
    this.handleIncoming();
    this.emit("connected", worldId, serverUrl);
    this._isConnected = true;

    // initialize the world's networking state
    // TODO: the client should dynamically provide fresh ECS worlds on join
    this.joinEcsWorld(Client.world, this.client);
  }

  private handleIncoming() {
    if (!this.client) {
      return;
    }

    this.client.on("close", () => {
      console.error("Network client closed");
      // Handle reconnect or whatever
    });
  }

  private close() {
    if (!networkEnabled) {
      console.warn("🔌 Tried to close but network is disabled");
      return;
    }

    if (this.client) {
      this.client.close();
      this.client = null;
    }

    this.worldId = null;
    this._world = undefined;
    this._systems = undefined;
  }

  private _world?: World;
  private _systems?: SystemRunner;
  public joinEcsWorld(world: World, client: NetworkClient) {
    // set the current world
    this._world = world;

    // initialize ECS systems
    this._systems = makeNetworkSystemRunner();
    this._systems.init(world);
    world.registerSystemRunner(this._systems);

    // initialize services
    world.addService(new DeserializeService(world));
    const nc = world.addService(new NetworkClientService(world, client));

    // sync user entity asap
    this._userEntity.netShouldUpdate = true;

    // join world
    // TODO: ideally, the server would assign each user's entity ID.
    nc.send(MessageSchema.UserJoin, this._userEntity.id);
  }

  private _lastTime = 0;

  private updateLoop() {
    if (!networkEnabled) {
      return;
    }

    const now = performance.now();
    const dt = now - this._lastTime;
    this._lastTime = now;

    if (this._world && this._systems) {
      this._systems.run(this._world, dt);
    }
  }
}
