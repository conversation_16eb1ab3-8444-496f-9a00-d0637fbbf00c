import {
  Object3D,
  <PERSON>,
  Group,
  Vector3,
  Vector2,
  <PERSON>uatern<PERSON>,
  Intersection,
  Ray,
  Matrix4,
} from "three";
import {
  isTransformControlsActive,
  TransformControlsCreateOptions,
} from "../transformControlsCreate/TransformControlsCreate";
import { ControlPointIndex } from "../duplicateControls/DuplicateControlsControlPointsCollection";
import { DuplicateControls } from "../DuplicateControls";
import { isMobile } from "../../UserAgent";
import { ExtendedRaycaster } from "../../RaycastUtils";
import { isMultiSelectionActive } from "../dragSelect/DragSelectControls";
import { checkFalse } from "../../Check";
import { AxisArrow } from "./AxisArrow";

import { AxisScaler } from "./AxisScaler";

import { AxisRotator } from "./AxisRotator";

import { SphereScaler } from "./SphereScaler";
import { PlaneSlider } from "./PlaneSlider";
import { GameEntity } from "@/core/entity";
import { Client } from "@/core/client";

// export enum GIZMOTYPE {
//   "TRANSLATE" = "TRANSLATE",
//   "ROTATE" = "ROTATE",
//   "SCALE" = "SCALE",
// }

//@ts-expect-error dispatchEvent type
const _changeEvent: CustomEvent = { type: "change" };
//@ts-expect-error dispatchEvent type
const _mouseDownEvent: CustomEvent = { type: "mouseDown" };
//@ts-expect-error dispatchEvent type
const _mouseUpEvent: CustomEvent = { type: "mouseUp" };
//@ts-expect-error dispatchEvent type
const _objectChangeEvent: CustomEvent = { type: "objectChange" };

export enum GIZMOSPACE {
  "LOCAL" = "local",
  "GLOBAL" = "world",
}

// const bSphere = new Sphere();

const center = new Vector3();

const q0 = new Quaternion();

const p = new Vector3();

const q = new Quaternion();

const s = new Vector3();

export type onPointerDownProp = {
  i: Intersection;
};

export type onPointerMoveProp = {
  ray: Ray;
};

export type onDragStartPropType = {
  component: "Arrow" | "Slider" | "AxisRotator" | "Scale" | "None";

  axis: 0 | 1 | 2;

  origin: Vector3;

  directions: Vector3[];
};

export type OnDragStartType = (
  data: onDragStartPropType,
  space: GIZMOSPACE
) => void;

export type OnDragType = (
  _local: Matrix4,

  _deltaLocal: Matrix4,

  _world: Matrix4,

  _deltaWorld: Matrix4
) => void;

export interface IHelper {
  pointerDownInfo: onDragStartPropType;

  onHover(hover: boolean): void;

  onPointerDown(data: onPointerDownProp): void;

  onPointerMove(data: onPointerMoveProp): Matrix4;

  onPointerUp(): void;
}

export default class TransformControls extends Object3D {
  raycaster: ExtendedRaycaster;

  camera: Camera;

  domElement: HTMLCanvasElement;

  objectsMap = {} as { [key: string]: Object3D & IHelper };

  entities: GameEntity[] = [];
  objects: Object3D[] = [];

  content: Group;
  contentInner: Group;

  hoverObject: Object3D | null = null;

  activeObject: (Object3D & IHelper) | null = null;

  autoTransform: boolean;

  onPointerDownMatrixWorld = new Matrix4();

  onPointerDownMatrixLocal = new Matrix4();

  translationGizmo: Group;

  rotationGizmo: Group;

  localScaleGizmo: Group;

  globalScaleGizmo: Group;

  space: GIZMOSPACE;
  options: TransformControlsCreateOptions;
  parentObject: Object3D;

  // gizmoTypeTest: GIZMOTYPE;

  onDragStart?: OnDragStartType;

  onDrag?: OnDragType;

  onDragEnd?: () => void;

  constructor(
    camera: Camera,

    domElement: HTMLCanvasElement,

    raycaster: ExtendedRaycaster,

    parent: Object3D,

    autoTransform: boolean,

    options: TransformControlsCreateOptions & {
      duplicateControls?: DuplicateControls;
      i?: ControlPointIndex;
    } = {
      translateAllowed: true,
      translateXAllowed: true,
      translateYAllowed: true,
      translateZAllowed: true,
      rotateAllowed: true,
      rotateXAllowed: true,
      rotateYAllowed: true,
      rotateZAllowed: true,
      scaleAllowed: true,
      isMobileDevice: isMobile(),
      updateEntitiesOnDraggingChanged: true,
    },

    onDragStart?: OnDragStartType,

    onDrag?: OnDragType,

    onDragEnd?: () => void
  ) {
    super();
    parent.add(this);
    this.onDragStart = onDragStart;
    this.onDrag = onDrag;
    this.onDragEnd = onDragEnd;

    this.raycaster = raycaster;

    this.camera = camera;

    this.domElement = domElement;

    this.parentObject = parent;

    this.autoTransform = autoTransform;

    this.space = GIZMOSPACE.LOCAL;

    this.options = options;
    this.translationGizmo = new Group();
    if (this.options.translateAllowed) {
      if (this.options.translateYAllowed && this.options.translateZAllowed) {
        const planeSlider = new PlaneSlider(
          this.objectsMap,
          new Vector3(0, 1, 0),
          new Vector3(0, 0, 1),
          0,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.translationGizmo.add(planeSlider);
      }
      if (this.options.translateXAllowed && this.options.translateZAllowed) {
        const planeSlider = new PlaneSlider(
          this.objectsMap,
          new Vector3(0, 0, 1),
          new Vector3(1, 0, 0),
          1,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.translationGizmo.add(planeSlider);
      }
      if (this.options.translateXAllowed && this.options.translateYAllowed) {
        const planeSlider = new PlaneSlider(
          this.objectsMap,
          new Vector3(1, 0, 0),
          new Vector3(0, 1, 0),
          2,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.translationGizmo.add(planeSlider);
      }
      if (this.options.translateXAllowed) {
        const axisArrow = new AxisArrow(
          this.objectsMap,
          new Vector3(1, 0, 0),
          0,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.translationGizmo.add(axisArrow);
      }
      if (this.options.translateYAllowed) {
        const axisArrow = new AxisArrow(
          this.objectsMap,
          new Vector3(0, 1, 0),
          1,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.translationGizmo.add(axisArrow);
      }
      if (this.options.translateZAllowed) {
        const axisArrow = new AxisArrow(
          this.objectsMap,
          new Vector3(0, 0, 1),
          2,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.translationGizmo.add(axisArrow);
      }
    }

    this.rotationGizmo = new Group();
    if (this.options.rotateAllowed) {
      if (this.options.rotateXAllowed) {
        const axisRotator = new AxisRotator(
          this.objectsMap,
          new Vector3(1, 0, 0),
          0,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.rotationGizmo.add(axisRotator);
      }
      if (this.options.rotateYAllowed) {
        const axisRotator = new AxisRotator(
          this.objectsMap,
          new Vector3(0, 1, 0),
          1,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.rotationGizmo.add(axisRotator);
      }
      if (this.options.rotateZAllowed) {
        const axisRotator = new AxisRotator(
          this.objectsMap,
          new Vector3(0, 0, 1),
          2,
          true,
          0.03,
          1,
          false,
          1,
          options
        );
        this.rotationGizmo.add(axisRotator);
      }
    }

    this.localScaleGizmo = new Group();
    if (this.options.scaleAllowed) {
      const axisScalerX = new AxisScaler(
        this.objectsMap,
        new Vector3(1, 0, 0),
        0,
        true,
        0.03,
        1,
        false,
        1,
        options
      );
      this.localScaleGizmo.add(axisScalerX);

      const axisScalerY = new AxisScaler(
        this.objectsMap,
        new Vector3(0, 1, 0),
        1,
        true,
        0.03,
        1,
        false,
        1,
        options
      );
      this.localScaleGizmo.add(axisScalerY);

      const axisScalerZ = new AxisScaler(
        this.objectsMap,
        new Vector3(0, 0, 1),
        2,
        true,
        0.03,
        1,
        false,
        1,
        options
      );
      this.localScaleGizmo.add(axisScalerZ);
    }

    this.globalScaleGizmo = new Group();
    if (this.options.scaleAllowed) {
      const sphereScaler = new SphereScaler(
        this.objectsMap,
        new Vector3(Math.sqrt(3) / 3, Math.sqrt(3) / 3, Math.sqrt(3) / 3),
        true,
        0.03,
        1,
        false,
        1,
        options
      );
      this.globalScaleGizmo.add(sphereScaler);
    }

    this.content = new Group();
    this.contentInner = new Group();
    this.content.add(this.contentInner);
    this.add(this.content);
  }

  getHoveredRotationAxis(): (0 | 1 | 2) | null {
    if (!this.hoverObject) return null;
    const helper = this.objectsMap[this.hoverObject.uuid];
    return helper instanceof AxisRotator ? helper.axis : null;
  }

  getSpace() {
    // if (this.objects.length > 1) return GIZMOSPACE.GLOBAL;
    return this.space;
  }

  updateSpace(space: GIZMOSPACE) {
    this.space = space;

    this.setContentChild();

    this.setContentMatrix();
  }

  // updateType(type: GIZMOTYPE) {
  //   this.gizmoTypeTest = type;

  //   this.setContentChild();
  // }

  setContentMatrix() {
    if (this.options.updateEntitiesOnDraggingChanged) {
      this.content.position.copy(
        this.objects.length > 0
          ? this.objects[this.objects.length - 1].position
          : center
      );

      if (this.objects.length && this.getSpace() === GIZMOSPACE.LOCAL) {
        this.objects[this.objects.length - 1].updateMatrixWorld(true);

        this.objects[this.objects.length - 1].matrixWorld.decompose(p, q, s);

        this.content.quaternion.copy(q);
      } else {
        this.content.quaternion.copy(q0);
      }
    } else {
      if (this.activeObject) {
        this.objects.forEach((obj) => {
          obj.position.copy(this.content.position);
          obj.quaternion.copy(this.content.quaternion);
          obj.updateMatrix();
          obj.updateMatrixWorld(true);
        });
      } else {
        if (this.objects.length) {
          this.content.position.copy(
            this.objects[this.objects.length - 1].position
          );
          this.content.quaternion.copy(
            this.objects[this.objects.length - 1].quaternion
          );
          this.content.updateMatrix();
          this.content.updateMatrixWorld(true);
        }
      }
    }

    this.setContentScale();
  }

  setContentScale() {
    // set gizmo screen size the same always
    const camera = Client.userEntity.getCamera();
    if (camera) {
      const scale = TransformControls.GetGizmoScale(
        this.content.position,
        camera,
        this.options.isMobileDevice ?? isMobile()
      );
      this.contentInner.scale.set(scale, scale, scale);
    }
  }

  public static GetGizmoScale(
    position: Vector3,
    camera: Camera,
    isMobileDevice: boolean
  ) {
    return (
      position.distanceTo(camera.position) * (isMobileDevice ? 0.08 : 0.05)
    );
  }

  setContentPresent(present: boolean) {
    if (present) {
      this.parentObject.add(this);
    } else {
      this.parentObject.remove(this);
    }
  }

  setContentChild() {
    // switch (this.gizmoTypeTest) {
    //   case GIZMOTYPE.TRANSLATE:
    //     this.content.remove(this.rotationGizmo);

    //     this.content.remove(this.localScaleGizmo);

    //     this.content.remove(this.globalScaleGizmo);

    //     this.content.add(this.translationGizmo);

    //     break;

    //   case GIZMOTYPE.ROTATE:
    //     this.content.remove(this.translationGizmo);

    //     this.content.remove(this.localScaleGizmo);

    //     this.content.remove(this.globalScaleGizmo);

    //     this.content.add(this.rotationGizmo);

    //     break;

    //   case GIZMOTYPE.SCALE:
    // if (this.getSpace() === GIZMOSPACE.GLOBAL) {
    //   this.content.add(this.rotationGizmo);

    //   this.content.add(this.translationGizmo);

    //   this.content.add(this.localScaleGizmo);

    //   this.content.add(this.globalScaleGizmo);
    // } else {
    this.contentInner.add(this.rotationGizmo);

    this.contentInner.add(this.translationGizmo);

    this.contentInner.add(this.globalScaleGizmo);

    this.contentInner.add(this.localScaleGizmo);
    // }

    //     break;
    // }
  }

  getAttachedObjects() {
    return this.objects;
  }

  //TODO this fires gizmo position recalculation each frame
  // probably there should be better way to do that on entities matrix updates only
  override updateMatrixWorld(force: boolean) {
    this.setContentMatrix();

    if (this.options.updateEntitiesOnDraggingChanged) {
      //calculate gizmo matrix

      //calculate content
      this.setContentPresent(this.objects.length > 0);
      this.setContentChild();
    }

    super.updateMatrixWorld(force);
  }

  attachEntities(entities: GameEntity[]): this {
    // Filter out locked entities
    const unlockedEntities = entities.filter((e) => !e.getLocked());
    this.objects = unlockedEntities
      .map((e) => e.getRootNode())
      .filter((root) => Boolean(root)) as Object3D[];

    // this.setContentMatrix();
    this.setContentPresent(this.objects.length > 0);
    // this.setContentChild();
    for (let i = 0; i < this.entities.length; i++) {
      this.entities[i].attachTransformControls(null);
    }
    for (let i = 0; i < unlockedEntities.length; i++) {
      unlockedEntities[i].attachTransformControls(this);
    }
    this.entities = unlockedEntities;

    return this;
  }

  attachObjects(objects: Object3D[]): this {
    this.objects = objects;

    this.setContentMatrix();

    this.setContentPresent(this.objects.length > 0);
    this.setContentChild();
    for (let i = 0; i < this.entities.length; i++) {
      this.entities[i].attachTransformControls(null);
    }
    this.entities = [];

    this.updateSpace(GIZMOSPACE.LOCAL);

    return this;
  }

  handlePointerDown = (e: PointerEvent): boolean => {
    if (!this.visible) return false;
    if (e.button !== 0) return false;
    if (this.objects.length === 0) return false;

    this.content.updateMatrixWorld(true);

    this.content.updateMatrix();

    this.onPointerDownMatrixWorld.copy(this.content.matrixWorld);

    this.onPointerDownMatrixLocal.copy(this.content.matrix);

    this.camera.updateMatrixWorld(true);

    const coords = new Vector2(
      (e.offsetX / this.domElement.clientWidth) * 2 - 1,

      -(e.offsetY / this.domElement.clientHeight) * 2 + 1
    );

    this.raycaster.setFromCamera(coords, this.camera);

    const intersections = this.raycaster.intersectObject(
      this.content,
      true,
      undefined,
      undefined,
      this.raycaster.SORT_LAYER_DISTANCE
    );
    if (this.options.isMobileDevice) {
      this.applyHover(e, intersections);
    }

    if (intersections.length) {
      this.activeObject = this.objectsMap[intersections[0].object.uuid];

      this.activeObject.onPointerDown({ i: intersections[0] });

      if (this.onDragStart) {
        this.onDragStart(this.activeObject.pointerDownInfo, this.getSpace());
      }

      // @ts-expect-error - setPointerCapture is not in the type definition
      e.target.setPointerCapture(e.pointerId);

      this.dispatchEvent({
        ..._mouseDownEvent,
        // @ts-expect-error dispatchEvent type
        data: this.activeObject.pointerDownInfo,
      });

      return true;
    }
    return false;
  };

  applyHover = (e: PointerEvent, intersections: Intersection[]) => {
    if (!this.visible) return;
    if (!this.activeObject) {
      if (
        intersections.length &&
        !e.defaultPrevented &&
        (e.type === "pointerdown" ||
          e.type === "pointermove" ||
          e.buttons === 0)
      ) {
        if (this.hoverObject === intersections[0].object) {
          // nothing to do, still the same hovered object
        } else {
          if (document.body.style.cursor !== "grabbing")
            document.body.style.cursor = "auto";
          if (this.hoverObject) {
            this.objectsMap[this.hoverObject.uuid].onHover(false);
          }

          this.hoverObject = intersections[0].object;

          this.objectsMap[this.hoverObject.uuid].onHover(true);
        }
      } else {
        if (this.hoverObject) {
          this.objectsMap[this.hoverObject.uuid].onHover(false);
          if (document.body.style.cursor !== "grabbing")
            document.body.style.cursor = "grab";
        }

        this.hoverObject = null;
      }
    }
  };

  handlePointerMove = (e: PointerEvent): boolean => {
    if (!this.visible) return false;
    this.content.updateMatrixWorld(true);

    this.camera.updateMatrixWorld(true);

    const coords = new Vector2(
      (e.offsetX / this.domElement.clientWidth) * 2 - 1,

      -(e.offsetY / this.domElement.clientHeight) * 2 + 1
    );

    this.raycaster.setFromCamera(coords, this.camera);

    const intersections = this.raycaster.intersectObject(
      this.content,
      true,
      undefined,
      undefined,
      this.raycaster.SORT_LAYER_DISTANCE
    );
    if (!this.options.isMobileDevice && !isMultiSelectionActive) {
      this.applyHover(e, intersections);
    }

    if (this.activeObject) {
      const dm = this.activeObject.onPointerMove({
        ray: this.raycaster.ray.clone(),
      });

      const dl =
        this.getSpace() === GIZMOSPACE.GLOBAL
          ? dm

              .clone()

              .multiply(this.onPointerDownMatrixWorld.clone().invert())

              .multiply(this.onPointerDownMatrixLocal)
          : dm.clone();

      const dw =
        this.getSpace() === GIZMOSPACE.GLOBAL
          ? dm.clone()
          : dm

              .clone()

              .multiply(this.onPointerDownMatrixWorld)

              .multiply(this.onPointerDownMatrixLocal.clone().invert());

      const l = this.onPointerDownMatrixLocal.clone().multiply(dl);

      const w = this.onPointerDownMatrixWorld.clone().multiply(dw);
      if (
        this.autoTransform &&
        !this.options.updateEntitiesOnDraggingChanged &&
        !(
          this.getSpace() === GIZMOSPACE.GLOBAL &&
          this.activeObject.pointerDownInfo.component === "AxisRotator"
        )
      ) {
        l.decompose(
          ["Arrow", "Slider"].includes(
            this.activeObject.pointerDownInfo.component
          )
            ? this.content.position
            : new Vector3(),

          this.content.quaternion,

          new Vector3()
        );
      }

      if (this.onDrag) {
        this.onDrag(l, dl, w, dw);
      }
    }

    //@ts-expect-error dispatchEvent type
    this.dispatchEvent(_changeEvent);
    //@ts-expect-error dispatchEvent type
    this.dispatchEvent(_objectChangeEvent);

    return this.activeObject !== null;
  };

  handlePointerUp = (e: PointerEvent): boolean => {
    if (!this.visible) {
      checkFalse(
        isTransformControlsActive(),
        "TransformControls not visible but active when releasing pointer. This indicates the gizmo was hidden without ending the interaction beforehand."
      );
    }

    if (e.button !== 0) return false;

    // @ts-expect-error - releasePointerCapture is not in the type definition
    e.target.releasePointerCapture(e.pointerId);

    return this.endTransformInteraction();
  };

  /** Ends any current transform interaction, cleaning up any hover state and intersections
   * @returns {boolean} true if there was an active transform interaction to end, false otherwise
   * */
  endTransformInteraction(): boolean {
    if (this.options.isMobileDevice) {
      if (this.hoverObject) {
        this.objectsMap[this.hoverObject.uuid].onHover(false);
      }

      this.hoverObject = null;
    }

    if (this.activeObject) {
      this.activeObject.onPointerUp();

      if (this.onDragEnd) {
        this.onDragEnd();
      }

      this.dispatchEvent({
        ..._mouseUpEvent,
        //@ts-expect-error dispatchEvent type
        data: this.activeObject.pointerDownInfo,
      });

      this.activeObject = null;
      return true;
    } else {
      return false;
    }
  }

  dispose(): void {
    this.parentObject.remove(this);
    this.remove(this.content);

    this.content.traverse((obj) => {
      if (obj instanceof AxisArrow) {
        obj.dispose();
      }
    });
  }
}
