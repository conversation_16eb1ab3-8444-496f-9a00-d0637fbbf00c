/* eslint linebreak-style: 0 */
// annotate-tw-theme-vars.ts
import fs from "fs";

// Expanded mapping with more common Tailwind utility prefixes
const namespaceMap: Record<string, string[]> = {
  "--color-": [
    "bg-{name}",
    "text-{name}",
    "border-{name}",
    // "outline-{name}",
    "ring-{name}",
    // "from-{name}",
    // "to-{name}",
    // "via-{name}",
  ],
  "--font-": ["font-{name}"],
  "--text-": ["text-{name}"],
  "--font-weight-": ["font-{name}"],
  "--tracking-": ["tracking-{name}"],
  "--leading-": ["leading-{name}"],
  "--breakpoint-": ["{name}:*"],
  "--container-": ["@{name}:*", "max-w-{name}"],
  "--spacing-": [
    "p-{name}",
    // "px-{name}",
    // "py-{name}",
    "m-{name}",
    // "mx-{name}",
    // "my-{name}",
    "gap-{name}",
    "space-x-{name}",
    "space-y-{name}",
    // "w-{name}",
    // "h-{name}",
    // "min-w-{name}",
    // "min-h-{name}",
    // "max-w-{name}",
    // "max-h-{name}",
    "ring-offset-{name}",
  ],
  // More specific glob-like mapping for spacing sizes embedded in name
  "--spacing-*-size-": [
    "w-{name}",
    "h-{name}",
    // "min-w-{name}",
    // "min-h-{name}",
    // "max-w-{name}",
    // "max-h-{name}",
  ],
  "--radius-": [
    "rounded-{name}",
    // "rounded-t-{name}",
    // "rounded-b-{name}",
  ],
  "--shadow-": ["shadow-{name}"],
  "--inset-shadow-": ["inset-shadow-{name}"],
  "--drop-shadow-": ["drop-shadow-{name}"],
  "--blur-": ["blur-{name}"],
  "--perspective-": ["perspective-{name}"],
  "--aspect-": ["aspect-{name}"],
  "--ease-": ["ease-{name}"],
  "--animate-": ["animate-{name}"],
};

interface NamespaceMatch {
  key: string;
  name: string;
}

/**
 * Annotate Tailwind theme variables in CSS with utility class examples
 * @param {string} cssFilePath - Path to the CSS file to annotate
 */
export async function annotateTailwindThemeVars(
  cssFilePath: string
): Promise<void> {
  // Read CSS file
  const css = fs.readFileSync(cssFilePath, "utf8");

  // Remove all existing block comments from the CSS to avoid duplicating or
  // nesting annotations. This handles multi-line comment blocks.
  // But preserve JSDoc-style comments (/**...**/)
  const cssWithoutBlockComments = css.replace(/\/\*(?!\*)[\s\S]*?\*\//g, "");

  // Convert a glob-like pattern (supports '*') to a RegExp that matches from start
  const globToRegex = (pattern: string): RegExp => {
    const escaped = pattern.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const regexSource = `^${escaped.replace(/\\\*/g, ".*")}`;
    return new RegExp(regexSource);
  };

  // Build a regex that captures the first '*' as a named group 'name'
  const buildCapturingRegex = (pattern: string): RegExp => {
    // Escape regex chars; we will only use this regex for matching, not capturing name
    const escaped = pattern.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const replaced = escaped.replace(/\\\*/g, ".*?");
    return new RegExp(`^${replaced}`);
  };

  // Find the best matching namespace key (supports '*' wildcards) and extract name
  const matchNamespace = (variableName: string): NamespaceMatch | null => {
    const candidates = Object.keys(namespaceMap)
      .filter((key) => globToRegex(key).test(variableName))
      // prefer the longest pattern (most specific)
      .sort((a, b) => b.length - a.length);

    if (candidates.length === 0) return null;

    const selected = candidates[0];
    // Determine the prefix up to the first '*', if any.
    const starIndex = selected.indexOf("*");
    const basePrefix = starIndex >= 0 ? selected.slice(0, starIndex) : selected;
    // Verify the match with a regex built from the selected pattern
    const capturing = buildCapturingRegex(selected);
    if (!capturing.test(variableName)) return null;
    // Name is everything after the basePrefix
    const name = variableName.slice(basePrefix.length);
    return { key: selected, name };
  };

  // Process the entire CSS content to handle multiline declarations
  const annotated = cssWithoutBlockComments.replace(
    /(--[a-z0-9-]+):\s*([^;]*;)/gi,
    (match, varName: string, value: string) => {
      const matched = matchNamespace(varName);
      if (!matched) return match;

      const namePart = matched.name; // e.g. foo
      const examples = namespaceMap[matched.key].map((p) =>
        p.replace("{name}", namePart)
      );

      return `${varName}: ${value} /* ${examples.join(" ")} */`;
    }
  );

  // Write annotated CSS to the same file
  fs.writeFileSync(cssFilePath, annotated, "utf8");
}
