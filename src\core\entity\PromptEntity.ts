import { Object3D, BoxG<PERSON>metry, Mesh, Color, Box3, Vector3 } from "three";
import { setObjectRaycastable } from "../util/RaycastUtils";
import { BoxLineObject } from "../util/BoxLineObject";
import { runCommands } from "../command";
import SpawnPrefabCommand from "../command/commands/spawnPrefab";
import removeEntity from "../command/helpers/removeEntity";
import { createMeshEntityPrefab } from "../client/helpers/createMeshEntityPrefab";
import { LiveblocksEntity } from "./NetworkEntity";
import { localColor, UserEntity } from "./UserEntity";
import { EmissiveFresnelShader } from "@/core/shader";
import { Client } from "@/core/client";
import { EntityType, PromptEntityData } from "@/liveblocks.config";
import { EntityId, makeUniqueKey } from "@nilo/ecs";
import { PhysicsUserData } from "@/types";

/** Lightweight settings for controlling the properties of `PromptEntity` */
export class PromptEntitySettings {
  prompt: string = "";
  color: number = 0xffffff;
  userId: EntityId = "";
  isStatic: boolean = false;
}

/**
 * Generative prompt cursor entity
 * TODO: split into client / server parts when we switch to c/s (post proto)
 */
export class PromptEntity extends LiveblocksEntity {
  // entity type
  public override type = "PromptEntity" as const;
  private didSpawn: boolean = false;
  private isStatic: boolean = false;
  // prompt
  public _prompt: string = "";
  //user of the id who created this prompt entity
  public _userId: EntityId;

  public static override readonly UNIQUE_ID = makeUniqueKey("PromptEntity");

  public get userId() {
    return this._userId;
  }

  constructor(
    id: EntityId | null = null,
    settings: PromptEntitySettings = new PromptEntitySettings()
  ) {
    super(false, id); // non persistent

    this.setRootNode(this.root);

    this._userId = settings.userId;
    this.isStatic = settings.isStatic;
    this._createdBy = settings.userId;
  }

  public setPrompt(value: string, _dirty = true) {
    if (this._prompt !== value) {
      // always call on prompt change before setting the prompt value
      this.onPromptChange(value);
      this._prompt = value;
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
  }

  public override getSimulatedPosition(target: Vector3): void {
    this.getPosition(target);
  }

  public getPrompt() {
    return this._prompt;
  }

  // prompt color
  public _color: number = 0xffffff;
  public setColor(value: number, _dirty = true) {
    if (this._color !== value) {
      this._color = value;
      this.onColorChange();
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
  }

  public getColor() {
    return this._color;
  }

  // three.js local scenegraph
  private root: Object3D = new Object3D();

  // bounding box material
  private boundingBoxMaterial?: EmissiveFresnelShader;

  private bboxMesh?: BoxLineObject;

  private generateBoxMesh() {
    const entity = Client.getEntity(this._userId);
    if (!entity) return;
    const color =
      this._userId === Client.userEntity.id
        ? localColor
        : (Client.getEntity(this._userId) as UserEntity).getColor();

    const boundingBoxGeometry = new BoxGeometry(0.95, 0.95, 0.95);
    this.boundingBoxMaterial = new EmissiveFresnelShader({
      color: new Color(color),
      minOpacity: 0.25,
      maxOpacity: 0.6,
      power: 1.5,
    });
    const boundingBoxMesh = new Mesh(
      boundingBoxGeometry,
      this.boundingBoxMaterial
    );

    setObjectRaycastable(boundingBoxMesh);
    boundingBoxMesh.renderOrder = 1;
    boundingBoxMesh.position.y = 0.5;

    const bboxMesh = new BoxLineObject(this, color);
    bboxMesh.setFromBox(
      new Box3(new Vector3(-0.475, 0, -0.475), new Vector3(0.475, 0.95, 0.475))
    );

    bboxMesh.getMaterial().dashed = true; // This makes box dashed
    bboxMesh.getMaterial().dashSize = 0.05;
    bboxMesh.getMaterial().dashOffset = 0.025;
    bboxMesh.getMaterial().gapSize = 0.05;

    this.bboxMesh = bboxMesh;
  }

  private addBox() {
    if (this.bboxMesh) this.root.add(this.bboxMesh);
  }

  private removeBox() {
    if (this.bboxMesh) this.root.remove(this.bboxMesh);
  }

  public override toJSON(): PromptEntityData {
    return {
      ...super.toJSON(),
      type: EntityType.PromptEntity,
      userId: this._userId,
      prompt: this._prompt,
      color: this._color,
      isStatic: this.isStatic,
    };
  }

  public setStatic(isStatic: boolean) {
    this.isStatic = isStatic;
  }

  public get isStaticEntity() {
    return this.isStatic;
  }

  public override onRoomJoin(): void {
    super.onRoomJoin();
    if (!this.bboxMesh) {
      this.generateBoxMesh();
      this.addBox();
    }
  }

  /** @deprecated: will be removed along with liveblocks */
  public override fromJSON(json: PromptEntityData) {
    super.fromJSON(json);
    this._userId = json.userId;
    this.setPrompt(json.prompt, false);
    this.setColor(json.color, false);
    this.setStatic(json.isStatic || false);
  }

  public override update(time: number, deltaTime: number) {
    super.update(time, deltaTime);
  }

  private onPromptChange(prompt: string) {
    const isAuthoringUser = this._userId === Client.userEntity.id;

    if (prompt && isAuthoringUser && !this.didSpawn) {
      const physicsUserData: PhysicsUserData = {
        physics: {
          isStatic:
            !Client.composer.options.enablePhysicsOnCreate || this.isStatic,
          isDisabled: !Client.composer.options.enableCollidersOnCreate,
          shapeType:
            Client.composer.options.shapeTypeOnCreate === "asis"
              ? "asis"
              : "convexHull",
        },
      };

      const prefab = createMeshEntityPrefab({
        position: this.position,
        rotation: this.rotation,
        scale: this.scale,
        color: this._color,
        userId: Client.userEntity.id,
        generateFromPrompt: prompt,
        physicsUserData: physicsUserData,
        tags: ["generated"], // add a tag for finding objects later
      });

      this.didSpawn = true;
      removeEntity(this); // remove directly as prompt entities can't be resurrected
      runCommands([new SpawnPrefabCommand(prefab, /* select */ true)]);
    }
  }

  private onColorChange() {
    this.boundingBoxMaterial?.setColor(new Color(this._color));
  }

  override destroy() {
    this.removeBox();

    super.destroy();
  }
}
