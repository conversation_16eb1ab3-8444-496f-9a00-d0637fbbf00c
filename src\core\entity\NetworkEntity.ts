import { Quaternion, Vector3 } from "three";
import { Schema } from "@evenstar/byteform";
import { GameEntity } from "./Entity";
import { Transform } from "packages/network/src";
import { GameEntityData } from "@/liveblocks.config";

const _p = new Vector3();
const _s = new Vector3();
const _q = new Quaternion();

/**
 * An entity that is networked (without Liveblocks).
 * The `Msg` generic parameter represents the wire data type of this entity's encoding.
 * Note that it is used for implementers in order to ensure type safety of network code.
 * For users of `NetworkEntity`, this can be safely typed as `unknown`.
 */
export abstract class NetworkEntity<Msg> extends GameEntity {
  private _netShouldUpdate: boolean = false;

  public set netShouldUpdate(value: boolean) {
    this._netShouldUpdate = value;
  }

  public get netShouldUpdate() {
    return this._netShouldUpdate;
  }

  public deserializeTransform(transform: Transform) {
    const { position, orientation, scale } = transform;
    this._entityReplication.fromTransform(position, orientation);
    this.setTransformProps(undefined, undefined, scale);
  }

  public serializeTransform() {
    this.getPosition(_p);
    this.getOrientation(_q);
    this.getScale(_s);

    return {
      position: _p.clone(),
      orientation: _q.clone(),
      scale: _s.clone(),
    };
  }

  /**
   * Static schema definition for this entity type (set on subclasses).
   * If not present, the message will instead use CBOR encoding.
   */
  public static readonly SCHEMA?: Schema<unknown>;

  /**
   * Static universal, numerical (u32) identifier for the type of this entity.
   * Best derived using `makeComponentKey`.
   */
  public static readonly UNIQUE_ID: number;

  /** Instance view that forwards to the static SCHEMA on the subclass. */
  public get SCHEMA(): Schema<Msg> | undefined {
    return (this.constructor as typeof NetworkEntity).SCHEMA as
      | Schema<Msg>
      | undefined;
  }

  /** Instance view that forwards to the static UNIQUE_ID on the subclass. */
  public get UNIQUE_ID(): number {
    return (this.constructor as typeof NetworkEntity).UNIQUE_ID as number;
  }

  /**
   * Loads a network message into this entity's state.
   * @param msg The serialized data to decode into this entity
   */
  public abstract deserialize(msg: Msg): void;

  /**
   * Serializes the state of this entity to a network message.
   */
  public abstract serialize(): Msg;
}

/** The simplest, bestest, stupidest way to completely get rid of Liveblocks. */
export abstract class LiveblocksEntity extends NetworkEntity<GameEntityData> {
  public static override readonly SCHEMA = undefined;

  public override deserialize(msg: GameEntityData): void {
    this.fromJSON(msg);
  }

  public override serialize(): GameEntityData {
    return this.toJSON();
  }
}
