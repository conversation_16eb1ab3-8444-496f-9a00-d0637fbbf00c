import {
  collection,
  CollectionReference,
  doc,
  DocumentData,
} from "firebase/firestore";
import {
  DbCollections,
  DbModelGenerationTask,
  DbUser,
  DbWorld,
  DbDeletedWorld,
  DbWorldPlaySession,
  DbWorldScore,
  DbWorldUserRelation,
  DbAsset,
} from "@nilo/firebase-schema";
import { db } from "@/config/firebase";

function col<T extends DocumentData>(name: DbCollections) {
  return collection(db, name) as CollectionReference<T, T>;
}

export const usersCollection = col<DbUser>(DbCollections.users);
export const modelGenerationTasksCollection = col<DbModelGenerationTask>(
  DbCollections.modelGenerationTasks
);

export const worldsCollection = col<DbWorld>(DbCollections.worlds);
export const deletedWorldsCollection = col<DbDeletedWorld>(
  DbCollections.deletedWorlds
);
export const worldScoresCollection = col<DbWorldScore>(
  DbCollections.worldScores
);
export const worldUserRelationsCollection = col<DbWorldUserRelation>(
  DbCollections.worldUserRelations
);
export const worldUserRelationDoc = ({
  kind,
  worldId,
  userId,
}: {
  kind: string;
  worldId: string;
  userId: string;
}) => doc(worldUserRelationsCollection, `${kind}-${worldId}-${userId}`);
export const worldPlaySessionsCollection = col<DbWorldPlaySession>(
  DbCollections.worldPlaySessions
);

export const assetsCollection = col<DbAsset>(DbCollections.assets);
