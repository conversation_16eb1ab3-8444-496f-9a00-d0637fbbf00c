import { Client } from "../client";
import { PanelPositioning } from "../util/UIManager/Panels/options";
import { GameEntity } from "./Entity";
import { MeshEntity } from "./MeshEntity";
import { ParticleEntity } from "./ParticleEntity";
import { PrimitiveEntity } from "./PrimitiveEntity";
import { UserEntity } from "./UserEntity";
import { SelectedEntityUI } from "@/components/entity/SelectedEntityUI";
import UIManager from "@/core/util/UIManager";

const showAtBottomOffset: number = 10;

export default class SelectedEntities {
  private entities: GameEntity[] = [];

  constructor(private readonly userEntity: UserEntity) {}

  clone() {
    const selectedEntities = new SelectedEntities(this.userEntity);
    selectedEntities.entities = [...this.entities];
    return selectedEntities;
  }

  get() {
    return [...this.entities];
  }

  updateOutline = () => {
    if (Client.composer.passes.outline) {
      Client.composer.passes.outline.setSelectedObjects(
        this.userEntity.getColor(),
        this.get()
      );
    }
  };

  updateSelectionLayers(entity: GameEntity, value: boolean) {
    if (
      entity instanceof PrimitiveEntity ||
      entity instanceof MeshEntity ||
      entity instanceof ParticleEntity
    ) {
      if (value) {
        return entity.setSelected(value);
      }

      const selectedOutlineObjects =
        Client.composer.passes.outline.selectedEntities;
      let counter = 0;
      selectedOutlineObjects.keys().forEach((key) => {
        if (selectedOutlineObjects.get(key)?.includes(entity)) {
          counter += 1;
        }
      });
      if (counter <= 0) {
        // if this entity is not selected for any client, remove outline
        entity.setSelected(value);
      }
    }
  }

  set(entities: GameEntity[]) {
    let result = false;
    const entitiesSet = new Set(entities);

    const prev = this.get();
    const prevSet = new Set(prev);

    for (let i = 0; i < prev.length; i += 1) {
      if (!entitiesSet.has(prev[i])) {
        result = true;
        //delete
        this.delete(prev[i]);
      }
    }

    for (let i = 0; i < entities.length; i += 1) {
      if (!prevSet.has(entities[i])) {
        result = true;
        //add
        this.add(entities[i]);
      }
    }

    return result;
  }

  add(entity: GameEntity) {
    if (!this.has(entity)) {
      this.entities.push(entity);
      this.addSelectedEntityPanel(entity);
      this.editHintDuplicatePanel();
      this.userEntity.updateToolbar();
      this.updateOutline();
      this.updateSelectionLayers(entity, true);

      return true;
    }
    return false;
  }
  delete(entity: GameEntity) {
    const idx = this.entities.findIndex((e) => e === entity);
    if (idx !== -1) {
      this.removeSelectedEntityPanel(entity);
      this.editHintDuplicatePanel();
      this.entities.splice(idx, 1);
      this.updateOutline();
      this.updateSelectionLayers(entity, false);

      this.userEntity.updateToolbar();

      return true;
    }
    return false;
  }
  clear() {
    for (let i = 0; i < this.entities.length; i += 1) {
      this.removeSelectedEntityPanel(this.entities[i]);
    }
    const entities = [...this.entities];
    this.entities.length = 0;
    this.userEntity.updateToolbar();
    this.updateOutline();
    for (let i = 0; i < entities.length; i += 1) {
      this.updateSelectionLayers(entities[i], false);
    }
    return true;
  }
  has(entity: GameEntity) {
    return this.entities.includes(entity);
  }
  get size() {
    return this.entities.length;
  }

  private addSelectedEntityPanel(entity: GameEntity) {
    if (this.userEntity.isLocal) {
      entity.setSelectedPanelId(
        UIManager.instance.addPanel({
          entityId: entity.id,
          screenOffset: { x: 0, y: showAtBottomOffset },
          sceneOffset: { x: 0, y: 0, z: 0 },
          content: <SelectedEntityUI id={entity.id} />,
          positioning: PanelPositioning.BOTTOM_CENTER,
          showOnlyNearCamera: true,
        })
      );
    }
  }

  private removeSelectedEntityPanel(entity: GameEntity) {
    const panelId = entity.getSelectedPanelId();
    if (panelId) {
      UIManager.instance.removePanel(panelId);
      entity.setSelectedPanelId(null);
    }
  }

  private editHintDuplicatePanel() {
    UIManager.instance.editPanel(this.userEntity.getHintDuplicatePanelId(), {
      state: { hasEntitiesSelected: this.size > 0 },
    });
  }
}
