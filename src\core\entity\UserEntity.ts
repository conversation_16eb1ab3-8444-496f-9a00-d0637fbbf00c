import { gsap } from "gsap";
import { nanoid } from "nanoid/non-secure";
import toast from "react-hot-toast";
import {
  Box3,
  Group,
  Intersection,
  Matrix4,
  Mesh,
  Object3D,
  PerspectiveCamera,
  Plane,
  Quaternion,
  Vector2,
  Vector2<PERSON>ike,
  Vector3,
} from "three";
import { runCommands } from "../command";
import CreateImageMeshEntityCommand from "../command/commands/createImageMeshEntity";
import CreateUserModelMeshEntityCommand from "../command/commands/createUserModelMeshEntity";
import SelectEntitiesCommand from "../command/commands/selectEntities";
import SetEntityOrientationCommand from "../command/commands/setEntityOrientation";
import SetEntityPhysicsCommand from "../command/commands/setEntityPhysics";
import SetEntityPhysicsFlagsCommand from "../command/commands/setEntityPhysicsFlags";
import SetEntityPositionCommand from "../command/commands/setEntityPosition";
import { selectedEntitiesDelete } from "../commands/SelectedEntitiesDelete";
import { arrayEqual } from "../util/ArrayUtils";
import { assertUnreachable } from "../util/Assert";
import { BrushControls } from "../util/controls/brushControls/BrushControls";
import {
  BrushControlsHandler,
  createBrushControlsHandler,
} from "../util/controls/brushControls/BrushControlsHandler";
import {
  isMultiSelectionActive,
  DragSelectControls,
  dragSelectControlsCreate,
} from "../util/controls/dragSelect/DragSelectControls";
import { DuplicateControls } from "../util/controls/DuplicateControls";
import { duplicateControlsCreate } from "../util/controls/duplicateControls/DuplicateControlsCreate";
import { DuplicateControlsInstant } from "../util/controls/duplicateControlsInstant/DuplicateControlsInstant";
import { DuplicateControlsWhileDragging } from "../util/controls/duplicateControlsInstant/DuplicateControlsWhileDragging";
import TransformControls, {
  GIZMOSPACE,
} from "../util/controls/TransformControls";
import { transformControlsCreate } from "../util/controls/transformControlsCreate/TransformControlsCreate";
import { BRUSH_ACTIONS, CurrentAction } from "../util/CurrentAction";
import { EntitySelectionMode } from "../util/EntitySelectionMode";
import { Exporter, ExportFormat } from "../util/export";
import { ImportedModel, Importer, LoadedAnimation } from "../util/import";
import {
  ACCEPTABLE_MODEL_FILE_EXTENSIONS,
  openFileBrowserAndGetFiles,
} from "../util/import/FileUtils";
import {
  PointerEventButton,
  pointerEventButton,
} from "../util/PointerEventButton";
import { poolCreate } from "../util/Pool";
import { allLayers } from "../util/RaycastUtils";
import { Toolbar } from "../util/toolbar/Toolbar";
import {
  beginTransaction,
  redo,
  Transaction,
  undo,
} from "../command/transaction";
import { GameEntityComponent } from "../components";
import { PlayerControlsType } from "../play/PlayerControlsFactory";
import { TransformBehavior } from "../client/behaviors";
import { ParticleBehavior } from "../ecs/behaviors/particle";
import SpawnPrefabCommand from "../command/commands/spawnPrefab";
import { createPromptEntityPrefab } from "../client/helpers/createPromptEntityPrefab";
import RemoveEntityCommand from "../command/commands/removeEntity";
import {
  PhysicsShapeType,
  PhysicsShapeTypeList,
} from "../../../packages/physics-with-jolt/src/types/PhysicsOptions";
import { PhysicsBodyInfoComponent } from "../ecs/behaviors/physics";
import { checkNull } from "../util/Check";
import { UserPresenceIndicators } from "./locationCone/UserPresenceIndicators";

import {
  HintBrushPanel,
  HintDuplicatePanel,
  HintTransformPanel,
  RadialMenuPanel,
} from "./panels/UserEntity";
import SelectedEntities from "./SelectedEntities";
import { SharedEntity } from "./SharedEntity";
import { _raycaster, Client } from "@/core/client";
import {
  EntityOriginTypes,
  GameEntity,
  MeshEntity,
  NetworkEntity,
  ParticleEntity,
  PrimitiveEntity,
  PromptEntity,
} from "@/core/entity";
import { CameraControls } from "@/core/util";
import { CameraComponent } from "@/core/ecs/components/CameraComponent";
import {
  AnimationData,
  BrushType,
  GameEntityData,
  EntityType,
  GenerationType,
  MeshEntityData,
  PrimitiveType,
  UserEntityData,
} from "@/liveblocks.config";
import { RoomDataService } from "@/services/RoomDataService";
import { createDelegateFunction } from "@nilo/utilities";
import { saveRoomLegacyPreview } from "@/utils/roomdata/saveRoomLegacyPreview";
import { EntityId, makeUniqueKey, Prefab, Query } from "@nilo/ecs";
import { UserData, userDataSchema } from "@nilo/network";
import UIManager from "@/core/util/UIManager";
import { isMobile } from "@/components/RadialMenu/useIsMobile";
import { getKeyboardAction } from "@/utils/keyboardShortcuts";
import { KeyboardMappingIDs } from "@/config/keyboardMapping";

const RADIAL_PANEL_ID = "radial-menu-next";

const DEFAULT_FOV = 50;
export const ANGLE_STATIC = Math.PI / 180;
const DEFAULT_ORIGIN = new Vector3(-2, 3.5, -15);
const DISTANCE_TO_SKIP_SELECTION = 4; // in pixels
const MAX_SIMPLE_DRAG_HORIZONTAL_DISTANCE = 150; // in scene units

export interface SetPrimitiveOptions {
  id?: string;
  selectEntity?: boolean;
  position?: Vector3;
  scale?: Vector3;
}

const position = new Vector3();
const intersection = new Vector3();
const secondaryIntersection = new Vector3();
const cameraDirection = new Vector3();
const cameraPosition = new Vector3();
const cameraRight = new Vector3();
const screenUp = new Vector3(0, 1, 0);
const dragPlaneNormal = new Vector3();
const offset = new Vector3();
const screenAlignedPlane = new Plane();
const horizontalPlane = new Plane(screenUp.clone());
const dragPlane = new Plane();
const secondaryDragPlane = new Plane();

enum PrevTypes {
  "A" = "A",
  "B" = "B",
  "NONE" = "",
}
let prevType: PrevTypes = PrevTypes.NONE;

const updateDragPlane = (point: Vector3, fallbackToTypeA?: boolean) => {
  const userEntity = Client.userEntity;
  const camera = userEntity.getCamera() as PerspectiveCamera;
  const isTypeA =
    userEntity.shiftPressed || userEntity.RMBPressed || fallbackToTypeA;
  if (isTypeA) {
    // Type A: Use vertical plane with normal parallel to camera Z axis
    if (prevType !== PrevTypes.A || fallbackToTypeA) {
      camera.getWorldDirection(cameraDirection);
      cameraRight.crossVectors(cameraDirection, screenUp).normalize();

      // Create a plane that's vertical and perpendicular to camera's right vector
      dragPlaneNormal.crossVectors(cameraRight, screenUp).normalize();
      screenAlignedPlane.setFromNormalAndCoplanarPoint(dragPlaneNormal, point);
      if (!fallbackToTypeA) prevType = PrevTypes.A;
    }
    return screenAlignedPlane;
  } else {
    // Type B: Use horizontal ground plane, but update its position to match object's X and Z
    if (prevType !== "B") {
      horizontalPlane.constant = -point.y; // Reset to ground level
      horizontalPlane.normal.copy(screenUp);
    }
    camera.getWorldPosition(cameraPosition);

    if (cameraPosition.y > point.y) {
      if (horizontalPlane.normal.equals(screenUp)) {
        horizontalPlane.constant = -horizontalPlane.constant;
        horizontalPlane.normal.copy(screenUp.clone().multiplyScalar(-1));
      }
    } else {
      if (!horizontalPlane.normal.equals(screenUp)) {
        horizontalPlane.constant = -horizontalPlane.constant;
        horizontalPlane.normal.copy(screenUp);
      }
    }
    prevType = PrevTypes.B;
    return horizontalPlane;
  }
};

const isBitSet = (number: number, bitPosition: number) => {
  const mask = 1 << bitPosition;
  return (number & mask) !== 0;
};

const stopAllEntities = () => {
  const entities = Client.getAllEntities();
  if (entities.length) {
    for (let i = 0; i < entities.length; i += 1) {
      entities[i]
        ?.getPhysicsBodyControl()
        ?.setLinearVelocity({ x: 0, y: 0, z: 0 });
    }
  }
};

export function toBase64(file: File | Blob) {
  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
  });
}

const secureShapeType = (input?: string) => {
  if (PhysicsShapeTypeList.includes(input as PhysicsShapeType)) {
    return input as PhysicsShapeType;
  }

  return undefined;
};

export const localColor = 0x2b99ff;
function getExtensionFromMimeType(type: string): string {
  switch (type) {
    case "image/jpeg":
      return "jpeg";
    case "image/jpg":
      return "jpg";
    case "image/png":
      return "png";
    case "image/gif":
      return "gif";
    case "image/webp":
      return "webp";
    case "image/svg+xml":
      return "svg";
    case "image/bmp":
      return "bmp";
    case "image/tiff":
      return "tiff";
    default:
      // Default to jpg if unknown mime type
      console.warn(`Unknown mime type: ${type}, defaulting to jpg`);
      return "jpg";
  }
}

const DOUBLE_TAP_DISTANCE = 16;
const doubleTap = (maxTapsDelay = 500) => {
  let lastTap = 0;

  const prevTapPosition = new Vector2();
  const currentTapPosition = new Vector2();

  return (event: PointerEvent): boolean => {
    if (!event.isPrimary) return false;

    prevTapPosition.copy(currentTapPosition);
    currentTapPosition.set(event.clientX, event.clientY);

    const time = Date.now();
    const dt = time - lastTap;
    lastTap = time;

    if (dt > maxTapsDelay) {
      return false;
    }

    if (prevTapPosition.distanceTo(currentTapPosition) > DOUBLE_TAP_DISTANCE) {
      return false;
    }

    return true;
  };
};

/**
 * Collaborative user entity
 * TODO: split into client and server parts when we switch to c/s (post proto)
 * TODO: note we'll probably only have a single entity class once component system implemented
 */
export class UserEntity extends NetworkEntity<UserData> {
  //#region members
  // entity type
  public override type = "UserEntity" as const;

  // users can't be manually transformed
  public override transformable: boolean = false;

  public shiftPressed = false;
  public RMBPressed = false;
  private lastRightClickTime = 0;
  private doubleRightClickThreshold = 300; // milliseconds
  private lastDraggedEntity: GameEntity | null = null; // Keep track of last dragged entity for unlock
  private lastClickedEntity: GameEntity | null = null; // Keep track of last clicked entity for unlock

  /// Transaction while currently moving an entity by simple click+drag
  private simpleMoveTransaction: Transaction | null = null;

  public targetEntity: GameEntity | null = null;
  public isLocal: boolean;

  // selected entities
  private _selectedEntities: SelectedEntities; //Set<Entity> = new Set();

  public readonly OnSceneClick = createDelegateFunction<[Vector2]>();

  private _toolbar: Toolbar | null = null;

  public getToolbar() {
    return this._toolbar;
  }

  public updateToolbar() {
    this._toolbar?.update();
  }

  // user color
  public _color: number = 0xffffff;
  public setColor(value: number, _dirty = true) {
    if (this._color !== value) {
      const prevColor = this._color;
      this._color = value;
      this.onColorChange(prevColor);
      this.netShouldUpdate ||= _dirty;
    }
  }

  public setTargetEntity(value: MeshEntity | null, _dirty = true) {
    if (this.targetEntity !== value) {
      this.targetEntity = value;
      this.setLocationConeVolumetricTargetEntity(value);
      this.netShouldUpdate ||= _dirty;
    }
  }

  public getColor() {
    return this._color;
  }

  // view fov (degrees)
  public _fov: number = DEFAULT_FOV;
  public setFov(value: number) {
    if (this._fov !== value) {
      this._fov = value;
      this.onFovChange();
      this.netShouldUpdate = true;
    }
  }
  public getFov() {
    return this._fov;
  }

  // users current generative prompt entity
  private promptEntity: PromptEntity | null = null;
  private previewPrimitiveEntity: PrimitiveEntity | null = null;

  // three.js local scenegraph
  private root = new Object3D();

  // camera (only exists on local users)
  private camera: PerspectiveCamera | null = null;
  private cameraControls: CameraControls | null = null;

  // view cone elements (only exists on remote users)
  private userPresenceIndicators: UserPresenceIndicators | null = null;

  private _currentAction: CurrentAction = CurrentAction.TRANSFORM_CONTROLS;

  /// local or remote user

  private importPreviewEntity: MeshEntity | null = null;

  public importer = new Importer();

  private pointerDownLocation = new Vector2();
  private pointerDownTime = 0;
  private pointerUpLocation = new Vector2();
  private pointerLocation = new Vector2();
  private lastTimestamp = 0;
  private pointerSpeed = new Vector2();
  private pointerLocationThreeScreenSpace = new Vector2();
  private _dragSelectControls?: DragSelectControls;

  public draggedEntity: GameEntity | null = null;
  private dragPosition = new Vector3();
  private dragStatic = false;
  private dragSpeed = new Vector3();
  private draggedOnDownPositions: Vector3[] = [];
  private draggedOnDownPosition = new Vector3();
  private draggedObject: Mesh | null = null;

  private _showGizmos = false;
  private _hintDuplicatePanelId: string | null = null;

  public setHintDuplicatePanelId(id: string | null) {
    this._hintDuplicatePanelId = id;
  }

  public getHintDuplicatePanelId() {
    return this._hintDuplicatePanelId;
  }

  private _hintTransformPanelId: string | null = null;

  public setHintTransformPanelId(id: string | null) {
    this._hintTransformPanelId = id;
  }

  public getHintTransformPanelId() {
    return this._hintTransformPanelId;
  }

  private _hintBrushPanelId: string | null = null;

  public setHintBrushPanelId(id: string | null) {
    this._hintBrushPanelId = id;
  }

  public getHintBrushPanelId() {
    return this._hintBrushPanelId;
  }

  protected isRadialOpened() {
    return UIManager.instance.hasPanel(RADIAL_PANEL_ID);
  }

  protected openRadial(point: Vector2Like) {
    if (!this.isRadialOpened()) {
      UIManager.instance.addPanel({
        id: RADIAL_PANEL_ID,
        floating: true,
        screenPosition: {
          x: point.x,
          y: point.y,
        },
        content: RadialMenuPanel(),
      });
    }
  }

  protected closeRadial() {
    UIManager.instance.removePanel(RADIAL_PANEL_ID);
  }

  protected toggleRadial(point: Vector2Like) {
    if (this.isRadialOpened()) {
      this.closeRadial();
    } else {
      this.openRadial(point);
    }
  }

  protected getRadialPosition(): Vector2 | null {
    if (this.isRadialOpened()) {
      const panel = UIManager.instance.getPanel(RADIAL_PANEL_ID);
      if (panel) {
        return this.getNormalizedCoordinates(
          panel.options.screenPosition!.x!,
          panel.options.screenPosition!.y!
        );
      }
    }
    return null;
  }

  //#endregion
  //#region constructor

  constructor(userId?: string) {
    super(false, userId); // non persistent
    this._selectedEntities = new SelectedEntities(this);
    this.root.name = "UserEntity";
    this.isLocal = !userId;

    if (this.isLocal) {
      this._toolbar = new Toolbar(this);
    }

    // Local user always has white selection box (or theme color)

    if (this.isLocal) {
      this.camera = new PerspectiveCamera(
        this._fov,
        Client.container.clientWidth / Client.container.clientHeight
      );
      this.camera.layers = allLayers;
      this.camera.position.copy(DEFAULT_ORIGIN);

      this.setHintTransformPanelId(
        UIManager.instance.addPanel({
          floating: false,
          visible: true,
          style: { top: 0, width: "100%" },
          content: HintTransformPanel(),
        })
      );

      this.setHintDuplicatePanelId(
        UIManager.instance.addPanel({
          floating: false,
          visible: false,
          style: { top: 0, width: "100%" },
          state: { hasEntitiesSelected: false },
          content: HintDuplicatePanel(),
        })
      );
      this.setHintBrushPanelId(
        UIManager.instance.addPanel({
          floating: false,
          visible: false,
          style: { top: 0, width: "100%" },
          state: { hasEntitiesSelected: false },
          content: HintBrushPanel(),
        })
      );

      this._dragSelectControls = dragSelectControlsCreate(
        Client.renderer.domElement
      );

      // Track keyboard events on Canvas, to prevent hotkeys fire on other UI elements
      Client.renderer.domElement.addEventListener("keydown", this.onKeyDown);
      Client.renderer.domElement.addEventListener("keyup", this.onKeyUp);
      Client.renderer.domElement.addEventListener("pointerdown", () => {
        // Focus as soon as we started interacting with the canvas
        Client.renderer.domElement.focus();
      });

      UIManager.instance.on("panel-open", (id) => {
        if (id === RADIAL_PANEL_ID) {
          // Hide transform controls
          this.setTransformControlsVisibility(false);
        }
      });

      UIManager.instance.on("panel-close", (id) => {
        /**
         * If any panel has been closed by a user, then focus state should be invalidated
         * In this case, we are going to focus the canvas so it can receive input again
         */
        Client.renderer.domElement.focus();

        if (id === RADIAL_PANEL_ID) {
          // Restore transform controls visibility
          this.setTransformControlsVisibility(true);
        }
      });

      document.body.addEventListener("pointerdown", this.onPointerDown);
      document.body.addEventListener("mousedown", this.onMouseDown);
      document.body.addEventListener("mouseup", this.onMouseUp);
      document.body.addEventListener("pointerup", this.onPointerUp);
      document.body.addEventListener("pointermove", this.onPointerMove);

      const isDoubleTap = doubleTap();
      document.body.addEventListener(
        "pointerup",
        (event) => {
          // Enable double-tap for mobile devices and pen/stylus input
          const isPenOrTouch =
            event.pointerType === "pen" || event.pointerType === "touch";
          if (isDoubleTap(event) && (isMobile() || isPenOrTouch)) {
            // Handle double tap
            this.openRadial({
              x: event.clientX,
              y: event.clientY,
            });
          }
        },
        { capture: true }
      );

      Client.renderer.domElement.addEventListener("dragover", (event) => {
        const intersectionPoint = this.getCurserCoordinates(event)[0];

        if (this.importPreviewEntity == null) {
          this.previewDragImport();
        }
        if (intersectionPoint) {
          this.setImportPreviewBoxPosition(intersectionPoint);
        }

        event.preventDefault();
      });

      // drag leave to remove preview of the import bounding box
      // Client.renderer.domElement.ondragleave = () => this.revertImportPreview();
      Client.renderer.domElement.addEventListener("dragleave", () => {
        this.revertImportPreview();
      });

      //drop to add the import entity
      Client.renderer.domElement.addEventListener("drop", (event) => {
        event.preventDefault();

        this.revertImportPreview();

        this.dropImport(event);
      });

      // special case for the camera
      Client.scene.add(this.camera);
    } else {
      // build cones to represent the remote user
      this.buildLocationCone();
    }

    this.setRootNode(this.root);

    window.addEventListener("resize", () => this.onResize());
  }
  dragSelectControls() {
    return this._dragSelectControls;
  }

  public override destroy() {
    super.destroy();
    this.userPresenceIndicators?.dispose();
    this.selectEntities([], EntitySelectionMode.REPLACE);
  }

  public setTransformControlsVisibility(visible: boolean) {
    const count = this._transformControlsPool.count();
    for (let i = 0; i < count; i++) {
      const controls = this._transformControlsPool.item(i);
      if (controls) {
        // End any transactions before we make the object invisible, since it is no longer possible to interact with it
        if (!visible) controls.endTransformInteraction();

        controls.visible = visible;
      }
    }
  }

  //#endregion
  //#region update

  public override update(time: number, deltaTime: number) {
    if (this.isLocal && this.camera) {
      this.setPosition(this.camera.position);
      this.setOrientation(this.camera.quaternion);
    }
    super.update(time, deltaTime);
  }
  //#endregion
  //#region selection

  protected compareEntities(entities: GameEntity[]) {
    const selectedEntities = this.getSelectedEntities();

    // if different lengths, not equal
    if (entities.length !== selectedEntities.length) return false;

    // if any entity is different, not equal
    for (let i = 0; i < entities.length; i++) {
      if (selectedEntities.indexOf(entities[i]) !== -1) return false;
    }

    // if any selected entity is different, not equal
    for (let i = 0; i < selectedEntities.length; i++) {
      if (entities.indexOf(selectedEntities[i]) === -1) return false;
    }

    return true;
  }

  public getSelectedEntities(): GameEntity[] {
    // TODO: investigate ways to use the selectedEntities that does not create an array every time
    // return Array.from(this._selectedEntities);
    return this._selectedEntities.get();
  }
  public hasSelectedEntities(): boolean {
    return this._selectedEntities.size > 0;
  }
  public deselectEntity(entity: GameEntity, fromLive = false) {
    if (this._selectedEntities.has(entity)) {
      if (this.isLocal) {
        entity.onDeselect();
      }

      const newSelection = this._selectedEntities.clone();
      newSelection.delete(entity);
      if (this.isLocal) {
        this.updateSelectionOrAction(newSelection);

        if (!fromLive) {
          this.netShouldUpdate = true;
        }
      } else {
        this._selectedEntities = newSelection;
        Client.events.emit("entity-select", this, this._selectedEntities.get());
      }
    }
  }

  public deselectAllEntities() {
    if (this.isLocal) {
      for (const entity of this._selectedEntities.get()) {
        entity.onDeselect();
      }
    }

    const newSelection = this._selectedEntities.clone();
    newSelection.clear();

    if (this.isLocal) {
      this.updateSelectionOrAction(newSelection);
      this.netShouldUpdate = true;
    } else {
      this._selectedEntities.clear();
      Client.events.emit("entity-select", this, this._selectedEntities.get());
    }
  }

  public selectEntities(
    entities: GameEntity[],
    selectionMode: EntitySelectionMode,
    _dirty = true
  ) {
    if (arrayEqual(entities, this.getSelectedEntities())) return;

    var newSelection = this._selectedEntities.clone();

    switch (selectionMode) {
      case EntitySelectionMode.REPLACE: {
        newSelection.set(entities);
        break;
      }
      case EntitySelectionMode.ADD: {
        for (const entity of entities) {
          newSelection.add(entity);
        }
        break;
      }
      case EntitySelectionMode.REMOVE: {
        for (const entity of entities) {
          // this.deselectEntity(entity);
          newSelection.delete(entity);
        }
        break;
      }
    }

    if (this.isLocal) {
      for (const entity of entities) {
        if (this._selectedEntities.has(entity)) {
          entity.onSelect();
        } else {
          entity.onDeselect();
        }
      }
      this.updateSelectionOrAction(newSelection);
    } else {
      this._selectedEntities = newSelection;
      Client.events.emit("entity-select", this, this._selectedEntities.get());
    }

    // If selected entities count === 1 and collaboartive editor is opened, then update it
    if (this.isLocal && Client.isCollaborativeEditorOpen()) {
      // Without setTimeout(async) it has a weird behavior, selection box just didn't apply to the selected entity
      setTimeout(() => {
        const selectedEntitties = this.getSelectedEntities();
        if (selectedEntitties.length) {
          Client.openCollaborativeEditor(selectedEntitties[0].id);
        }
      }, 0);
    }

    if (this.isLocal && _dirty) {
      this.netShouldUpdate = true;
      Client.markEntityAsDirty(this);
    }
  }

  //#endregion
  //#region network

  public static override readonly SCHEMA = userDataSchema;
  public static override readonly UNIQUE_ID = makeUniqueKey("UserEntity");

  public override deserialize(data: UserData) {
    this.deserializeTransform(data.transform);
    // TODO: serializing user-specific stuff goes here

    this.setTargetEntity(
      data.targetEntity ? Client.getEntity(data.targetEntity) : null,
      false
    );

    this.setColor(data.color, false);

    // update profile info
    if (this.userPresenceIndicators) {
      const { avatarUrl, displayName } = data.profile;

      // avatar should be initialized, then kept
      if (avatarUrl) {
        this.userPresenceIndicators.setAvatarImageUrl(avatarUrl);
      }

      // may be empty but that's alright
      this.userPresenceIndicators.setLabel(displayName ?? "");
    }

    // update user selection
    const prevSelection = this.getSelectedEntities();
    const newSelection = Client.getEntities(data.selectedEntities);
    if (!arrayEqual(prevSelection, newSelection)) {
      this.selectEntities(newSelection, EntitySelectionMode.REPLACE);
    }
  }

  public override serialize(): UserData {
    // convert optional user profile with optional types to line format
    const profile = {
      username: Client.myUserProfileInfo?.username || null,
      displayName: Client.myUserProfileInfo?.displayName || null,
      avatarUrl: Client.myUserProfileInfo?.avatarUrl || null,
      bio: Client.myUserProfileInfo?.bio || null,
    };

    return {
      transform: this.serializeTransform(),
      color: this._color,
      selectedEntities: this._selectedEntities.get().map((e, _idx) => e.id),
      targetEntity: this.targetEntity?.id || null,
      profile,
    };
  }

  //#endregion
  //#region json

  public override toJSON() {
    return {
      ...super.toJSON(),
      color: this._color,
      userProfileInfo: Client.myUserProfileInfo,
      selectedEntities: this.getSelectedEntities().map((e) => e.id),
      targetEntity: this.targetEntity?.id ?? null,
    };
  }

  public override fromJSON(json: GameEntityData) {
    const prevSelectedEntities = this.getSelectedEntities();
    super.fromJSON(json);

    // Type assertion to ensure we're working with UserEntityData
    if (json.type !== EntityType.UserEntity) {
      throw new Error(
        `UserEntity.fromJSON: Invalid data type - expected UserEntityData`
      );
    }

    const userData = json as UserEntityData;
    this.setColor(userData.color, false);

    if (this.userPresenceIndicators && userData.userProfileInfo) {
      const { avatarUrl, displayName } = userData.userProfileInfo;
      if (avatarUrl) {
        this.userPresenceIndicators.setAvatarImageUrl(avatarUrl);
      }
      if (displayName) {
        this.userPresenceIndicators.setLabel(displayName);
      } else {
        this.userPresenceIndicators.setLabel("");
      }
    }

    this.setTargetEntity(
      json.targetEntity ? Client.getEntity(json.targetEntity) : null,
      false
    );

    // select entities after they have been created in case of batched update
    // if (!this.local) {
    Promise.resolve().then(() => {
      const selectedEntities = Client.getEntities(
        userData.selectedEntities.map((id) => id)
      );
      if (!arrayEqual(selectedEntities, prevSelectedEntities))
        this.selectEntities(
          selectedEntities,
          EntitySelectionMode.REPLACE,
          false
        );
      // this.dirty = false;
    });
    // }
  }
  //#endregion
  //#region camera

  public getCamera() {
    return this.camera;
  }

  public getCameraControls() {
    return this.cameraControls;
  }

  private onFovChange() {
    if (!this.isLocal) {
      if (this.camera) this.camera.fov = this._fov;
    } else {
      // TODO:
      // fov should be changeable without rebuilding the cone
      // but not a high priority for now, as we don't expect users to change fov often
      // or for the change to be noticeable.
      // And when/if we do implement this, make it so that it updates properties/uniforms of the volumetric cones,
      // and not create a new one.
    }
  }

  public addCameraControls() {
    if (!this.camera) return;
    this.cameraControls = new CameraControls(
      this.camera,
      Client.renderer.domElement
    );

    // Add CameraComponent to ECS world so the CameraControlsSystem can update it
    if (this.entity) {
      this.data().addComponent(CameraComponent, this.cameraControls);
    }
  }
  //#endregion
  private onColorChange(prevColor: number) {
    this.userPresenceIndicators?.setColor(this._color);
    Client.composer.passes.outline.setSelectedObjects(prevColor, []);
    Client.composer.passes.outline.setSelectedObjects(
      this._color,
      this.getSelectedEntities() as (
        | PrimitiveEntity
        | MeshEntity
        | ParticleEntity
      )[]
    );
  }
  public setLocationConeVolumetricOpacity(opacity: number) {
    this.userPresenceIndicators?.setOpacity(opacity);
  }
  public setLocationConeVolumetricVisible(visible: boolean) {
    this.userPresenceIndicators?.setVisible(visible);
  }
  public setLocationConeVolumetricControlsType(
    controlsType: PlayerControlsType | null
  ) {
    this.userPresenceIndicators?.setControlsType(controlsType);
  }
  public setLocationConeVolumetricTargetEntity(entity: MeshEntity | null) {
    this.userPresenceIndicators?.setTargetEntity(entity);
  }
  //#region location cone
  // cone representing (non local) users location
  private buildLocationCone() {
    if (this.userPresenceIndicators) {
      return;
    }
    if (this.isLocal) {
      return;
    }
    this.userPresenceIndicators = new UserPresenceIndicators();
    this.userPresenceIndicators.setColor(this._color);

    this.root.add(this.userPresenceIndicators.groupCone);
  }
  //#endregion

  //#region events
  public onResize() {
    if (this.camera) {
      //Client.container.clientWidth, <- doesn't work properly in chrome (linux)
      //Client.container.clientHeight <- doesn't work properly in chrome (linux)
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
    }
  }

  private onKeyDown = async (event: KeyboardEvent) => {
    // Check if there's a configured keyboard action for this key combination
    const action = getKeyboardAction(event);

    if (action) {
      // Handle actions from centralized configuration
      switch (action) {
        case KeyboardMappingIDs.SAVE_CAMERA_SETTINGS: {
          event.preventDefault();
          const camera = this.getCamera() as PerspectiveCamera;
          const controls = (
            this.getCameraControls() as CameraControls
          ).getControls();
          camera.userData.data = {
            up: camera.up.toArray(),
            p: camera.position.toArray(),
            q: camera.quaternion.toArray(),
            s: camera.scale.toArray(),
            pm: camera.projectionMatrix.toArray(),
            t: controls.target.toArray(),
          };

          // Save camera settings to Firestore
          if (Client.room) {
            await RoomDataService.updateRoomData(Client.room.id, {
              cameraSettings: {
                position: camera.position.toArray() as [number, number, number],
                quaternion: camera.quaternion.toArray() as [
                  number,
                  number,
                  number,
                  number,
                ],
                target: controls.target.toArray() as [number, number, number],
              },
            });
            console.debug("📸 Saved camera settings to Firestore");
          }
          break;
        }
        case KeyboardMappingIDs.LOAD_CAMERA_SETTINGS: {
          event.preventDefault();
          const camera = this.getCamera() as PerspectiveCamera;
          const controls = (
            this.getCameraControls() as CameraControls
          ).getControls();
          if (camera.userData.data) {
            camera.up.fromArray(camera.userData.data.up);
            camera.position.fromArray(camera.userData.data.p);
            camera.quaternion.fromArray(camera.userData.data.q);
            camera.scale.fromArray(camera.userData.data.s);
            camera.projectionMatrix.fromArray(camera.userData.data.pm);
            controls.target.fromArray(camera.userData.data.t);
            controls.update();
          }
          break;
        }
        case KeyboardMappingIDs.TOGGLE_ENTITY_LOCK: {
          event.preventDefault();
          const selectedEntities = this.getSelectedEntities();
          if (selectedEntities.length > 0) {
            // Toggle lock state for all selected entities
            const firstEntity = selectedEntities[0];
            const shouldLock = !firstEntity.getLocked();
            selectedEntities.forEach((entity) => {
              entity.setLocked(shouldLock);
            });
            console.debug(
              `${shouldLock ? "🔒" : "🔓"} ${
                selectedEntities.length
              } entities ${shouldLock ? "locked" : "unlocked"}`
            );
          }
          break;
        }
        case KeyboardMappingIDs.SCENE_UNDO: {
          undo();
          break;
        }
        case KeyboardMappingIDs.SCENE_REDO: {
          redo();
          stopAllEntities();
          break;
        }
        case KeyboardMappingIDs.SAVE_ROOM_PREVIEW: {
          event.preventDefault();
          saveRoomLegacyPreview(Client.room.id)
            .then(() => {
              alert("Room preview saved");
            })
            .catch((error: unknown) => {
              alert("Error saving room preview");
              console.error(error);
            });
          break;
        }
        case KeyboardMappingIDs.TOGGLE_PHYSICS: {
          event.preventDefault();
          this.togglePhysicsForSelectedEntities();
          break;
        }
        case KeyboardMappingIDs.GLOBAL_DISMISS: {
          // deselect on escape press (while not in a text field)
          // TODO: only do this when pointer not over an entity
          if (
            document.activeElement?.getAttribute("role") !== "textbox" &&
            document.activeElement?.tagName !== "INPUT"
          ) {
            if (this.currentAction() !== CurrentAction.TRANSFORM_CONTROLS) {
              this.abortCurrentAction();
              return;
            } else {
              this.deselectAllEntities();
            }
          }
          break;
        }
        case KeyboardMappingIDs.DELETE_SELECTED_ENTITIES: {
          selectedEntitiesDelete();
          break;
        }
        case KeyboardMappingIDs.COMMIT_CURRENT_ACTION: {
          this.commitCurrentAction();
          break;
        }
        case KeyboardMappingIDs.DUPLICATE_ENTITIES: {
          if (this.camera) {
            event.preventDefault(); // prevent browser bookmarks from opening up with Ctrl+D

            if (this.draggedEntity) {
              // End current drag transaction to make duplication its own undo operation
              this.simpleMoveTransaction?.end();
              this.simpleMoveTransaction = null;

              // When dragging, duplicate all selected entities if the dragged entity is part of selection
              const selectedEntities = this.getSelectedEntities();
              const entitiesToDuplicate = selectedEntities.includes(
                this.draggedEntity
              )
                ? selectedEntities
                : [this.draggedEntity];
              DuplicateControlsWhileDragging.commit(entitiesToDuplicate);

              // Start a new drag transaction for continued dragging
              this.simpleMoveTransaction = beginTransaction("simple-move");
            } else {
              DuplicateControlsInstant.commit(
                this.getSelectedEntities(),
                this.camera
              );
            }
          }
          break;
        }
        case KeyboardMappingIDs.FOCUS_ENTITIES: {
          if (!this.camera || !this.cameraControls) {
            return;
          }

          const selectedEntities = this.getSelectedEntities();
          if (selectedEntities.length === 0) {
            return;
          }
          this.focusEntities(selectedEntities);
          break;
        }
        case KeyboardMappingIDs.TOGGLE_PARTICLE_GIZMOS: {
          event.preventDefault();
          const query = new Query([GameEntityComponent]);
          query.forEach(Client.world, (_, [entity]) => {
            if (entity instanceof ParticleEntity) {
              if (this._showGizmos) {
                entity.hideGizmo();
              } else {
                entity.showGizmo();
              }
            }
          });

          this._showGizmos = !this._showGizmos;
          break;
        }
        case KeyboardMappingIDs.RESET_CAMERA: {
          if (!this.camera) {
            return;
          }
          if (!this.cameraControls) {
            return;
          }

          const camera = this.cameraControls.getCamera();
          const controls = this.cameraControls.getControls();

          const dest = new Vector3().copy(DEFAULT_ORIGIN);
          const target = new Vector3();

          gsap.to(camera.position, {
            ...dest,
            duration: 0.5,
          });

          gsap.to(controls.target, target);
          break;
        }
        case KeyboardMappingIDs.TOGGLE_TRANSFORM_SPACE: {
          // removed due to space is affected by physics now
          const firstSelected = this.getSelectedEntities()[0];
          const space = firstSelected?.getTransformControlsSpace();
          firstSelected?.setTransformControlsSpace(
            space == GIZMOSPACE.LOCAL ? GIZMOSPACE.GLOBAL : GIZMOSPACE.LOCAL
          );
          break;
        }
        case KeyboardMappingIDs.ROTATE_CLOCKWISE: {
          event.preventDefault();
          this.rotateSelectedEntities(Math.PI / 4);
          break;
        }
        case KeyboardMappingIDs.ROTATE_COUNTERCLOCKWISE: {
          event.preventDefault();
          this.rotateSelectedEntities(-Math.PI / 4);
          break;
        }
        case KeyboardMappingIDs.CYCLE_SELECTION: {
          const selected = Client.userEntity.getSelectedEntities();
          if (selected.length > 1) {
            const first = selected[0];
            selected.shift();
            selected.push(first);
            Client.userEntity.selectEntities(
              selected,
              EntitySelectionMode.REPLACE
            );
          }
          break;
        }
        case KeyboardMappingIDs.TOGGLE_RADIAL_MENU: {
          if (!Client.playerControls.getActivePlayerControlsType()) {
            this.toggleRadial(this.pointerLocation);
          }
          break;
        }
      }
    } else {
      switch (event.code) {
        case "ShiftRight":
        case "ShiftLeft": {
          this.shiftPressed = true;
          break;
        }
      }
    }
  };

  private togglePhysicsForSelectedEntities = () => {
    const selectedEntities = this.getSelectedEntities();

    const entitiesWithPhysics = selectedEntities.filter((entity) => {
      return entity.data().hasComponent(PhysicsBodyInfoComponent);
    });

    if (entitiesWithPhysics.length === 0) {
      return;
    }

    // Use the first entity's physics state to determine the toggle direction for all entities
    const firstEntity = entitiesWithPhysics[0];
    const firstEntityBodyControl = firstEntity.getPhysicsBodyControl();
    const firstEntityIsStatic =
      firstEntityBodyControl?.getPhysicsOptions().isStatic ?? false;
    const shouldMakeStatic = !firstEntityIsStatic;

    Client.room.batch(() => {
      entitiesWithPhysics.forEach((entity) => {
        if (entity.getLocked()) {
          return;
        }
        runCommands(
          new SetEntityPhysicsFlagsCommand(entity.id, shouldMakeStatic)
        );
      });
    });
  };

  private onKeyUp = (event: KeyboardEvent) => {
    switch (event.code) {
      case "ShiftRight":
      case "ShiftLeft": {
        this.shiftPressed = false;
        break;
      }
    }
  };

  private _radialMenuDisplayedOnPointerdown: boolean = false;
  private _radialMenuDisplayedOnPointerup: boolean = false;
  private onPointerDown = (event: PointerEvent) => {
    // using window not Client.container due to the latter measuring incorrectly on chrome (linux)
    const selectedEntities = this.getSelectedEntities();
    this.pointerDownLocation.set(event.clientX, event.clientY);

    const pointerDownLocationThreeScreenSpace = this.getNormalizedCoordinates(
      this.pointerDownLocation.x,
      this.pointerDownLocation.y
    );
    const intersections: Intersection[] = [];
    const entity = Client.raycastObject(
      pointerDownLocationThreeScreenSpace,
      intersections
    );

    // Track the clicked entity for potential unlock, regardless of lock status
    if (
      entity &&
      entity instanceof GameEntity &&
      intersections.length > 0 &&
      isBitSet(event.buttons, 0)
    ) {
      this.lastClickedEntity = entity;
    }

    // Set up essential state that pointerUp relies on - must happen before any early returns
    this.pointerDownTime = event.timeStamp;
    this._radialMenuDisplayedOnPointerdown = this.isRadialOpened();

    // Try transform controls as the first interaction control
    const transformControls = this.getCurrentTransformControls();
    if (transformControls) {
      const didHandle = transformControls.handlePointerDown(event);

      // Prevent handling of the event by the simple-move
      if (didHandle) {
        // Prevent further handling of this event for the document body element.
        event.stopImmediatePropagation();
        event.preventDefault();

        // Just in case, always clear drag marker
        if (entity instanceof GameEntity) entity.removeDragMarker();

        this.draggedObject = null;
        this.draggedEntity = null;
        return;
      }
    }

    // If transform controls didn't handle it, try simple-move
    if (
      entity &&
      entity instanceof GameEntity &&
      intersections.length > 0 &&
      isBitSet(event.buttons, 0) &&
      !event.ctrlKey &&
      !entity.getLocked()
    ) {
      checkNull(
        this.simpleMoveTransaction,
        "Attempt to start a new drag transaction while one is active"
      );
      this.simpleMoveTransaction = beginTransaction("simple-move");
      this.getCameraControls()?.disableControls();

      this.draggedObject = intersections[0].object as Mesh;

      this.draggedEntity = entity;
      this.lastDraggedEntity = entity; // Track for potential unlock
      entity.addDragMarker();
      document.body.style.cursor = "grabbing";

      this.lastTimestamp = event.timeStamp;
      entity.getSimulatedPosition(this.dragPosition);
      this.draggedOnDownPosition.copy(this.dragPosition);

      // this allows us to undo to initial position
      runCommands([
        new SetEntityPositionCommand(entity.id, this.dragPosition.clone()),
      ]);

      // Update drag plane based on mode and calculate intersection
      if (selectedEntities.includes(this.draggedEntity)) {
        this.draggedOnDownPositions = selectedEntities.map((e) => {
          e.getSimulatedPosition(position);
          // this allows us to undo to initial position
          runCommands([new SetEntityPositionCommand(e.id, position.clone())]);
          return position.clone();
        });
        selectedEntities.map((e) => {
          e.getPhysicsBodyControl()?.removeFromWorld();
        });
      } else {
        entity.getPhysicsBodyControl()?.removeFromWorld();
      }

      dragPlane.copy(updateDragPlane(intersections[0].point));
      if (this.draggedEntity) {
        this.draggedEntity.updateDragMarker();
      }

      offset.copy(this.dragPosition).sub(intersections[0].point);
    } else {
      if (entity instanceof GameEntity) entity.removeDragMarker();

      this.draggedObject = null;
      this.draggedEntity = null;
    }
  };

  private onMouseDown = (event: MouseEvent) => {
    if (event.button === 2) {
      this.RMBPressed = true;

      // Check for double right-click when left mouse button is being held
      const currentTime = Date.now();
      const timeDiff = currentTime - this.lastRightClickTime;

      if (timeDiff < this.doubleRightClickThreshold) {
        // Double right-click detected while holding left mouse button
        const targetEntity =
          this.draggedEntity ||
          this.lastDraggedEntity ||
          this.lastClickedEntity;
        if (targetEntity) {
          const wasLocked = targetEntity.getLocked();
          targetEntity.toggleLocked();
          console.debug(
            `${targetEntity.getLocked() ? "🔒" : "🔓"} Entity ${
              targetEntity.getLocked() ? "locked" : "unlocked"
            } via double right-click (was ${wasLocked ? "locked" : "unlocked"})`
          );
        } else {
          console.debug("No target entity for double right-click");
        }
      }

      this.lastRightClickTime = currentTime;
    }
  };

  private onPointerMove = (event: PointerEvent) => {
    this.pointerSpeed
      .set(event.clientX, event.clientY)
      .sub(this.pointerLocation)
      .multiplyScalar(1 / (event.timeStamp - this.lastTimestamp));
    this.pointerLocation.set(event.clientX, event.clientY);
    this.getNormalizedCoordinates(
      this.pointerLocation.x,
      this.pointerLocation.y,
      this.pointerLocationThreeScreenSpace
    );
    const intersections: Intersection[] = [];
    const entity = Client.raycastEntity(
      this.pointerLocationThreeScreenSpace,
      intersections
    );

    // Handle hovering of entities
    if (
      event.pointerType === "mouse" &&
      !this.draggedEntity &&
      !this.draggedObject
    ) {
      if (
        entity &&
        (entity instanceof PrimitiveEntity ||
          entity instanceof MeshEntity ||
          entity instanceof ParticleEntity)
      ) {
        Client.composer.passes.outline.setHoveredEntity(
          Client.composer.options.hoverVisible && !isMultiSelectionActive
            ? entity
            : null
        );
      } else {
        Client.composer.passes.outline.setHoveredEntity(null);
      }
    }

    // Handle transform controls if attached
    const transformControls = this.getCurrentTransformControls();
    if (transformControls) {
      const didHandle = transformControls.handlePointerMove(event);

      if (didHandle) {
        event.preventDefault();

        checkNull(
          this.draggedEntity,
          "Did not expect to find dragged entity when handling transform movements controls"
        );
      }
    }

    // Handle dragging of entities
    if (!this.draggedEntity || !this.draggedObject) return;

    // Check if the dragged entity is locked - if so, stop dragging
    if (this.draggedEntity.getLocked()) {
      this.draggedEntity.removeDragMarker(); // EDIT: Remove drag marker when entity gets locked
      this.draggedObject = null;
      this.draggedEntity = null;
      document.body.style.cursor = "auto";
      this.getCameraControls()?.enableControls();
      return;
    }

    if (intersections.length) {
      dragPlane.copy(updateDragPlane(intersections[0].point));
    }

    _raycaster.setFromCamera(
      this.pointerLocationThreeScreenSpace,
      this.getCamera() as PerspectiveCamera
    );

    // Update drag plane and intersection for current mode
    this.draggedEntity.getSimulatedPosition(position);

    // here intersection always tracks last successful intersection to a plane
    // but planeIntersection represents last intersection attempt result and can be null
    const prevIntersection = intersection.toArray();
    const tempIntersection = _raycaster.ray.intersectPlane(
      dragPlane,
      intersection
    );

    const distance = intersection.distanceTo(this.draggedOnDownPosition);
    const planeIntersection =
      distance > MAX_SIMPLE_DRAG_HORIZONTAL_DISTANCE ? null : tempIntersection;
    if (distance > MAX_SIMPLE_DRAG_HORIZONTAL_DISTANCE) {
      intersection.fromArray(prevIntersection);
    }
    this.draggedEntity.getPosition(this.dragPosition);

    const draggedEntityPosition = intersection.clone().add(offset);
    //if pointer went outside of horizontal helper plane
    if (!planeIntersection) {
      secondaryDragPlane.copy(updateDragPlane(intersection, true));

      const secondaryPlaneIntersection = _raycaster.ray.intersectPlane(
        secondaryDragPlane,
        secondaryIntersection
      );
      if (secondaryPlaneIntersection) {
        draggedEntityPosition.copy(secondaryIntersection.clone().add(offset));
      }
    }

    this.dragSpeed
      .copy(draggedEntityPosition)
      .sub(this.dragPosition)
      .divideScalar((event.timeStamp - this.lastTimestamp) / 1000);
    this.lastTimestamp = event.timeStamp;

    this.draggedEntity.setPosition(draggedEntityPosition);
    const selectedEntities = this.getSelectedEntities();
    if (selectedEntities.includes(this.draggedEntity))
      selectedEntities.forEach((e, i) => {
        if (e !== this.draggedEntity && !e.getLocked()) {
          e.setPosition(
            this.draggedOnDownPositions[i]
              .clone()
              .add(draggedEntityPosition)
              .sub(this.draggedOnDownPosition)
          );
        }
      });
    if (this.draggedEntity) {
      this.draggedEntity.updateDragMarker();
    }
  };

  private processEntitySelection(
    event: PointerEvent | MouseEvent,
    location: Vector2
  ) {
    const prevSelectedEntities = this.getSelectedEntities();
    const screenPos = this.getNormalizedCoordinates(location.x, location.y);
    const clickedEntity = this.getClickedEntity(screenPos);

    if (clickedEntity) {
      this.handleEntityClick(
        clickedEntity,
        event.ctrlKey,
        prevSelectedEntities
      );
    } else {
      this.handleEmptySpaceClick(event.ctrlKey, prevSelectedEntities);
    }

    this.cleanupPromptEntity(clickedEntity);
    this.updateNetworkIfSelectionChanged(prevSelectedEntities);
  }

  private getClickedEntity(screenPos: Vector2): GameEntity | null {
    const object = Client.raycastObject(screenPos);
    return object instanceof GameEntity ? object : null;
  }

  private handleEntityClick(
    entity: GameEntity,
    ctrlPressed: boolean,
    prevSelected: GameEntity[]
  ) {
    const isSelected = prevSelected.includes(entity);

    if (ctrlPressed) {
      this.toggleEntitySelection(entity, isSelected, prevSelected);
    } else {
      this.selectSingleEntity(entity, isSelected, prevSelected);
    }
  }

  private toggleEntitySelection(
    entity: GameEntity,
    isSelected: boolean,
    prevSelected: GameEntity[]
  ) {
    if (isSelected) {
      // Remove from selection
      const newSelection = prevSelected.filter((e) => e !== entity);
      runCommands(
        new SelectEntitiesCommand(
          newSelection.map((e) => e.id),
          EntitySelectionMode.REPLACE
        )
      );
    } else {
      // Add to selection
      runCommands(
        new SelectEntitiesCommand([entity.id], EntitySelectionMode.ADD)
      );
    }
  }

  private selectSingleEntity(
    entity: GameEntity,
    isSelected: boolean,
    prevSelected: GameEntity[]
  ) {
    // Don't re-select if this entity is already the sole selection
    if (prevSelected.length === 1 && isSelected) {
      return;
    }

    runCommands(
      new SelectEntitiesCommand([entity.id], EntitySelectionMode.REPLACE)
    );
  }

  private handleEmptySpaceClick(
    ctrlPressed: boolean,
    prevSelected: GameEntity[]
  ) {
    // Deselect all unless ctrl is pressed
    if (!ctrlPressed && prevSelected.length > 0) {
      runCommands(new SelectEntitiesCommand([], EntitySelectionMode.REPLACE));
    }
  }

  private cleanupPromptEntity(clickedEntity: GameEntity | null) {
    if (!this.promptEntity) return;

    const shouldCleanup =
      clickedEntity !== this.promptEntity ||
      this.getSelectedEntities().length === 0; // Also cleanup if nothing is selected

    if (shouldCleanup && Client.world.isAlive(this.promptEntity.id)) {
      runCommands(new RemoveEntityCommand(this.promptEntity.id), false);
      this.promptEntity = null;
    }
  }

  private updateNetworkIfSelectionChanged(prevSelected: GameEntity[]) {
    const currentSelected = this.getSelectedEntities();

    // Fast exit clause if length is different the selection has to be different
    if (currentSelected.length !== prevSelected.length) {
      this.netShouldUpdate = true;
      return;
    }

    // If any entity is different the selection has to be different
    for (let i = 0; i < currentSelected.length; i++) {
      if (currentSelected[i].id !== prevSelected[i].id) {
        this.netShouldUpdate = true;
        return;
      }
    }
  }

  private onPointerUp = async (event: PointerEvent) => {
    // using window not Client.container due to the latter measuring incorrectly on chrome (linux)
    this.pointerUpLocation.set(event.clientX, event.clientY);
    const pointerMoved =
      this.pointerDownLocation.distanceTo(this.pointerUpLocation) >=
      DISTANCE_TO_SKIP_SELECTION;

    prevType = PrevTypes.NONE;

    // Handle transform controls if attached
    const transformControls = this.getCurrentTransformControls();
    if (transformControls) {
      const didHandle = transformControls.handlePointerUp(event);
      if (didHandle) {
        // Prevent other elements from handling the event
        event.stopPropagation();

        checkNull(
          this.draggedEntity,
          "Did not expect to find dragged entity when handling transform movements controls"
        );
      }
    }

    // Handle pointer release and commit of dragged entity
    // TODO: this should be another function or class altogether, for code organization
    const selectedEntities = this.getSelectedEntities();
    if (this.draggedEntity) {
      if (selectedEntities.includes(this.draggedEntity)) {
        selectedEntities.forEach((e) => {
          e.getSimulatedPosition(position);
          runCommands([new SetEntityPositionCommand(e.id, position.clone())]);
        });
      } else {
        this.draggedEntity.getSimulatedPosition(position);
        runCommands([
          new SetEntityPositionCommand(this.draggedEntity.id, position.clone()),
        ]);
      }
      [...selectedEntities, this.draggedEntity].forEach((e) => {
        const bodyControl = e.getPhysicsBodyControl();
        bodyControl?.addToWorld();
        e.setDragging(false);
      });
      this.draggedEntity.removeDragMarker();
      document.body.style.cursor = "grab";
    } else {
      const intersections: Intersection[] = [];
      const entity = Client.raycastEntity(
        this.pointerLocationThreeScreenSpace,
        intersections
      );
      if (
        event.pointerType === "mouse" &&
        !this.draggedEntity &&
        !this.draggedObject
      ) {
        if (
          entity &&
          (entity instanceof PrimitiveEntity ||
            entity instanceof MeshEntity ||
            entity instanceof ParticleEntity)
        ) {
          Client.composer.passes.outline.setHoveredEntity(
            Client.composer.options.hoverVisible ? entity : null
          );
        } else {
          Client.composer.passes.outline.setHoveredEntity(null);
        }
      }
    }
    this.draggedEntity = null;
    this.draggedObject = null;
    this.simpleMoveTransaction?.end();
    this.simpleMoveTransaction = null;

    this.getCameraControls()?.enableControls();

    DuplicateControlsWhileDragging.clearAllDuplicationPositions();

    // Clear lastDraggedEntity and lastClickedEntity after a delay to allow for double right-click unlock
    setTimeout(() => {
      this.lastDraggedEntity = null;
      this.lastClickedEntity = null;
    }, this.doubleRightClickThreshold + 100);

    // pass pointer up event to the radial menu
    this._radialMenuDisplayedOnPointerup =
      UIManager.instance.hasPanel(RADIAL_PANEL_ID);

    // Check if this is pen input
    // Some browsers/devices might report pen input differently:
    // - Standard: pointerType === "pen"
    // - Some touchscreens with stylus: pointerType === "touch" with buttons === 2 (right button)
    const isPenInput =
      event.pointerType === "pen" ||
      (event.pointerType === "touch" && event.isPrimary && event.buttons === 2);

    if (
      pointerMoved === false &&
      pointerEventButton(event) == PointerEventButton.RIGHT &&
      !isPenInput // Disable right-click radial menu for pen input
    ) {
      if (!Client.playerControls.getActivePlayerControlsType()) {
        if (!isMobile()) {
          if (!this.isRadialOpened()) {
            try {
              // Select what is under the cursor before opening the radial menu
              const location = this.getNormalizedCoordinates(
                this.pointerUpLocation.x,
                this.pointerUpLocation.y
              );
              const object = Client.raycastObject(location);

              // Don't reset selection if we hit empty space or a non-GameEntity object
              if (object && object instanceof GameEntity) {
                this.processEntitySelection(event, this.pointerUpLocation);
              }
            } finally {
              this.openRadial(this.pointerUpLocation);
            }
          }
        }
      }
      return;
    }

    // on iphone, pointerMoved appears to always be false (I haven't investigated why for now).
    // So we have another check here before doing the raycast below,
    // which is to check is the radial menu just appeared.
    // We consider it as "just appeared" if it was hidden during the pointerdown, and is display in the pointerup (this function).
    // If that is the case, we return early.
    const radialMenuJustAppeared =
      this._radialMenuDisplayedOnPointerdown === false &&
      this._radialMenuDisplayedOnPointerup === true;
    if (radialMenuJustAppeared) {
      return;
    }

    // if pointer hasn't moved and the target isn't a text field or button etc
    // raycast entity selection from cursor
    // regardless of mode, we should still allow select in generate
    const pointerOverCanvas =
      (event.target as HTMLCanvasElement).tagName === "CANVAS";
    if (!pointerMoved && pointerOverCanvas) {
      this.processEntitySelection(event, this.pointerUpLocation);
    }

    const touchedCanvas = event.target === Client.renderer.domElement;
    if (touchedCanvas && pointerEventButton(event) == PointerEventButton.LEFT) {
      this.OnSceneClick.invoke(this.pointerUpLocation);
    }
  };

  private onMouseUp = (event: MouseEvent) => {
    if (event.button === 2) {
      this.RMBPressed = false;
    }
  };
  //#endregion
  //#region generate

  private _getNewEntityPosition() {
    return Client.raycastScreen(new Vector2(0, 0));
  }

  beginGenerateToolbar(type: GenerationType) {
    const intersectionPoint = this._getNewEntityPosition();
    if (intersectionPoint) {
      if (type === "text_to_model")
        this.addPromptEntity(intersectionPoint, false);
      else if (type === "image_to_model")
        this.getImageFromUser(intersectionPoint, false);
    }
  }

  openAIModelBuilder() {
    // Emit event to open AI Model Builder popover
    Client.events.emit("open-ai-model-builder");
  }

  beginGenerate(type: GenerationType) {
    const [intersectionPoint, vAngle] = this.getRadialMenuCoordinates();
    if (intersectionPoint) {
      if (type === "text_to_model")
        this.addPromptEntity(intersectionPoint, vAngle > ANGLE_STATIC);
      else if (type === "image_to_model")
        this.getImageFromUser(intersectionPoint, vAngle > ANGLE_STATIC);
    }
  }

  async generateFromImage(url: string) {
    const intersectionPoint = this._getNewEntityPosition();
    if (!intersectionPoint) {
      return;
    }

    try {
      const image = await fetch(url);
      const imageBlob = await image.blob();
      const imageFile = new File(
        [imageBlob],
        `${crypto.randomUUID()}.${getExtensionFromMimeType(imageBlob.type)}`,
        {
          type: imageBlob.type,
        }
      );

      this._generateModelFromImage(imageFile, intersectionPoint, true);
    } catch (error) {
      console.error("Error generating model from image:", error);
      toast.error(
        "Failed to generate model from image. Please try again later."
      );
    }
  }

  addPromptEntity(intersectionPoint: Vector3, isStatic: boolean) {
    // create generative prompt entity at intersection point

    const transaction = beginTransaction("add-prompt-entity");

    const camera = Client.userEntity.getCamera() as PerspectiveCamera;
    const q = new Quaternion().setFromUnitVectors(
      new Vector3(0, 0, 1),
      new Vector3(
        camera.position.x - intersectionPoint.x,
        0,
        camera.position.z - intersectionPoint.z
      ).normalize()
    );

    const prefab = createPromptEntityPrefab({
      position: intersectionPoint,
      rotation: q,
      scale: new Vector3(1, 1, 1),
      prompt: "",
      userId: Client.userEntity.id,
      color: this._color,
      isStatic,
    });

    const spawnCommand = new SpawnPrefabCommand(prefab, true /* select */);

    // Use callback to get the entity reference after spawning
    spawnCommand.onSpawnCallback((entity) => {
      this.promptEntity = entity.getComponent(
        GameEntityComponent
      ) as PromptEntity;
    });

    runCommands(
      spawnCommand,
      false /* do not allow undoing creation and selection */
    );

    transaction.end();
  }

  getImageFromUser(intersectionPoint: Vector3, isStatic: boolean) {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = ".png, .jpg, .jpeg, .webp";

    //only called when the user selects/deselects a file
    fileInput.addEventListener("change", async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];

      if (file) this._generateModelFromImage(file, intersectionPoint, isStatic);
    });

    fileInput.click();
  }
  //#endregion
  //#region primitive

  private _primitivePreviewEnabled: boolean = true;

  setPrimitivePreviewEnabled(value: boolean) {
    this._primitivePreviewEnabled = value;
  }

  /// @param {string} primitiveType - The type of primitive to preview
  /// @param {Vector3} previewPosition - Override position to preview the primitive at
  /// @returns {void}
  previewPrimitive(primitiveType: PrimitiveType, previewPosition?: Vector3) {
    if (!this._primitivePreviewEnabled) return;

    var intersectionPoint: Vector3 | null =
      previewPosition ?? this._getNewEntityPosition();

    if (!intersectionPoint) {
      return;
    }

    if (
      !this.previewPrimitiveEntity ||
      this.previewPrimitiveEntity.primitiveType != primitiveType
    ) {
      this.previewPrimitiveEntity = new PrimitiveEntity();
      this.previewPrimitiveEntity.setPreview(primitiveType);
    }

    Client.addEntityToWorld(this.previewPrimitiveEntity);

    this.previewPrimitiveEntity.onPreview(primitiveType, intersectionPoint);
  }

  revertPrimitive(primitiveType: string) {
    if (this.previewPrimitiveEntity)
      this.previewPrimitiveEntity.onRevert(primitiveType);
    this.previewPrimitiveEntity = null;
  }

  revertPrimitivePreviews(primitiveTypes: string[]) {
    if (this.previewPrimitiveEntity) {
      for (const type of primitiveTypes) {
        this.previewPrimitiveEntity.onRevert(type);
      }
      this.previewPrimitiveEntity = null;
    }
  }

  //#endregion
  //#region CurrentAction

  public setCurrentAction(value: CurrentAction) {
    if (this._currentAction == value) return;
    this._currentAction = value;
    this.updateSelectionOrAction(this._selectedEntities);
    Client.events.emit("current-action-change", value);

    switch (value) {
      case CurrentAction.DUPLICATE_CONTROLS:
        UIManager.instance.editPanel(this.getHintDuplicatePanelId(), {
          visible: true,
        });
        UIManager.instance.editPanel(this.getHintTransformPanelId(), {
          visible: false,
        });
        UIManager.instance.editPanel(this.getHintBrushPanelId(), {
          visible: false,
        });

        break;
      case CurrentAction.TRANSFORM_CONTROLS:
        UIManager.instance.editPanel(this.getHintDuplicatePanelId(), {
          visible: false,
        });
        UIManager.instance.editPanel(this.getHintTransformPanelId(), {
          visible: true,
        });
        UIManager.instance.editPanel(this.getHintBrushPanelId(), {
          visible: false,
        });
        break;
      case CurrentAction.BRUSHSTROKE_GRASS:
      case CurrentAction.BRUSHSTROKE_WALL:
      case CurrentAction.BRUSHSTROKE_WATER:
      case CurrentAction.BRUSHSTROKE_ERASER: {
        UIManager.instance.editPanel(this.getHintDuplicatePanelId(), {
          visible: false,
        });
        UIManager.instance.editPanel(this.getHintTransformPanelId(), {
          visible: false,
        });
        UIManager.instance.editPanel(this.getHintBrushPanelId(), {
          visible: true,
        });
        break;
      }
    }

    this.updateToolbar();
  }
  public currentAction() {
    return this._currentAction;
  }
  private _transformControlsPool = poolCreate<TransformControls>({
    createItem: transformControlsCreate,
  });
  private _duplicateControlsPool = poolCreate<DuplicateControls>({
    createItem: duplicateControlsCreate,
  });
  private __brushControlsHandler?: BrushControlsHandler;
  public brushControlsHandler() {
    this.__brushControlsHandler ??= createBrushControlsHandler({
      domElement: Client.renderer.domElement,
    });
    return this.__brushControlsHandler;
  }
  async loadBrushControls(): Promise<BrushControls> {
    return await this.brushControlsHandler().load();
  }
  public duplicateControls(index: number) {
    return this._duplicateControlsPool.item(index);
  }
  private updateSelectionOrAction(newSelection: SelectedEntities) {
    // Wait to update selection until current action is cancelled as it needs the old entities
    const newEntities = newSelection.get();

    if (newEntities.length == 0) {
      // if we are on a current action like the duplicate controls,
      // and we click away to de-select, we cancel the current action,
      // so that we go back to the transform controls.
      // update: although we don't want to cancel if we've just set the action to a brushstroke
      if (BRUSH_ACTIONS.includes(this._currentAction) == false) {
        this.cancelAndConfirmCurrentAction();
        // return;
      }
    }

    this.cameraControls?.enableControlsIfNotFirstPerson();

    // attach to new entities
    this._selectedEntities = newSelection;
    // Notify listeners, e.g; UI that selection changed
    Client.events.emit("entity-select", this, this._selectedEntities.get());

    switch (this._currentAction) {
      case CurrentAction.TRANSFORM_CONTROLS:
        {
          // It would be nice to have one transform control per entity,
          // but currently when we transform one, it will affect the rest.
          // so for now, we only have one transform control, for the last selected entity.
          // const attachOneTransformControlPerEntity = false;
          const controls = this._transformControlsPool.item(0);

          if (controls) {
            controls.attachEntities(newEntities);
          }
        }
        return;
      case CurrentAction.DUPLICATE_CONTROLS: {
        this._duplicateControlsPool.setCount(newEntities.length);
        for (let i = 0; i < newEntities.length; i++) {
          const controls = this._duplicateControlsPool.item(i);
          if (controls) {
            newEntities[i].attachDuplicateControls(controls);
            controls.visible = true;
          }
        }
        return;
      }
      case CurrentAction.BRUSHSTROKE_GRASS: {
        this.brushControlsHandler().enterBrushMode(BrushType.GRASS);
        return;
      }
      case CurrentAction.BRUSHSTROKE_WALL: {
        this.brushControlsHandler().enterBrushMode(BrushType.WALL);
        return;
      }
      case CurrentAction.BRUSHSTROKE_WATER: {
        this.brushControlsHandler().enterBrushMode(BrushType.WATER);
        return;
      }
      case CurrentAction.BRUSHSTROKE_ERASER: {
        this.brushControlsHandler().enterBrushMode(BrushType.ERASER);
        return;
      }
    }
    assertUnreachable(this._currentAction);
  }

  private exitCurrentAction(shouldCommit: boolean) {
    switch (this._currentAction) {
      case CurrentAction.DUPLICATE_CONTROLS: {
        if (shouldCommit) {
          DuplicateControls.detachAndCommit(
            this.getSelectedEntities(),
            this._duplicateControlsPool
          );
        } else {
          DuplicateControls.detach(
            this.getSelectedEntities(),
            this._duplicateControlsPool
          );
        }
        break;
      }
      case CurrentAction.BRUSHSTROKE_GRASS:
      case CurrentAction.BRUSHSTROKE_WALL:
      case CurrentAction.BRUSHSTROKE_WATER:
      case CurrentAction.BRUSHSTROKE_ERASER: {
        // Brush controls do not use pending/commit
        this.brushControlsHandler().exitBrushMode();
        break;
      }
    }
    this.setCurrentAction(CurrentAction.TRANSFORM_CONTROLS);
  }

  commitCurrentAction() {
    if (this._currentAction == CurrentAction.TRANSFORM_CONTROLS) {
      // we return early if we are in transform controls
      // as we don't want to deselect the entities
      return;
    }
    // this should be more generic,
    // and controls that are not immediate like transforms
    // should have a .commit() method
    if (this._currentAction == CurrentAction.DUPLICATE_CONTROLS) {
      const transaction = beginTransaction("duplicate-controls-commit");
      DuplicateControls.commit(
        this.getSelectedEntities(),
        this._duplicateControlsPool
      );
      this.setCurrentAction(CurrentAction.TRANSFORM_CONTROLS);
      // this.deselectAllEntities({ emit: true });
      runCommands(new SelectEntitiesCommand([], EntitySelectionMode.REPLACE));
      transaction.end();
    }
  }

  abortCurrentAction() {
    this.exitCurrentAction(false);
  }

  cancelAndConfirmCurrentAction() {
    this.exitCurrentAction(true);
  }

  public getCurrentTransformControls(): TransformControls | undefined {
    if (this._transformControlsPool.count() == 0) return undefined;
    return this._transformControlsPool.item(0);
  }

  public setTransfomControlCount(count: number) {
    this._transformControlsPool.setCount(count);
  }

  private rotateSelectedEntities(radians: number): void {
    const entitiesToRotate = this.getSelectedEntities().filter(
      (entity) => !entity.getLocked()
    );

    if (
      this.draggedEntity &&
      !this.draggedEntity.getLocked() &&
      !entitiesToRotate.includes(this.draggedEntity)
    ) {
      entitiesToRotate.push(this.draggedEntity);
    }

    if (entitiesToRotate.length === 0) return;

    const transformControls = this.getCurrentTransformControls();
    const hoveredAxis = transformControls?.getHoveredRotationAxis?.();
    const space = transformControls?.getSpace();

    // Use hovered axis if available, otherwise default to Y-axis
    const rotationAxis =
      hoveredAxis != null
        ? new Vector3(
            hoveredAxis === 0 ? 1 : 0,
            hoveredAxis === 1 ? 1 : 0,
            hoveredAxis === 2 ? 1 : 0
          )
        : new Vector3(0, 1, 0);

    const transaction = beginTransaction("rotate-entities-45");

    const currentOrientation = new Quaternion();
    for (const entity of entitiesToRotate) {
      entity.getOrientation(currentOrientation);

      const deltaRotation = new Quaternion().setFromAxisAngle(
        rotationAxis,
        radians
      );

      const newOrientation =
        space === GIZMOSPACE.GLOBAL
          ? deltaRotation.clone().multiply(currentOrientation)
          : currentOrientation.clone().multiply(deltaRotation);

      newOrientation.normalize();

      runCommands(new SetEntityOrientationCommand(entity.id, newOrientation));
    }

    transaction.end();
  }
  //#endregion

  //#region radial menu
  public getRadialMenuCoordinates(): [
    Readonly<Vector3> | null,
    number,
    Vector3 | null,
  ] {
    const radialPosition = this.getRadialPosition();
    if (!radialPosition) {
      return [null, 0, null];
    }

    return Client.raycastGround(radialPosition);
  }
  //#endregion

  public createParticleEntity() {
    const position = this.getRadialMenuCoordinates()[0];
    if (!position) return;

    const prefab = new Prefab("particle", [
      new TransformBehavior(
        position.clone(),
        new Quaternion(),
        new Vector3(1, 1, 1)
      ),
      new ParticleBehavior(/* createdBy */ this.id),
    ]);

    runCommands(new SpawnPrefabCommand(prefab));
  }

  export(format: ExportFormat, selectedEntities: GameEntity[] = []) {
    const exportEntity = new Exporter();

    if (selectedEntities.length == 0) exportEntity.exportScene(format);
    else if (selectedEntities.length > 0)
      exportEntity.exportSelectedEntities(format, selectedEntities);
  }

  generateImportMesh() {
    const importMeshEntity = new MeshEntity();
    importMeshEntity.setCreatedBy(this.id);
    return importMeshEntity;
  }

  async importAnimationFromFile(): Promise<AnimationData> {
    const sharedEntity = Client.getEntity<SharedEntity>(SharedEntity.ID);
    let tempAnimationData: AnimationData | undefined;
    const getFileName = (fileName: string) => {
      tempAnimationData = {
        name: fileName,
        provider: "UserImported",
        url: "",
        id: fileName,
        isGenerating: true,
        skeletonType: "Custom",
      };
      sharedEntity?.addCharacterMotion(tempAnimationData);
    };
    const animationData =
      await this.importer.readFileAndImportAnimation(getFileName);

    if (sharedEntity) {
      if (tempAnimationData) {
        sharedEntity.removeCharacterMotion(tempAnimationData.id);
      }
      sharedEntity.addCharacterMotion(animationData);
      this.netShouldUpdate = true;
    }

    return animationData;
  }

  //Use importAnimation when possible, this one doesn't use animationStorage
  async importAnimationFromUrl(url: string): Promise<LoadedAnimation> {
    return this.importer.importAnimationFromUrl(url);
  }

  async importAnimation(
    animationData: AnimationData
  ): Promise<LoadedAnimation> {
    return Client.animationStorage.getLoadedAnimation(animationData);
  }

  async parseImportedModel(
    importedModelData: ImportedModel,
    isStatic: boolean,
    isDisabled: boolean,
    position: Vector3,
    quaternion: Quaternion,
    scale?: Vector3,
    shapeType?: PhysicsShapeType
  ) {
    // TODO: interact with client to get the entity instead
    const id = nanoid();
    //@ts-expect-error some
    const entityData: MeshEntityData = {
      id,
      type: EntityType.MeshEntity,
      createdBy: this.id,
      originType: EntityOriginTypes.NativeOrigin,
      positionX: position.x,
      positionY: position.y, //importMesh.getEntitySize().y,
      positionZ: position.z,
      persistent: true,
      dragging: false,
      orientationX: quaternion.x,
      orientationY: quaternion.y,
      orientationZ: quaternion.z,
      orientationW: quaternion.w,
      prompt: "",
      scaleX: scale?.x || 1,
      scaleY: scale?.y || 1,
      scaleZ: scale?.z || 1,
      physicsUserData: {
        isUserImported: true,
        physics: {
          isStatic,
          isDisabled,
          shapeType: shapeType || Client.composer.getShapeOnCreate(),
        },
      },
    };
    runCommands([
      new CreateUserModelMeshEntityCommand(entityData, importedModelData),
      new SelectEntitiesCommand([entityData.id], EntitySelectionMode.REPLACE),
    ]);
  }

  public preventModelFalling = (
    id: EntityId,
    position: Vector3,
    quaternion: Quaternion
  ) => {
    let currentAttempts = 0;
    const maxAttempts = 100; // 1 second max wait time
    const interval = setInterval(() => {
      currentAttempts++;
      const entity = Client.getEntity(id) as MeshEntity | null;
      if (entity) {
        const mesh = entity.getMesh();
        const body = entity.getPhysicsBodyControl();
        // for user models we want this only if custom prop isStatic is not set on the model
        if (body && mesh.userData.isStatic === undefined) {
          setTimeout(() => {
            const nq = new Quaternion();
            entity.getSimulatedOrientation(nq);
            const np = new Vector3();
            entity.getSimulatedPosition(np);
            if (
              new Quaternion(nq.x, nq.y, nq.z, nq.w).angleTo(quaternion) >
                0.1 ||
              position.y - np.y > 0.01
            ) {
              runCommands(
                [
                  new SetEntityPhysicsCommand(id, {
                    physics: { isStatic: true },
                  }),
                  new SetEntityPositionCommand(
                    id,
                    position.clone().add(new Vector3(0, 1e-6, 0))
                  ),
                  new SetEntityOrientationCommand(
                    id,
                    quaternion
                      .clone()
                      .multiply(new Quaternion().fromArray([0, 1e-5, 0, 1]))
                  ),
                ],
                false
              );
              toast.success("Disabled physics for object");
            }
          }, 1000);
          clearInterval(interval);
        }
      }
      if (currentAttempts >= maxAttempts) {
        clearInterval(interval);
        console.warn(
          `Failed to prevent model falling after ${maxAttempts} attempts`
        );
      }
    }, 10);
  };

  private processImageGeneratedModels(
    coordinates: Vector3,
    offset: Vector3,
    assumedModelOffset: Vector3,
    importedImageFiles: File[],
    isStatic: boolean
  ) {
    for (let i = 0; i < importedImageFiles.length; i += 1) {
      const file = importedImageFiles[i];
      this._generateModelFromImage(
        file,
        coordinates
          .clone()
          .add(offset)
          .add(assumedModelOffset.clone().multiplyScalar(i + 0.5)),
        isStatic
      );
    }

    offset.add(
      assumedModelOffset.clone().multiplyScalar(importedImageFiles.length)
    );
  }

  async onClickImport() {
    const [coordinates, vAngle] = this.getRadialMenuCoordinates();
    if (coordinates) {
      const transaction = beginTransaction("import-model");

      const files = await openFileBrowserAndGetFiles(
        ACCEPTABLE_MODEL_FILE_EXTENSIONS
      );
      const promises = files
        .filter(
          (file) =>
            file.name.endsWith(".gltf") ||
            file.name.endsWith(".glb") ||
            file.name.endsWith(".obj") ||
            file.name.endsWith(".fbx") ||
            file.name.endsWith(".stl") ||
            file.name.endsWith(".ply")
        )
        .map(
          (file) =>
            new Promise<ImportedModel>((resolveFile, rejectFile) => {
              const f = async () => {
                try {
                  const model = await this.importer.loadModel(file, files);
                  resolveFile({
                    model,
                    file_size_mb: file.size / 1_000_000,
                  }); // Resolve with the loaded model
                } catch (error) {
                  console.error(error);
                  //@ts-expect-error toast
                  toast.error(error.message);
                  rejectFile(error); // Reject in case of an error
                }
              };
              f();
            })
        );

      const modelSizes = promises.map(() => new Vector3());
      const camera = this.getCamera() as PerspectiveCamera;
      const q = new Quaternion().setFromUnitVectors(
        new Vector3(0, 0, 1),
        new Vector3(
          camera.position.x - coordinates.x,
          0,
          camera.position.z - coordinates.z
        ).normalize()
      );
      const assumedModelOffset = new Vector3(2, 0, 0).applyQuaternion(q);

      promises.forEach((promise, index) => {
        promise.then((importedModel) =>
          this.promiseCallback(
            importedModel,
            modelSizes,
            coordinates,
            q,
            assumedModelOffset,
            index,
            vAngle > ANGLE_STATIC
          )
        );
      });

      await Promise.all(promises);
      transaction.end();
    }
  }

  async dropImport(event: DragEvent) {
    const [coordinates, vAngle] = this.getCurserCoordinates(event);

    const fileList = event.dataTransfer?.files;
    if (!fileList || !coordinates) return;
    const transaction = beginTransaction("drop-import");

    const files = Array.from(fileList);

    const dataUrl = event.dataTransfer.getData("text/uri-list");
    if (dataUrl) {
      const ext = dataUrl.split(".").pop()!.toLowerCase();
      const isImageUrl =
        dataUrl.startsWith("http") && ["png", "jpg", "jpeg"].includes(ext);

      if (isImageUrl) {
        // check if url is an image url and if so, convert it to a file
        try {
          const response = await fetch(dataUrl);
          const blob = await response.blob();
          const file = new File([blob], `${crypto.randomUUID()}.${ext}`, {
            type: blob.type,
          });
          files.push(file);

          console.debug("Converted data URL to file:", file);
        } catch (error) {
          console.error("Failed to convert data URL to file:", error);
        }
      }
    }

    const importedModelFiles = files.filter(
      (file) =>
        file.name.endsWith(".gltf") ||
        file.name.endsWith(".glb") ||
        file.name.endsWith(".obj") ||
        file.name.endsWith(".fbx") ||
        file.name.endsWith(".stl") ||
        file.name.endsWith(".ply")
    );
    const importedImageFiles = files.filter(
      (file) =>
        file.name.endsWith(".png") ||
        file.name.endsWith(".jpg") ||
        file.name.endsWith(".jpeg") ||
        file.name.endsWith(".webp")
    );

    const offset = new Vector3();
    const camera = this.getCamera() as PerspectiveCamera;
    const q = new Quaternion().setFromUnitVectors(
      new Vector3(0, 0, 1),
      new Vector3(
        camera.position.x - coordinates.x,
        0,
        camera.position.z - coordinates.z
      ).normalize()
    );
    const assumedModelOffset = new Vector3(2, 0, 0).applyQuaternion(q);
    if (importedImageFiles.length > 0 && importedModelFiles.length === 0) {
      // we generate from images only when models are not present in selection
      // otherwise we assume images to be a textures for some of models
      this.processImageGeneratedModels(
        coordinates,
        offset,
        assumedModelOffset,
        importedImageFiles,
        vAngle > ANGLE_STATIC
      );
    }
    const modelSizes = importedModelFiles.map(() => new Vector3());
    const promises = importedModelFiles.map(
      (file) =>
        new Promise<ImportedModel>((resolveFile, rejectFile) => {
          const f = async () => {
            try {
              const model = await this.importer.loadModel(file, files);
              resolveFile({
                model,
                file_size_mb: file.size / 1_000_000,
              }); // Resolve with the loaded model
            } catch (error) {
              console.error(error);
              //@ts-expect-error toast
              toast.error(error.message);
              rejectFile(error); // Reject in case of an error
              // reject(error);
            }
          };
          f();
        })
    );

    promises.forEach((promise, index) => {
      promise.then((importedModel) =>
        this.promiseCallback(
          importedModel,
          modelSizes,
          coordinates,
          q,
          assumedModelOffset,
          index,
          vAngle > ANGLE_STATIC
        )
      );
    });

    await Promise.all(promises);
    transaction.end();
  }

  async promiseCallback(
    importedModel: ImportedModel,
    modelSizes: Vector3[],
    coordinates: Vector3,
    q: Quaternion,
    assumedModelOffset: Vector3,
    index: number,
    isStatic: boolean
  ) {
    const {
      eachMeshSeparateOnCreate,
      enablePhysicsOnCreate,
      enableCollidersOnCreate,
      unchangedPositionOnCreate,
      checkModelCustomProperties,
    } = Client.composer.options;
    if (!eachMeshSeparateOnCreate) {
      const size = new Vector3();
      const box = new Box3().setFromObject(importedModel.model.clone());
      box.getSize(size);
      modelSizes[index].copy(size);
      this.parseImportedModel(
        importedModel,
        !enablePhysicsOnCreate || isStatic,
        !enableCollidersOnCreate,
        (unchangedPositionOnCreate ? new Vector3() : coordinates.clone()).add(
          assumedModelOffset.clone().multiplyScalar(index + 0.5)
        ),
        // .add(new Vector3(0, size.y * 0.5, 0)),
        unchangedPositionOnCreate ? new Quaternion() : q
      );
    } else {
      const size = new Vector3();
      const box = new Box3().setFromObject(importedModel.model.clone());
      box.getSize(size);
      modelSizes[index].copy(size);
      importedModel.model.traverse((obj) => {
        if ((obj as Mesh).isMesh) {
          const mesh = (obj as Mesh).clone();
          const position = new Vector3();
          const quaternion = new Quaternion();
          const scale = new Vector3();
          const model = new Group().add(mesh);
          model.userData = JSON.parse(JSON.stringify(mesh.userData));
          mesh.position.set(0, 0, 0);
          mesh.quaternion.set(0, 0, 0, 1);
          mesh.scale.set(1, 1, 1);
          const matrix = new Matrix4()
            .copy((obj as Mesh).matrixWorld)
            .premultiply(
              new Matrix4().compose(
                (unchangedPositionOnCreate
                  ? new Vector3()
                  : coordinates.clone()
                ).add(assumedModelOffset.clone().multiplyScalar(index + 0.5)),
                unchangedPositionOnCreate ? new Quaternion() : q,
                new Vector3(1, 1, 1)
              )
            );
          matrix.decompose(position, quaternion, scale);

          const isStaticUserData = checkModelCustomProperties
            ? mesh.userData.isStatic
            : undefined;
          const isDisabledUserData = checkModelCustomProperties
            ? mesh.userData.isDisabled
            : undefined;
          const shapeType = secureShapeType(
            checkModelCustomProperties ? mesh.userData.shapeType : undefined
          );

          const isStaticDefault = !enablePhysicsOnCreate || isStatic;
          const isDisabledDefault = !enableCollidersOnCreate;

          this.parseImportedModel(
            { model, file_size_mb: importedModel.file_size_mb },
            isStaticUserData ?? isStaticDefault,
            isDisabledUserData ?? isDisabledDefault,
            position,
            quaternion,
            scale,
            shapeType
          );
        }
      });
    }
  }

  private async _generateModelFromImage(
    file: File | Blob,
    position: Vector3,
    isStatic: boolean
  ) {
    const b64 = await toBase64(file);
    const meshEntityData: MeshEntityData = {
      type: EntityType.MeshEntity,
      originType: EntityOriginTypes.GeometryCenter,
      id: nanoid(),
      taskId: "",
      originalMeshTaskId: "",
      positionX: position.x,
      positionY: position.y,
      positionZ: position.z,
      orientationX: 0,
      orientationY: 0,
      orientationZ: 0,
      orientationW: 1,
      scaleX: 1,
      scaleY: 1,
      scaleZ: 1,
      prompt: "",
      createdBy: this.id,
      detail: 1,
      modelData: null,
      color: 0xffffff,
      persistent: true,
      dragging: false,
      locked: false,
      materialMetalness: 0,
      materialRoughness: 0.9,
      materialWireframe: false,
      materialTransmission: 0,
      materialIor: 0,
      materialThickness: 0,
      materialDispersion: 0,
      childrenIds: [],
      parentId: null,
      physicsUserData: {
        physics: {
          isStatic: !Client.composer.options.enablePhysicsOnCreate || isStatic,
          isDisabled: !Client.composer.options.enableCollidersOnCreate,
          shapeType: Client.composer.getShapeOnCreate(),
        },
      },
    };

    runCommands([
      new CreateImageMeshEntityCommand(meshEntityData, {
        imagePrompt: b64,
        imageType: file.type,
      }),
      new SelectEntitiesCommand(
        [meshEntityData.id],
        EntitySelectionMode.REPLACE
      ),
    ]);
  }

  public getNormalizedCoordinates(
    screenX: number,
    screenY: number,
    out = new Vector2()
  ): Vector2 {
    out.x = (screenX / window.innerWidth) * 2 - 1;
    out.y = -(screenY / window.innerHeight) * 2 + 1;
    return out;
  }

  getCurserCoordinates(event: DragEvent) {
    const previewPos = Client.raycastGround(
      this.getNormalizedCoordinates(event.clientX, event.clientY)
    );
    return previewPos;
  }

  previewDragImport() {
    this.importPreviewEntity = new MeshEntity();
    this.importPreviewEntity.addBox();
    Client.addEntityToWorld(this.importPreviewEntity);
  }

  setImportPreviewBoxPosition(newPos: Vector3) {
    if (this.importPreviewEntity) this.importPreviewEntity.setPosition(newPos);
  }

  revertImportPreview() {
    if (this.importPreviewEntity) {
      this.importPreviewEntity.removeBox();
      Client.removeEntity(this.importPreviewEntity);
      this.importPreviewEntity = null;
    }
  }

  public focusEntities(entities: GameEntity[]) {
    if (entities.length > 0) {
      const box = new Box3().setFromObject(
        entities[0].getRootNode() as Object3D
      );

      for (let i = 1; i < entities.length; i++) {
        box.expandByObject(entities[i].getRootNode() as Object3D);
      }

      if (box.isEmpty()) {
        return;
      }

      const center = new Vector3();
      const size = new Vector3();
      box.getCenter(center);
      box.getSize(size);

      this.cameraControls?.focusOnPosition(
        center,
        (size.x + size.y + size.z) / 4
      );
    }
  }
}
