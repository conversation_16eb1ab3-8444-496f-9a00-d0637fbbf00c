import React, { useEffect, useState } from "react";
import { Link, Outlet } from "react-router-dom";
import {
  Briefcase,
  Bug,
  Database,
  EyeIcon,
  Globe,
  Home,
  Info,
  ListCheck,
  RefreshCcwIcon,
  Mail,
  User,
  Variable,
  Wrench,
} from "lucide-react";
import { ExclamationCircleIcon } from "@heroicons/react/24/solid";
import { useAuthState } from "react-firebase-hooks/auth";
import { IdTokenResult, signOut } from "firebase/auth";
import { Typography } from "@/components/ui-nilo/Typography";
import { NavigationLink } from "@/components/ui-nilo/NavigationLink";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { useIsAdmin } from "@/hooks/useIsAdmin";
import { PlatformHeader } from "@/components/platform/PlatformHeader";
import { SignOutIcon } from "@/icons";
import { auth } from "@/config/firebase";

const ForbiddenScreen = () => {
  const accentColorHex = "#ef4444"; // Tailwind red-500
  const [user] = useAuthState(auth);
  const [token, setToken] = useState<IdTokenResult | undefined>(undefined);
  const [showToken, setShowToken] = useState(false);

  useEffect(() => {
    if (user) {
      user.getIdTokenResult().then(setToken);
    } else {
      setToken(undefined);
    }
  }, [user]);

  const handleLogout = async () => {
    try {
      await signOut(auth);
      console.debug("🚪 User logged out successfully");
    } catch (error) {
      console.error("❌ Error logging out:", error);
    }
  };

  const handleRefreshToken = async () => {
    try {
      const newToken = await user?.getIdTokenResult(true);
      setToken(newToken);
    } catch (error) {
      console.error("❌ Error refreshing token:", error);
    }
  };

  return (
    <>
      <PlatformHeader showUser={true} showLogo={true} />
      <div className="absolute inset-0 flex flex-col items-center justify-center gap-4 select-none bg-black/80">
        <div
          className="flex items-center justify-center rounded-full h-16 w-16 mb-2"
          style={{ backgroundColor: accentColorHex + "22" }} // subtle background
        >
          <span style={{ color: accentColorHex, fontSize: 40 }}>
            <ExclamationCircleIcon className="h-12 w-12" />
          </span>
        </div>
        <div
          className="text-lg font-semibold text-center whitespace-pre-line"
          style={{ color: accentColorHex }}
        >
          You are not authorized to access this page
        </div>
        <div className="flex flex-row gap-2">
          <Button onClick={handleLogout} variant="destructive" size="default">
            <SignOutIcon />
            Logout
          </Button>
          <Button
            onClick={handleRefreshToken}
            variant="secondary"
            size="default"
          >
            <RefreshCcwIcon />
            Refresh Token
          </Button>
          <Button onClick={() => setShowToken(!showToken)}>
            <EyeIcon />
            Show Token
          </Button>
        </div>
        <div className="flex flex-col gap-2 max-w-6xl">
          {showToken && (
            <pre className="bg-muted text-muted-foreground p-4 rounded-lg overflow-x-auto text-sm font-mono leading-relaxed border">
              {JSON.stringify(token, null, 2)}
            </pre>
          )}
        </div>
      </div>
    </>
  );
};

const InternalLayout = () => {
  const isAdmin = useIsAdmin();
  if (!isAdmin) {
    return <ForbiddenScreen />;
  }

  const sections = [
    {
      pages: [
        {
          path: "/internal/userInfo",
          label: "User Info",
          icon: <User />,
          helperText: "Current user information",
        },
        {
          path: "/internal/envInfo",
          label: "Env Info",
          icon: <Info />,
          helperText: "Environment information",
        },
      ],
    },
    {
      label: "Data",
      pages: [
        {
          path: "/internal/builderBag",
          label: "Builder Bag",
          icon: <Briefcase />,
          helperText: "Debug builder bag",
        },
        {
          path: "/internal/worlds",
          label: "Worlds",
          icon: <Globe />,
          helperText: "Debug worlds",
        },
        {
          path: "/internal/whitelist",
          label: "Whitelist",
          icon: <Mail />,
          helperText: "Whitelist emails",
        },
      ],
    },
    {
      label: "Backend tasks",
      pages: [
        {
          path: "/internal/fixtures",
          label: "Fixtures",
          icon: <Bug />,
          helperText: "Run fixture functions",
        },
        {
          path: "/internal/dataJobs",
          label: "Data Jobs",
          icon: <Database />,
          helperText: "Data jobs",
        },
        {
          path: "/internal/tasks",
          label: "Generation Tasks",
          icon: <ListCheck />,
          helperText: "Generation tasks",
        },
        {
          path: "/internal/taskStats",
          label: "Generation Task Stats",
          icon: <ListCheck />,
          helperText: "Generation task stats",
        },
        {
          path: "/internal/tripo",
          label: "Tripo Tasks",
          icon: <ListCheck />,
          helperText: "(deprecated)",
        },
      ],
    },
    {
      label: "Misc",
      pages: [
        {
          path: "/internal/uiShowcase",
          label: "UI Showcase",
          icon: <Variable />,
          helperText: "Custom theme components showcase",
        },
        {
          path: "/internal/devUrls",
          label: "Dev URLs",
          icon: <Wrench />,
          helperText: "Development URLs",
        },
        {
          path: "/internal/sentry",
          label: "Sentry Testing",
          icon: <Bug />,
          helperText: "Trigger an error",
        },
      ],
    },
  ];

  return (
    <div className="min-h-screen bg-nilo-neutral-black">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 shadow-sm border-r border-nilo-border-primary space-y-2">
          <div className="p-4 space-y-2">
            <div className="flex flex-col items-center space-y-2">
              <Typography.Title level={3} color="light">
                Internal Tools
              </Typography.Title>
              <Link to="/">
                <Button className="p-2" variant="outline">
                  <Home size={20} />
                </Button>
              </Link>
            </div>
            {sections.map((section, idx) => (
              <React.Fragment key={idx}>
                {idx > 0 && <Separator />}
                <div className="space-y-2">
                  {section.label && (
                    <Typography.Heading level={1} color="light">
                      {section.label}
                    </Typography.Heading>
                  )}
                  {section.pages.map((page) => (
                    <Link to={page.path} key={page.path}>
                      <NavigationLink
                        icon={page.icon}
                        helperText={page.helperText}
                      >
                        {page.label}
                      </NavigationLink>
                    </Link>
                  ))}
                </div>
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 p-6">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default InternalLayout;
