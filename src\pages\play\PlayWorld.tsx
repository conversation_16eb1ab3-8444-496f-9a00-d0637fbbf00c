import { ExclamationCircleIcon } from "@heroicons/react/24/solid";

import { lazy, Suspense, useEffect } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { useParams } from "react-router-dom";

import { BanIcon } from "lucide-react";
import { auth } from "@/config/firebase";
import { useStreamDocumentById } from "@/hooks/firebaseHooks";
import { UserBroadcasting } from "@/networking/UserBroadcasting";
import { worldsCollection } from "@/utils/firestoreCollections";

import { LiveblocksRoom } from "@/components/LiveblocksRoom";
import { CriticalErrorScreen } from "@/components/platform/CriticalErrorScreen";
import { LoadingScreen } from "@/components/platform/LoadingScreen";
import { PlayWorldHeader } from "@/pages/play/PlayWorldHeader";
import { WorldInfoProvider } from "@/contexts/WorldInfoContext";
import { WorldSettingsProvider } from "@/contexts/WorldSettingsContext";
import { EditorUIProvider } from "@/contexts/EditorUIContext";
import MultiTabGuard from "@/providers/multi-tab-guard";
import { urlParams } from "@/utils/urlParams";

import { trackWorldViewed } from "@/utils/tracking/worldUtils";
import { trackPlaySession } from "@/utils/tracking/sessionUtils";

const ThreeCanvas = lazy(() => import("@/components/ThreeCanvas/ThreeCanvas"));

function PlayWorld() {
  const { worldId } = useParams();

  if (!worldId) {
    return (
      <CriticalErrorScreen
        icon={<ExclamationCircleIcon className="h-12 w-12" />}
        message="No world id provided"
      />
    );
  }

  return <PlayWorldView worldId={worldId} />;
}

///// TEMP: Temporarily need to export this to reuse in LegacyPlayWorld.tsx
///// (previously NiloRoom.tsx) for back-compat with /:liveblocksRoomId route
export function PlayWorldView({ worldId }: { worldId: string }) {
  const [user] = useAuthState(auth);
  const userId = user?.uid;

  const doc = useStreamDocumentById(worldsCollection, worldId);
  const world = doc.value?.data();
  const worldExists = !!world;

  useEffect(() => {
    if (worldExists) {
      // Track world viewed with auto-detected entry point
      if (userId) {
        trackWorldViewed(worldId, userId);
      }

      const stopTracking = trackPlaySession({ userId, worldId });
      return stopTracking;
    }
  }, [userId, worldId, worldExists]);

  if (doc.status === "loading") {
    return <LoadingScreen message="Loading World Data" />;
  }

  if (doc.status === "error") {
    if (doc.error === "permission-denied") {
      return (
        <CriticalErrorScreen
          icon={<BanIcon className="h-12 w-12" />}
          message="You don't have permission to access this world."
          submessage={`Contact the world creator for access.`}
          accentColorHex="#4fef94"
        />
      );
    }

    return (
      <CriticalErrorScreen
        icon={<ExclamationCircleIcon className="h-12 w-12" />}
        message={"Internal Error"}
        submessage={`[${doc.error}]`}
      />
    );
  }

  if (!world) {
    return (
      <CriticalErrorScreen
        icon={<ExclamationCircleIcon className="h-12 w-12" />}
        message="No world data available"
      />
    );
  }

  if (!world?.liveblocksRoomId) {
    return (
      <CriticalErrorScreen
        icon={<ExclamationCircleIcon className="h-12 w-12" />}
        message="No liveblocks room id available for this world"
      />
    );
  }

  return (
    <WorldInfoProvider worldId={worldId}>
      <WorldSettingsProvider>
        <EditorUIProvider>
          <MultiTabGuard
            roomId={world.liveblocksRoomId}
            bypass={urlParams.allowMultitab}
          >
            <LiveblocksRoom id={world.liveblocksRoomId}>
              <Suspense fallback={<LoadingScreen message="Loading World" />}>
                <ThreeCanvas />
                <UserBroadcasting />
              </Suspense>

              <PlayWorldHeader />
            </LiveblocksRoom>
          </MultiTabGuard>
        </EditorUIProvider>
      </WorldSettingsProvider>
    </WorldInfoProvider>
  );
}

export default PlayWorld;
