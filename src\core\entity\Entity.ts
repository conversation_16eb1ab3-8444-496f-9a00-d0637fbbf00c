import {
  Vector3,
  <PERSON>uatern<PERSON>,
  Object3D,
  Vector2,
  Vector3<PERSON><PERSON>,
  QuaternionLike,
} from "three";
import { DuplicateControls } from "../util/controls/DuplicateControls";
import TransformControls, {
  GIZMOSPACE,
} from "../util/controls/TransformControls";
import { Position, Rotation, Scale, ScriptComponent } from "../components";
import { assertNotNull, assertTrue } from "../util/Assert";
import {
  CollisionEvent,
  DeathEvent,
  EventDelegate,
  OnCollision,
  OnDeath,
  OnDestroyed,
  OnRespawn,
  OnSpawned,
  OnTriggerEnter,
  OnTriggerExit,
  RespawnEvent,
} from "../ecs/events";
import { EntityState } from "./networking/EntityState";
import { EntityProgress } from "./common/EntityProgress";
import { EntityReplication } from "./networking/EntityReplication";
import { GameEntityData } from "@/liveblocks.config";
import { Client } from "@/core/client";
import { getBodyControlFor } from "@/physics/helpers/getBodyControlFor";
import { createDelegateFunction, DelegateFunction } from "@nilo/utilities";
import {
  updateFollowingMovement,
  updateWanderingMovement,
} from "@/core/entity/Npc";
import UIManager from "@/core/util/UIManager";
import { Component, EntityData, EntityId } from "@nilo/ecs";
import { DynamicProperties } from "packages/physics-with-jolt/src/types/webworker";

const _p = new Vector3();
const _s = new Vector3();
const _q = new Quaternion();
// interface AttachTransformControlsOptions {
//   detachGivenControls: boolean;
// }

const voidFn = () => {};

export enum EntityOriginTypes {
  "NativeOrigin" = "NativeOrigin",
  "GeometryCenter" = "GeometryCenter",
  "GeometryBottomCenter" = "GeometryBottomCenter",
}
const _simulatedPosition = new Vector3();
const _currentPosition = new Vector3();

/**
 * Entity base class
 * TODO: add component system (post proto)
 * TODO: physics components (post proto)
 * TODO: script component (post proto)
 * TODO: split into client and server parts when we switch to c/s (post proto)
 * TODO: implement full c/s networking (post proto)
 * TODO: note we'll probably only have a single entity class once component system implemented
 */
export class GameEntity {
  // entity type
  public type: string = "Entity";

  public entity: EntityId = null!;

  public get id(): EntityId {
    return this.entity;
  }

  /// Returns true if the entity is properly added to the world.
  ///
  /// If an entity is spawned from within a query, it will not be visible until the end of the query
  public get isInWorld(): boolean {
    // NOTE: checking just id is not enough, as some systems still use and set the old nanoid
    const data = Client.world.entityData(this.id);
    return data !== null && !data.isDeferred();
  }

  protected _alwaysLocal: boolean = false;

  /** User entity which created this entity */
  protected _createdBy: EntityId | null = null;
  // Without _disposed, it is easy to call Client.removeEntity(entitiy)
  // more than once on the same entity,
  // which would trigger side effects such at removing it from physics.
  // So we need a way to know when an entity has already been removed,
  // so that any side effect is not triggered more than once.
  private _disposed: boolean = false;

  private _state: EntityState = new EntityState(this);

  public get state() {
    return this._state;
  }
  protected _progress: EntityProgress;
  protected _generationPanelId: string | null = null;

  public get progress() {
    return this._progress;
  }

  // Tags for gameplay logic
  private tags: Set<string> = new Set();

  // Add one or more tags to the entity
  public addTag(...tags: string[]): this {
    for (const tag of tags) {
      this.tags.add(tag);
    }
    return this;
  }

  // Remove one or more tags from the entity
  public removeTag(...tags: string[]): this {
    for (const tag of tags) {
      this.tags.delete(tag);
    }
    return this;
  }

  // Check if the entity has all the specified tags
  public hasAllTags(...tags: string[]): boolean {
    return tags.every((tag) => this.tags.has(tag));
  }

  // Check if the entity has any of the specified tags
  public hasTags(...tags: string[]): boolean {
    return tags.some((tag) => this.tags.has(tag));
  }

  // Get all tags currently applied to the entity
  public getTags(): string[] {
    return Array.from(this.tags);
  }

  // Remove all tags from the entity
  public clearTags(): this {
    this.tags.clear();
    return this;
  }

  public data(): EntityData {
    const data = Client.world.entityData(this.id);
    if (!data) {
      throw new Error(`Entity data not found for entity ${this.id}`);
    }
    return data;
  }

  public delegates = {
    onCodeError: createDelegateFunction<[string]>(),
  } as {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    [key: string]: DelegateFunction<any, any>;
  };

  /**
   * Initialize ECS event components for this entity
   */
  private initializeEventComponents() {
    assertTrue(
      this.isInWorld,
      `Cannot initialize event components for entity ${this.id} which is not in the world.`
    );

    // Initialize event delegates for ECS events
    this.data().addComponent(OnSpawned, createDelegateFunction<[GameEntity]>());
    this.data().addComponent(
      OnDestroyed,
      createDelegateFunction<[GameEntity]>()
    );
    this.data().addComponent(
      OnCollision,
      createDelegateFunction<[CollisionEvent]>()
    );
    this.data().addComponent(
      OnTriggerEnter,
      createDelegateFunction<[CollisionEvent]>()
    );
    this.data().addComponent(
      OnTriggerExit,
      createDelegateFunction<[CollisionEvent]>()
    );
    this.data().addComponent(OnDeath, createDelegateFunction<[DeathEvent]>());
    this.data().addComponent(
      OnRespawn,
      createDelegateFunction<[RespawnEvent]>()
    );
  }

  public emitEvent<T>(event: Component<EventDelegate<T>>, eventData: T) {
    const emitter = this.data().getComponent(event);
    assertNotNull(emitter, `Event emitter not found for ${event.KEY}`);
    emitter.invoke(eventData);
  }

  /**
   * Emit an OnSpawned event through the ECS system
   */
  public emitSpawnedEvent() {
    this.emitEvent(OnSpawned, this);
  }

  /**
   * Emit an OnDestroyed event through the ECS system
   */
  public emitDestroyedEvent() {
    this.emitEvent(OnDestroyed, this);
  }

  /**
   * Emit an OnCollision event through the ECS system
   */
  public emitCollisionEvent(otherEntity: GameEntity | null) {
    const collisionEvent = new CollisionEvent(
      this.data(),
      otherEntity ? otherEntity.data() : null
    );

    this.emitEvent(OnCollision, collisionEvent);
  }

  /**
   * Emit an OnTriggerEnter event through the ECS system
   */
  public emitTriggerEnterEvent(otherEntity: GameEntity | null) {
    const collisionEvent = new CollisionEvent(
      this.data(),
      otherEntity ? otherEntity.data() : null
    );

    this.emitEvent(OnTriggerEnter, collisionEvent);
  }

  /**
   * Emit an OnTriggerExit event through the ECS system
   */
  public emitTriggerExitEvent(otherEntity: GameEntity | null) {
    const collisionEvent = new CollisionEvent(
      this.data(),
      otherEntity ? otherEntity.data() : null
    );

    this.emitEvent(OnTriggerExit, collisionEvent);
  }

  /**
   * Emit an OnRespawn event through the ECS system
   */
  public emitRespawnEvent(
    respawnPoint: GameEntity,
    position: Vector3,
    orientation: Quaternion
  ) {
    const respawnEvent = new RespawnEvent(
      this.data(),
      respawnPoint.data(),
      position,
      orientation
    );
    this.emitEvent(OnRespawn, respawnEvent);
  }

  /**
   * Emit an OnDeath event through the ECS system
   */
  public emitDeathEvent(deathTime: number) {
    const deathEvent = new DeathEvent(this.data(), deathTime);
    this.emitEvent(OnDeath, deathEvent);
  }

  /**
   * Get the collision event delegate for direct access from scripting layer
   * @returns The delegate for collision events, or null if not available
   */
  public get onCollision(): EventDelegate<CollisionEvent> | null {
    if (!this.isInWorld) return null;

    return this.data().getComponent(OnCollision) || null;
  }

  /**
   * Get the trigger enter event delegate for direct access from scripting layer
   * @returns The delegate for trigger enter events, or null if not available
   */
  public onTriggerEnter(): EventDelegate<CollisionEvent> | null {
    if (!this.isInWorld) return null;

    return this.data().getComponent(OnTriggerEnter) || null;
  }

  /**
   * Get the trigger exit event delegate for direct access from scripting layer
   * @returns The delegate for trigger exit events, or null if not available
   */
  public onTriggerExit(): EventDelegate<CollisionEvent> | null {
    if (!this.isInWorld) return null;

    return this.data().getComponent(OnTriggerExit) || null;
  }

  /**
   * Get the spawned event delegate for direct access from scripting layer
   * @returns The delegate for spawned events, or null if not available
   */
  public get onSpawned(): EventDelegate<GameEntity> | null {
    if (!this.isInWorld) return null;

    return this.data().getComponent(OnSpawned) || null;
  }

  /**
   * Get the destroyed event delegate for direct access from scripting layer
   * @returns The delegate for destroyed events, or null if not available
   */
  public get onDestroyed(): EventDelegate<GameEntity> | null {
    if (!this.isInWorld) return null;

    return this.data().getComponent(OnDestroyed) || null;
  }

  /**
   * Get the death event delegate for direct access from scripting layer
   * @returns The delegate for death events, or null if not available
   */
  public get onDeath(): EventDelegate<DeathEvent> | null {
    if (!this.isInWorld) return null;

    return this.data().getComponent(OnDeath) || null;
  }

  public setCreatedBy(value: EntityId | null, _dirty = true) {
    if (this._createdBy !== value) {
      this._createdBy = value;
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
  }

  public get createdBy() {
    return this._createdBy;
  }

  private _selectedPanelId: string | null = null;

  setSelectedPanelId(id: string | null) {
    this._selectedPanelId = id;
  }

  getSelectedPanelId() {
    return this._selectedPanelId;
  }

  getOffset() {
    return new Vector3();
  }

  // position
  protected _position: Vector3 = new Vector3();
  protected _simulatedPosition: Vector3 | undefined = undefined;
  protected _simulatedOrientation: Quaternion | undefined = undefined;
  public get position() {
    return this._position;
  }

  public get rotation() {
    return this._orientation;
  }

  public get scale() {
    return this._scale;
  }

  /** Updates any of the specified transform properties, such as position, rotation, or scale directly on the game entity class itself.
   *
   * This is used to change the position of a game entity before it is spawned.
   */
  public setTransformProps(
    position?: Vector3Like,
    orientation?: QuaternionLike,
    scale?: Vector3Like
  ) {
    if (position && !this._position.equals(position)) {
      this._position.copy(position);
      this.rootNode?.position.copy(this._position);

      if (this.isInWorld) {
        Client.world.setComponent(this.id, Position, this._position);
      }
    }

    if (orientation) {
      const comparison = this._orientation.clone().copy(orientation); // cast from QuaternionLike to Quaternion
      if (orientation && !this._orientation.equals(comparison)) {
        this._orientation.copy(orientation);
        this.rootNode?.quaternion.copy(this._orientation);

        if (this.isInWorld) {
          Client.world.setComponent(this.id, Rotation, this._orientation);
        }
      }
    }

    if (scale && !this._scale.equals(scale)) {
      this._scale.copy(scale);
      this.rootNode?.scale.copy(this._scale);

      if (this.isInWorld) {
        Client.world.setComponent(this.id, Scale, this._scale);
      }
    }

    //Update physics body
    const physicsBodyControl = this.getPhysicsBodyControl();
    if (physicsBodyControl) {
      const physicsPosition = this.getOffset().add(this._position);
      if (position) physicsBodyControl.setPosition(physicsPosition);

      if (orientation) physicsBodyControl.setRotation(this.rotation);
    }
  }

  public setPhysicsState(
    position: Vector3,
    rotation: Quaternion,
    dynamicProperties: DynamicProperties
  ) {
    const physicsBodyControl = this.getPhysicsBodyControl();
    if (!physicsBodyControl) {
      //This can happen when an entity is still being deleted, but it's not completed in the webworker
      return;
    }

    if (physicsBodyControl.hasUpdatesInProgress()) {
      //To prevent flickers when the body is being updated from the webworker should only prevent 1 or 2 updates.
      //Should be a temporary solution, until we find a better one.
      return;
    }
    physicsBodyControl.updateState(position, rotation, dynamicProperties);

    if (this.rootNode) {
      this.rootNode.position.copy(position);
      this.rootNode.quaternion.copy(rotation);
    }
    if (!this._simulatedPosition || !this._simulatedOrientation) {
      this._simulatedPosition = new Vector3();
      this._simulatedOrientation = new Quaternion();
    }
    this._simulatedPosition.copy(position);
    this._simulatedOrientation.copy(rotation);
  }

  public setPosition(value: Vector3Like, _dirty = true): void {
    const currentPosition = Client.world.getComponent(this.id, Position);

    if (
      (this._simulatedPosition && !this._simulatedPosition.equals(value)) ||
      !this._position.equals(value)
    ) {
      _currentPosition.copy(value);
      if (currentPosition) {
        currentPosition.copy(value);
        Client.world.markDirty(this.id, Position);
      }

      this._position.copy(value);
      if (_dirty && this._entityReplication.isInterpolating("position")) {
        this._entityReplication.refreshPositionInterpolation(value);
        return;
      }
      if (this.rootNode) {
        this.rootNode.position.copy(value);
      }
      //Update physics body position
      const physicsBodyControl = this.getPhysicsBodyControl();
      if (physicsBodyControl) {
        physicsBodyControl.setPosition(value);
      }
      if (_dirty) {
        Client.markEntityAsDirty(this, false, false);
      }
      this._simulatedPosition?.copy(value);
    }
  }

  public refreshPosition(_dirty = true) {
    const physicsBodyControl = this.getPhysicsBodyControl();
    if (physicsBodyControl) {
      this._simulatedPosition?.copy(physicsBodyControl.getPosition());
    }
    this.getPosition(_currentPosition);

    if (!this._simulatedPosition) return;
    this.setPosition(this._simulatedPosition, _dirty);
    if (this._simulatedPosition.distanceToSquared(_currentPosition) > 0.01) {
      Client.markEntityAsDirty(this, false, true);
    }
  }

  private _distanceToCamera: number = 0;

  public updateDistanceToCamera() {
    const camera = Client.userEntity.getCamera();
    if (!camera) return 0;
    const position = new Vector3();
    this.getSimulatedPosition(position);
    this._distanceToCamera = position.distanceTo(camera.position);
    return this._distanceToCamera;
  }

  public getDistanceToCamera() {
    return this._distanceToCamera;
  }

  public isEntityNearCamera() {
    return true;
  }

  public worldToScreen(offset?: Vector3): Vector2 {
    const target = new Vector3();
    this.getSimulatedPosition(target);
    if (offset) {
      target.add(offset);
    }
    return Client.worldToScreen(target) as Vector2;
  }

  public getPosition(target: Vector3): void {
    target.copy(this._position);
  }

  public getSimulatedPosition(target: Vector3): void {
    target.copy(this._simulatedPosition ?? this._position);
  }

  // orientation
  protected _orientation: Quaternion = new Quaternion();
  public setOrientation(
    value: QuaternionLike,
    _dirty = true,
    force: boolean = false
  ) {
    const currentOrientation = Client.world.getComponent(this.id, Rotation);
    const comparison = this._orientation.clone().copy(value); // cast from QuaternionLike to Quaternion
    if (!this._orientation.equals(comparison) || force) {
      this._orientation.copy(value);
      if (currentOrientation) {
        currentOrientation.copy(value);
        Client.world.markDirty(this.id, Rotation);
      }
      //console.debug('entity orientation setter', this.id, this.type, value);
      //console.debug('entity orientation setter - value changed', this.id, this.type, value);
      this._orientation.copy(value);
      if (this.rootNode) {
        this.rootNode.quaternion.copy(value);
      }
      //Update physics body orientation
      const physicsBodyControl = this.getPhysicsBodyControl();
      if (physicsBodyControl) {
        physicsBodyControl.setRotation(value);
      }
      if (_dirty) {
        Client.markEntityAsDirty(this, false, true);
      }
      this._simulatedOrientation?.copy(value);
    }
  }

  public getOrientation(target: Quaternion): void {
    target.copy(this._orientation);
  }

  public getSimulatedOrientation(target: Quaternion): void {
    target.copy(this._simulatedOrientation ?? this._orientation);
  }

  // scale
  protected _scale: Vector3 = new Vector3(1, 1, 1);
  public setScale(value: Vector3Like, _dirty = true) {
    const currentScale = Client.world.getComponent(this.id, Scale);
    if (!this._scale.equals(value)) {
      this._scale.copy(value);
      if (currentScale) {
        currentScale.copy(value);
        Client.world.markDirty(this.id, Scale);
      }
      this._scale.copy(value);
      if (this.rootNode) {
        this.rootNode.scale.copy(value);
      }
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
    const physicsBodyControl = this.getPhysicsBodyControl();
    if (physicsBodyControl) {
      physicsBodyControl.setScale(value);
    }
  }
  public getScale(target: Vector3): void {
    target.copy(this._scale);
    //Update physics body scale
    //const physicsBodyControl = this.getPhysicsBodyControl();
  }

  // whether the entity is stored between sessions
  public persistent: boolean = true;

  // whether the entity is transformable
  public transformable: boolean = true;

  // whether the entity is currently being dragged
  protected _dragging: boolean = false;

  // whether the entity is locked in place
  protected _locked: boolean = false;
  public setDragging(value: boolean, dirty = true) {
    if (!this._dragging === value) {
      UIManager.instance.editPanel(this.getSelectedPanelId(), {
        visible: !value,
      });

      this._dragging = value;
      if (dirty) {
        Client.markEntityAsDirty(this, false);
      }

      // If entity starts being dragged, disable interpolation
      if (value) {
        this._entityReplication.disableInterpolation("position");
      }
    }
  }

  public addDragMarker() {}
  public updateDragMarker() {}
  public removeDragMarker() {}

  public getDragging() {
    return this._dragging;
  }

  public setLocked(value: boolean, dirty = true) {
    if (this._locked !== value) {
      this._locked = value;
      if (dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
  }

  public getLocked() {
    return this._locked;
  }

  public toggleLocked(dirty = true) {
    this.setLocked(!this._locked, dirty);
  }

  // three.js entity scenegraph root
  protected rootNode: Object3D | null = null;

  private _hasScript: boolean = false;
  private _yjsUnsubscribe: (() => void) | null = null;
  private _updateFn: (t: number, dt: number) => void = voidFn;

  // Interpolation properties
  protected _entityReplication: EntityReplication;

  constructor(
    persistent: boolean,
    id: EntityId | null = null,
    hasScript: boolean = false
  ) {
    this.entity = id!;
    this.persistent = persistent;
    this._hasScript = hasScript;
    this._alwaysLocal = false;
    this._progress = new EntityProgress(this);
    this._entityReplication = new EntityReplication(this);
  }

  private _setUpdateFn(fn: (d: number, dt: number) => void) {
    this._updateFn = fn;
  }

  public onRoomJoin() {
    if (this._yjsUnsubscribe) {
      this._yjsUnsubscribe();
      this._yjsUnsubscribe = null;
    }
    const data = this.data();

    this._position = data.getComponent(Position)!;
    this._orientation = data.getComponent(Rotation)!;
    this._scale = data.getComponent(Scale)!;

    // Manually sync position in case someone edits position outside of GameEntity before it is created
    if (this.rootNode) {
      this.rootNode.position.copy(this._position);
      this.rootNode.quaternion.copy(this._orientation);
      this.rootNode.scale.copy(this._scale);
      this.rootNode.updateMatrixWorld();
    }

    const physicsBodyControl = this.getPhysicsBodyControl();
    if (physicsBodyControl) {
      const physicsPosition = this.getOffset().add(this._position);
      physicsBodyControl.setPosition(physicsPosition);
      physicsBodyControl.setRotation(this.rotation);
    }

    if (this._hasScript) {
      const script = Client.getEntityScript(this.id);
      this.data().addComponent(ScriptComponent, script);
    }

    // Initialize ECS event components when entity joins the world
    this.initializeEventComponents();

    // Emit spawned event when entity is properly added to world
    this.emitSpawnedEvent();
  }

  protected setRootNode(rootNode: Object3D) {
    this.rootNode = rootNode;
    // TODO: temporary for picking
    // @ts-expect-error we assign entity to Object3D
    this.rootNode.entity = this;
    Client.scene.add(this.rootNode);
  }

  public getRootNode() {
    return this.rootNode;
  }

  public getPhysicsBodyControl() {
    return getBodyControlFor(this.id);
  }

  public destroy() {
    if (this._selectedPanelId)
      UIManager.instance.removePanel(this._selectedPanelId);
    if (this.rootNode) this.rootNode.parent?.remove(this.rootNode);

    if (this._yjsUnsubscribe) this._yjsUnsubscribe();
  }

  public onSelect() {
    // this used to display the transform controls
    // but we are now displaying them from UserEntity,
    // based on CurrentAction
  }
  public onDeselect() {
    this.detachControls();
  }

  private _attachedControls: DuplicateControls | TransformControls | null =
    null;
  public attachTransformControls(
    controls: TransformControls | null
    // options?: AttachTransformControlsOptions
  ) {
    // const detachGivenControls =
    //   options != null ? options.detachGivenControls : true;
    // if (detachGivenControls) {
    //   controls.attachEntities([]);
    //   this.detachControls();
    // }

    if (this.transformable && this.rootNode && !this._locked) {
      this._attachedControls = controls;
    }
  }
  public attachDuplicateControls(controls: DuplicateControls) {
    controls.detach();
    this.detachControls();
    if (this.transformable && this.rootNode && !this._locked) {
      controls.attachEntity(this, this.rootNode);
      this._attachedControls = controls;
    }
  }

  public setTransformControlsSpace(space: GIZMOSPACE) {
    const controls = this._attachedControls as TransformControls;
    controls?.updateSpace(space);
  }

  public getTransformControlsSpace() {
    const controls = this._attachedControls as TransformControls;
    return controls?.getSpace();
  }

  public detachControls() {
    if (this._attachedControls instanceof TransformControls) {
      this._attachedControls?.attachEntities([]);
    }

    if (this._attachedControls instanceof DuplicateControls) {
      this._attachedControls?.detach();
    }

    this._attachedControls = null;
  }

  public selected() {
    return Client.userEntity.getSelectedEntities().includes(this);
  }

  public toJSON(): GameEntityData {
    this.getPosition(_p);
    this.getOrientation(_q);
    this.getScale(_s);

    //@ts-expect-error some
    return {
      id: this.id,
      persistent: this.persistent,
      dragging: this._dragging,
      locked: this._locked,
      // TODO: change to position.x etc
      positionX: _p.x,
      positionY: _p.y,
      positionZ: _p.z,
      orientationX: _q.x,
      orientationY: _q.y,
      orientationZ: _q.z,
      orientationW: _q.w,
      scaleX: _s.x,
      scaleY: _s.y,
      scaleZ: _s.z,
      createdBy: this.createdBy ?? null,
      state: this._state.toJSON(),

      // DO NOT TRY TO SYNC SCRIPT SINCE IT"S SYNCED THROUGH YJS
      // script: this._hasScript
      //   ? Client.getEntityScript(this.id).toString()
      //   : null,
    };
  }

  public fromJSON(json: GameEntityData) {
    if (this.id != null && this.id !== json.id) {
      console.debug("removing entity script", this.id, this.constructor.name);
      Client.removeEntityScript(this.id);
      this.entity = json.id;
      assertTrue(
        this.isInWorld,
        `Attempt to call \`fromJSON\` on an entity which is not yet in the world. id: ${this.id}, type: ${this.type}`
      );
      this.onRoomJoin();
    }
    this.persistent = json.persistent;

    const isDragging = json.dragging;
    this.setDragging(isDragging, false);

    const isLocked = typeof json.locked === "boolean" ? json.locked : false;
    this.setLocked(isLocked, false);

    //Interpolate position and orientation
    this._entityReplication.fromJSON(json);

    _s.set(json.scaleX, json.scaleY, json.scaleZ);
    this.setTransformProps(undefined, undefined, _s);
    this.setCreatedBy(json.createdBy, false);

    if (json.state) {
      this._state.fromJSON(json.state);
    }

    // DO NOT TRY TO SYNC SCRIPT SINCE IT"S SYNCED THROUGH YJS
    // if (json.script) {
    //   Client.setEntityScript(this.id, json.script as string);
    // }
  }

  public update(time: number, deltaTime: number) {
    // Handle interpolation if needed
    this._entityReplication.update(deltaTime);

    //Update entity script
    try {
      this._updateFn(time, deltaTime);
    } catch (error) {
      console.error(`Critical error in entity update loop:`, error);
    }
  }

  public updateFollowing(
    targetEntity: GameEntity,
    followDistance?: number,
    speed?: number
  ): void {
    updateFollowingMovement(this, targetEntity, followDistance, speed);
    Client.markEntityAsDirty(this, false);
  }

  public updateWandering(speed?: number): void {
    updateWanderingMovement(this, speed);
    Client.markEntityAsDirty(this, false);
  }

  public getProgress(): EntityProgress {
    return this._progress;
  }

  setGenerationPanelId(id: string | null) {
    this._generationPanelId = id;
  }

  getGenerationPanelId() {
    return this._generationPanelId;
  }

  public getIsGenerating() {
    return this._progress.inProgress();
  }

  markAsDisposed() {
    console.debug("💡", "Marking entity as disposed", this.id, this.type);
    if (this._disposed == true) {
      console.error(
        "💥",
        "we should not be disposing an entity a second time",
        this.id
      );
      return;
    }

    // Emit destroyed event before marking as disposed
    this.emitDestroyedEvent();

    this._disposed = true;
  }
  disposed() {
    return this._disposed;
  }

  private _forwardQ = new Quaternion();
  public getForwardVector(
    forwardDirection: Vector3 = new Vector3(0, 0, 1)
  ): Vector3 {
    this.getOrientation(this._forwardQ);
    forwardDirection.applyQuaternion(this._forwardQ);
    return forwardDirection;
  }
}
