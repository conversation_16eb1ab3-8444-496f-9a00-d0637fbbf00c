{"version": "2.0.0", "tasks": [{"label": "Install & Start Dev Server", "type": "shell", "command": "pnpm i && pnpm dev", "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": [], "icon": {"id": "rocket", "color": "terminal.ansiGreen"}}, {"label": "Update UI Theme", "type": "shell", "command": "pnpm update:ui-theme", "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": [], "icon": {"id": "color-mode", "color": "terminal.ansiMagenta"}}, {"label": "Lint + Fix", "type": "shell", "command": "pnpm lint --fix", "group": "build", "presentation": {"reveal": "always", "panel": "shared"}, "problemMatcher": ["$eslint-stylish"], "icon": {"id": "check-all", "color": "terminal.ansiBlue"}}]}