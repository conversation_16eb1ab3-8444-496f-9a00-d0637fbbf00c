import Jolt from "jolt-physics";
import { ConvexGeometry } from "three/examples/jsm/geometries/ConvexGeometry";

import { BufferAttribute, CylinderGeometry, Vector3 } from "three";
import { PhysicsShapeType } from "../types/PhysicsOptions";
import { vhacdDecomposeObject } from "../vhacd/vhacdDecomposeObject";
import { createConvexHullShape } from "../helpers/createConvexHullShape";
import { BodyGeometry } from "../types/Body";
import { createThreeJSGeometry } from "../lib/getGeometryFromObject";

export const BBOX_SHAPE_SETTINGS_MIN_SIZE = 0.1;

export class ShapeSettingsFactory {
  private joltModule: Awaited<ReturnType<typeof Jolt>>;

  constructor(joltModule: Awaited<ReturnType<typeof Jolt>>) {
    this.joltModule = joltModule;
  }

  /**
   * Create a Jolt.BoxShapeSettings representing a bounding box from an object.
   */
  boundingBox(geometry: BodyGeometry): Jolt.BoxShapeSettings | null {
    const threeGeometry = createThreeJSGeometry(geometry);
    if (!threeGeometry) {
      throw new Error(`Failed to create three.js geometry for bounding box`);
    }

    threeGeometry.computeBoundingBox();
    const bbox = threeGeometry.boundingBox;

    if (!bbox) {
      console.error(
        `Failed to compute bounding box for geometry: ${geometry.geometryId || "Unnamed"}`
      );
      // Return a default small box as a fallback
      return new this.joltModule.BoxShapeSettings(
        new this.joltModule.Vec3(0.1, 0.1, 0.1)
      );
    }

    // Calculate half-extents
    const halfExtents = new Vector3();
    bbox.getSize(halfExtents).multiplyScalar(0.5);

    // Make sure all three vectors are positive and > .1
    halfExtents.x = Math.max(halfExtents.x, 0.1);
    halfExtents.y = Math.max(halfExtents.y, 0.1);
    halfExtents.z = Math.max(halfExtents.z, 0.1);

    return new this.joltModule.BoxShapeSettings(
      new this.joltModule.Vec3(halfExtents.x, halfExtents.y, halfExtents.z)
    );
  }

  /**
   * Create a Jolt.ShapeSettings from an object in a sync context.
   * @param object - The object to create a shape settings for.
   * @param shapeKey - The type of shape to create.
   * @returns A Jolt.ShapeSettings.
   */
  createSync(geometry: BodyGeometry, shapeKey: PhysicsShapeType) {
    switch (shapeKey) {
      case "boundingBox":
        return this.boundingBox(geometry);
      case "plane":
        return this.plane(geometry);
      case "convexHull":
        return this.convexHull(geometry);
      case "asis":
        return this.asis(geometry);
      case "sphere":
        return this.sphere(geometry);
      case "cylinder":
        return this.cylinder(geometry);
      case "decomp":
        throw new Error(
          `Decomposition is not supported for sync ShapeSettings creation. Use createAsync instead.`
        );
      default:
        throw new UnkownShapeError(
          `Unknown shape type for sync ShapeSettings creation: ${shapeKey}`
        );
    }
  }

  /**
   * Create a Jolt.ShapeSettings from an object in a async context.
   * @param object - The object to create a shape settings for.
   * @param shapeKey - The type of shape to create.
   * @returns A Jolt.ShapeSettings.
   */
  async createAsync(
    geometry: BodyGeometry,
    shapeKey: PhysicsShapeType
  ): Promise<Jolt.ShapeSettings | null> {
    switch (shapeKey) {
      case "decomp":
        return this.decomp(geometry);
      default:
        try {
          return this.createSync(geometry, shapeKey);
        } catch (error) {
          if (error instanceof UnkownShapeError) {
            throw new Error(
              `Unknown shape type for async ShapeSettings creation: ${shapeKey}`
            );
          }
          throw error;
        }
    }
  }

  /**
   * Create a Jolt.BoxShapeSettings representing a plane from an object.
   */
  plane(geometry: BodyGeometry): Jolt.BoxShapeSettings {
    // I would have preferred to use an infinite plane for the ground,
    // instead of a box, but I could not find a way.
    const threeGeometry = createThreeJSGeometry(geometry);
    if (!threeGeometry) {
      throw new Error(`Failed to create three.js geometry for plane`);
    }
    threeGeometry.computeBoundingBox();
    const bbox = threeGeometry.boundingBox;
    const minSize = BBOX_SHAPE_SETTINGS_MIN_SIZE;

    if (!bbox) {
      console.error(
        `Failed to compute bounding box for geometry: ${geometry.geometryId || "Unnamed"}`
      );
      // Return a default small box as a fallback
      return new this.joltModule.BoxShapeSettings(
        new this.joltModule.Vec3(minSize, minSize, minSize)
      );
    }
    const xyMult = 1000;
    bbox.min.x *= xyMult;
    bbox.max.x *= xyMult;
    bbox.min.z *= xyMult;
    bbox.max.z *= xyMult;

    // Calculate half-extents
    const halfExtents = new Vector3();
    bbox.getSize(halfExtents).multiplyScalar(0.5);

    // Make sure all three vectors are positive and > .1
    halfExtents.x = Math.max(halfExtents.x, minSize);
    halfExtents.y = Math.max(halfExtents.y, minSize);
    halfExtents.z = Math.max(halfExtents.z, minSize);

    return new this.joltModule.BoxShapeSettings(
      new this.joltModule.Vec3(halfExtents.x, halfExtents.y, halfExtents.z)
    );
  }

  /**
   * Create a Jolt.ConvexHullShapeSettings from an object.
   */
  convexHull(geometry: BodyGeometry): Jolt.ConvexHullShapeSettings | null {
    try {
      const threeGeometry = createThreeJSGeometry(geometry);
      if (!threeGeometry) {
        throw new Error(`Failed to create three.js geometry for convex hull`);
      }

      const positionAttribute = threeGeometry.getAttribute("position");
      if (positionAttribute.count < 3) {
        throw new Error(`Convex hull must have at least 3 points`);
      }
      const points: Vector3[] = [];
      for (let i = 0; i < positionAttribute.count; i++) {
        const point = new Vector3();
        point.fromBufferAttribute(positionAttribute, i);
        points.push(point);
      }
      const convexGeometry = new ConvexGeometry(points);

      const convexHull = createConvexHullShape(this.joltModule, convexGeometry);

      return convexHull;
    } catch (error) {
      console.warn("💛 Error creating convex hull shape", error);
      return null;
    }
  }

  /**
   * Create a Jolt.StaticCompoundShapeSettings from an object.
   * Uses VHACD to decompose the object into multiple convex hulls.
   */
  async decomp(geometry: BodyGeometry): Promise<Jolt.ShapeSettings | null> {
    try {
      const threeGeometry = createThreeJSGeometry(geometry);
      if (!threeGeometry) {
        throw new Error(`Failed to create three.js geometry for decomposition`);
      }

      const decomposition = await vhacdDecomposeObject(threeGeometry);
      const compoundShape = new this.joltModule.StaticCompoundShapeSettings();
      const zeroVec = new this.joltModule.Vec3(0, 0, 0);
      const identityQuat = new this.joltModule.Quat(0, 0, 0, 1);
      decomposition.forEach((convexHull) => {
        if (convexHull.vertices.length <= 3) {
          //Cannot use convex hull with less than 3 vertices
          return;
        }
        const geometry = new ConvexGeometry();
        geometry.setAttribute(
          "position",
          new BufferAttribute(new Float32Array(convexHull.vertices), 3)
        );
        geometry.setIndex(
          new BufferAttribute(new Uint32Array(convexHull.indices), 1)
        );
        const shape = createConvexHullShape(this.joltModule, geometry);
        compoundShape.AddShapeShapeSettings(zeroVec, identityQuat, shape, 0);
      });
      this.joltModule.destroy(zeroVec);
      this.joltModule.destroy(identityQuat);
      return compoundShape;
    } catch (error) {
      console.warn("💛 Error creating decomposition shape", error);
      console.debug("Proceeding to return convex hull shape");
      return this.convexHull(geometry);
    }
  }

  /**
   * Create a Jolt.MeshShapeSettings from an object.
   */
  asis(geometry: BodyGeometry): Jolt.MeshShapeSettings | null {
    try {
      const threeGeometry = createThreeJSGeometry(geometry);
      if (!threeGeometry) {
        throw new Error(`Failed to create three.js geometry for as-is`);
      }

      const vertices = threeGeometry.getAttribute("position").array;
      const indices = threeGeometry.index ? threeGeometry.index.array : null;

      const triangles = new this.joltModule.TriangleList();

      if (indices) {
        // Indexed geometry
        for (let i = 0; i < indices.length; i += 3) {
          const v1 = new this.joltModule.Vec3(
            vertices[indices[i] * 3],
            vertices[indices[i] * 3 + 1],
            vertices[indices[i] * 3 + 2]
          );
          const v2 = new this.joltModule.Vec3(
            vertices[indices[i + 1] * 3],
            vertices[indices[i + 1] * 3 + 1],
            vertices[indices[i + 1] * 3 + 2]
          );
          const v3 = new this.joltModule.Vec3(
            vertices[indices[i + 2] * 3],
            vertices[indices[i + 2] * 3 + 1],
            vertices[indices[i + 2] * 3 + 2]
          );
          const triangle = new this.joltModule.Triangle(v1, v2, v3);
          triangles.push_back(triangle);
        }
      } else {
        // Non-indexed geometry
        for (let i = 0; i < vertices.length; i += 9) {
          const v1 = new this.joltModule.Vec3(
            vertices[i],
            vertices[i + 1],
            vertices[i + 2]
          );
          const v2 = new this.joltModule.Vec3(
            vertices[i + 3],
            vertices[i + 4],
            vertices[i + 5]
          );
          const v3 = new this.joltModule.Vec3(
            vertices[i + 6],
            vertices[i + 7],
            vertices[i + 8]
          );
          const triangle = new this.joltModule.Triangle(v1, v2, v3);
          triangles.push_back(triangle);
        }
      }

      const materials = new this.joltModule.PhysicsMaterialList();
      return new this.joltModule.MeshShapeSettings(triangles, materials);
    } catch (error) {
      console.error("🔺 Error creating as-is (mesh) shape", error);
      return null;
    }
  }

  /**
   * Create a Jolt.SphereShapeSettings from an object.
   */
  sphere(geometry: BodyGeometry): Jolt.SphereShapeSettings {
    const threeGeometry = createThreeJSGeometry(geometry);
    if (!threeGeometry) {
      throw new Error(`Failed to create three.js geometry for sphere`);
    }
    const radius = threeGeometry.boundingSphere?.radius || 1;
    return new this.joltModule.SphereShapeSettings(radius * 0.01);
  }

  /**
   * Create a Jolt.CylinderShapeSettings from an object.
   */
  cylinder(geometry: BodyGeometry): Jolt.CylinderShapeSettings {
    const threeGeometry = createThreeJSGeometry(geometry);
    if (!threeGeometry) {
      throw new Error(`Failed to create three.js geometry for cylinder`);
    }
    if (threeGeometry instanceof CylinderGeometry) {
      return new this.joltModule.CylinderShapeSettings(
        threeGeometry.parameters.height / 2,
        threeGeometry.parameters.radiusBottom
      );
    } else {
      console.error(
        `Failed to create cylinder shape for geometry: ${geometry.geometryId || "Unnamed"}`
      );

      // Return a default small cylinder as a fallback
      return new this.joltModule.CylinderShapeSettings(0.1, 0.1);
    }
  }
}

class UnkownShapeError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "UnkownShapeError";
  }
}
