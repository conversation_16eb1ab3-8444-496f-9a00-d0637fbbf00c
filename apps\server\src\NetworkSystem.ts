import {
  createServer as createHttpServer,
  Server as HttpServer,
} from "node:http";
import { createServer as createHttpsServer } from "node:https";
import { ByteStreamWriter } from "@evenstar/byteform";
import express, { Express } from "express";
import exitHook from "exit-hook";
import Emittery from "emittery";
import {
  CertificateDescriptor,
  ServerOptions,
  Connection,
  ServerAddress,
  NoArgs,
  WebSocketServer,
  WebTransportServer,
  SecureServerOptions,
  ProviderType,
  Router,
  MessageSchema,
  EntityId,
  ComponentId,
} from "@nilo/network";
import { getLogger } from "@nilo/logger";
import { EntityStore } from "./EntityStore";

const logger = getLogger({ module: "network-system" });

interface NetworkSystemEvents {
  ready: NoArgs;
  connection: Connection;
  close: NoArgs;
}

interface NetworkSystemOptions {
  wtOptions: SecureServerOptions;
  wsOptions: ServerOptions;
}

const getHost = (address: <PERSON>Address) =>
  `${address.address}${address.port ? `:${address.port}` : ""}`;

const createAPIServer = (
  createOptions:
    | {
        app: Express;
        secure: boolean;
        certificate: CertificateDescriptor;
      }
    | {
        app: Express;
        secure: false;
      }
) => {
  return createOptions.secure
    ? createHttpsServer(
        {
          cert: createOptions.certificate.cert,
          key: createOptions.certificate.key,
        },
        createOptions.app
      )
    : createHttpServer(createOptions.app);
};

/**
 * Handles per-client networking logic including message routing and broadcasting.
 * Each client connection gets its own ClientHandler instance.
 */
export class ClientHandler {
  /** The client's network connection */
  private _conn: Connection;
  /** Reference to the parent network system */
  private _ns: NetworkSystem;
  /** Reusable buffer for message serialization */
  private _writeBuf: ByteStreamWriter = new ByteStreamWriter(256, {
    maxByteLength: 64 * 1024, // 64KiB *should* be plenty
  });

  /** A map of sets of dirty entities and their dirty components. */
  private _dirtyEntities: Map<EntityId, Set<ComponentId>> = new Map();

  /** A list of all entities that have been spawned on this client. */
  private _spawnedEntities: Set<EntityId> = new Set();

  /** This client's user entity.
   * TODO: this is assigned by the client for now, but ideally would be assigned by the server.
   */
  private _userEntity?: EntityId;

  /**
   * Creates a new client handler for a connection.
   * @param connection The client's network connection
   * @param ns The parent network system
   */
  constructor(connection: Connection, ns: NetworkSystem) {
    this._conn = connection;
    this._ns = ns;
    this.route();
  }

  /** Sets up message routing and channel configuration. */
  private route() {
    const router = new Router<ClientHandler>()
      .addHandler(MessageSchema.Ping, (client, _msg) => {
        client.send(MessageSchema.Ping, {});
      })
      .addHandler(MessageSchema.UserJoin, (client, entity) => {
        // technically allows the client to join multiple times.
        client._userEntity = entity;

        // when a user joins, all of the server state is dirty
        this._ns.entityStore.alive().forEach((e, id) => {
          this._dirtyEntities.set(id, e.all());
        });
      })
      .addHandler(MessageSchema.SpawnEntity, (client, id) => {
        const ns = client._ns;
        client._spawnedEntities.add(id);
        if (ns.entityStore.ensureSpawned(id)) {
          ns.markEntityDirty(id, undefined, client);
        }
      })
      .addHandler(MessageSchema.KillEntity, (client, id) => {
        const ns = client._ns;
        client._spawnedEntities.delete(id);
        if (ns.entityStore.kill(id)) {
          ns.markEntityDirty(id, undefined, client);
        }
      })
      .addHandler(MessageSchema.InsertComponent, (client, msg) => {
        const ns = client._ns;
        const entity = ns.entityStore.spawn(msg.entity);
        if (entity.insert(msg.component, msg.data)) {
          ns.markEntityDirty(msg.entity, msg.component, client);
        }
      })
      .addHandler(MessageSchema.RemoveComponent, (client, msg) => {
        const ns = client._ns;
        const entity = ns.entityStore.spawn(msg.entity);
        if (entity.remove(msg.component)) {
          ns.markEntityDirty(msg.entity, msg.component, client);
        }
      });

    const onData = router.route(this);
    const { unreliableUnordered, reliableOrdered } = this._conn.channels;
    if (this._conn.provider === ProviderType.WebTransport) {
      unreliableUnordered.onReceive(onData);
      reliableOrdered.onReceive(onData);
    } else {
      reliableOrdered.onReceive(onData);
    }
  }

  /**
   * Sends a serialized message to this specific client.
   * @param schema The message schema to use for serialization
   * @param msg The message data to send
   */
  public send<T>(schema: MessageSchema<T>, msg: T) {
    const data = schema.serialize(this._writeBuf, msg);
    this._conn.channels.reliableOrdered.send(data);
  }

  /**
   * Marks an entity (and an optional component) as dirty on this client.
   * @param id The ID of the entity to mark as dirty.
   * @param component An optional ID of a specific component to mark as dirty.
   */
  public markEntityDirty(id: EntityId, component?: ComponentId) {
    // get or insert the entity
    let entity = this._dirtyEntities.get(id);
    if (!entity) {
      // TODO: is the overhead of re-allocating this set too much?
      entity = new Set();
      this._dirtyEntities.set(id, entity);
    }

    // if the component is specified, insert it
    if (component) {
      entity.add(component);
    }
  }

  /** Updates this client with all dirty data. */
  public flushDirty() {
    // do not sync to unjoined clients
    if (!this._userEntity) {
      return;
    }

    // synchronize all dirty entities
    for (const [id, components] of this._dirtyEntities) {
      // look up whether this entity exists on the client
      const local = this._spawnedEntities.has(id);

      // fetch the server's entity data
      const entity = this._ns.entityStore.get(id);

      // assert entity exists
      if (!entity) {
        // if entity is alive on client, kill it
        if (local) {
          this.send(MessageSchema.KillEntity, id);
          this._spawnedEntities.delete(id);
        }

        // skip updating components whether killed or not
        continue;
      }

      // spawn entity if not local
      if (!local) {
        this.send(MessageSchema.SpawnEntity, id);
        this._spawnedEntities.add(id);
      }

      // update each component
      for (const component of components) {
        const data = entity.get(component);
        if (data) {
          this.send(MessageSchema.InsertComponent, {
            entity: id,
            component,
            data,
          });
        } else {
          this.send(MessageSchema.RemoveComponent, { entity: id, component });
        }
      }
    }

    this._dirtyEntities.clear();
  }

  /**
   * Broadcasts a serialized message to all other connected clients.
   * @param schema The message schema to use for serialization
   * @param msg The message data to broadcast
   */
  public broadcast<T>(schema: MessageSchema<T>, msg: T) {
    // serialize once for all clients
    const data = schema.serialize(this._writeBuf, msg);

    // broadcast to all other clients
    this.forOthers((client) => {
      client._conn.channels.reliableOrdered.send(data);
    });
  }

  /** Performs an operation on each network client except for this one. */
  public forOthers(op: (client: ClientHandler) => void) {
    for (const client of this._ns.clients) {
      if (client !== this) {
        op(client);
      }
    }
  }

  /** Ran when this client disconnects. */
  public onClose() {
    if (this._userEntity) {
      const ns = this._ns;
      if (ns.entityStore.kill(this._userEntity)) {
        ns.markEntityDirty(this._userEntity);
      }
    }
  }
}

export class NetworkSystem extends Emittery<NetworkSystemEvents> {
  private _express: Express;
  private _http: HttpServer;

  private _options: NetworkSystemOptions;
  private _clients = new Set<ClientHandler>();

  /**
   * The store of entities in the ECS world.
   */
  public entityStore: EntityStore = new EntityStore();

  declare private _ws: WebSocketServer;
  declare private _wt: WebTransportServer;

  declare private _ready: Promise<void>;
  declare private _closed: Promise<void>;

  public constructor(options: NetworkSystemOptions) {
    super();

    this._options = options;

    this._express = express();
    this._http = createAPIServer(
      "certificate" in options.wsOptions
        ? {
            app: this._express,
            certificate: options.wsOptions.certificate,
            secure: true,
          }
        : {
            app: this._express,
            secure: false,
          }
    );

    this.onConnection = this.onConnection.bind(this);

    this.initWs(options);
    this.initWt(options);

    this.initReadyState();
    this.initCloseState();

    exitHook((signal) => {
      logger.info(`Exiting with signal: ${signal}`);
    });
  }

  private onConnection(connection: Connection) {
    const client = new ClientHandler(connection, this);

    this._clients.add(client);

    connection.once("close").then(() => {
      client.onClose();
      this._clients.delete(client);

      logger.info(`Connection with id ${connection.id} has been closed`);
    });

    logger.info(
      `Received new connection from ${connection.provider} with id ${connection.id}`
    );
  }

  private initReadyState() {
    const wsReady = this._ws.once("ready").then((address) => {
      this._ws.on("connection", this.onConnection);
      const isWss = "certificate" in this._options.wsOptions;
      logger.info(
        `WebSocket server ready at ${isWss ? "wss" : "ws"}://${getHost(address)}`
      );
    });

    const wtReady = this._wt.once("ready").then((address) => {
      this._wt.on("connection", this.onConnection);
      logger.info(`WebTransport server ready at ${getHost(address)}`);
    });

    this._ready = Promise.all([wsReady, wtReady]).then(() => {
      logger.info("Network system ready");
      this.emit("ready");
    });
  }

  private initCloseState() {
    const wsClosed = this._ws.once("close").then(() => {
      this._ws.off("connection", this.onConnection);
      logger.info("WebSocket server closed");
    });

    const wtClosed = this._wt.once("close").then(() => {
      this._wt.off("connection", this.onConnection);
      logger.info("WebTransport server closed");
    });

    this._closed = Promise.all([wsClosed, wtClosed]).then(() => {
      logger.info("Network system closed");
      this.emit("close");
    });
  }

  private initWs(options: NetworkSystemOptions) {
    this._ws = new WebSocketServer(options.wsOptions, this._http);
  }

  private initWt(options: NetworkSystemOptions) {
    this._wt = new WebTransportServer({
      ...options.wtOptions,
      certificate: options.wtOptions.certificate,
    });
  }

  public get clients() {
    return this._clients.values();
  }

  public get server(): HttpServer {
    return this._http;
  }

  public get express(): Express {
    return this._express;
  }

  public start() {
    this._ws.startServer();
    this._wt.startServer();

    // TODO: stop this automatically
    setInterval(this.flushDirty.bind(this), 50);
  }

  public stop() {
    this._ws.stopServer();
    this._wt.stopServer();
  }

  /**
   * Marks an entity (and an optional component) as dirty on every entity (but one, maybe).
   * @param entity The ID of the entity to mark as dirty.
   * @param component An optional ID of a specific component to mark as dirty.
   * @param client An optional client to exclude from marking dirtiness.
   */
  public markEntityDirty(
    entity: EntityId,
    component?: ComponentId,
    exclude?: ClientHandler
  ) {
    for (const client of this._clients) {
      // skip the excluded client (if there is one)
      if (exclude && client === exclude) {
        continue;
      }

      // propagate the dirtiness to this client
      client.markEntityDirty(entity, component);
    }
  }

  /** Flushes all dirty to clients. */
  public flushDirty() {
    // flush each client's dirty data
    for (const client of this._clients) {
      client.flushDirty();
    }
  }
}
