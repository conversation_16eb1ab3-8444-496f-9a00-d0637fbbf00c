import { Group, Object3D, Vector3 } from "three";
import { Client } from "../client";
import { OnMountedCallback } from "../util/controls/brushControls/BrushControlsHandler";
import { setObjectRaycastable } from "../util/RaycastUtils";
import {
  brushStrokeUniqueIdGenerate,
  deregisterBrushStrokeEntity,
  registerBrushStrokeEntity,
} from "../util/controls/brushControls/BrushStrokeEntityRegister";
import { KeptPointsSetContainer } from "../util/controls/brushControls/eraser/EraserReportFromBuffer";
import { arrayCopy } from "../util/ArrayUtils";
import { brushPointClone } from "../util/controls/brushControls/utils/BrushPoint";
import removeEntity from "../command/helpers/removeEntity";
import {
  brushStrokeEntityPositionsValid,
  brushStrokeEntityValidity,
  BrushStrokeEntityValidity,
} from "../util/controls/brushControls/BrushStrokeEntityValidity";
import { brushStrokeEntityDataEquals } from "../util/controls/brushControls/BrushStrokeEntityUtils";
import { EraserControls } from "../util/controls/brushControls/EraserControls";
import { BrushControls } from "../util/controls/brushControls/BrushControls";
import { LiveblocksEntity } from "./NetworkEntity";
import {
  BrushStrokeEntityData,
  BrushPoint,
  BrushType,
  EntityType,
} from "@/liveblocks.config";
import { EntityId, makeUniqueKey } from "@nilo/ecs";

const BRUSH_STROKE_ENTITY_RAYCASTABLE: boolean = false;

const _p0 = new Vector3();

const LABEL_BY_BRUSH_TYPE: Record<BrushType, string> = {
  [BrushType.GRASS]: "grass",
  [BrushType.WALL]: "wall",
  [BrushType.WATER]: "water",
  [BrushType.ERASER]: "eraser",
};
const MIN_POINTS_COUNT = 2;

export function withBrush(callback: OnMountedCallback) {
  Client.userEntity.brushControlsHandler().loadWithCallback(callback);
}
function _createGroup(groupName: string) {
  const group = new Group();
  group.name = groupName;
  // group.matrixAutoUpdate=false
  return group;
}

interface AddPointOptions {
  sync: boolean;
  notify: boolean;
}
interface RemoveEntityOptions {
  removePoints: boolean;
  computeAllNodes: boolean;
}
interface DebugStats {
  toJSONCount: number;
  fromJSONCount: number;
}

export enum BrushStrokeEntityGroupName {
  GROUP_CONTAINER = "generatedObjects-GROUP-CONTAINER",
  WALL_LOW_RES = "wallLowRes-GROUP",
  WALL_HIGH_RES = "wallHighRes-GROUP",
  GRASS = "grass-GROUP",
  WATER = "water-GROUP",
}

export class BrushStrokeEntity extends LiveblocksEntity {
  private _addedToClient: boolean = false;
  private root: Group = _createGroup("group");

  public static override readonly UNIQUE_ID =
    makeUniqueKey("BrushStrokeEntity");

  public override type = EntityType.BrushStrokeEntity as const;

  private _brushType: BrushType | null = null;
  private _brushStrokeId: number | null = null;
  private _positions: BrushPoint[] = [];
  private _completed: boolean = false;
  // here we add 2 levels of hierarchy
  // inside this entity.
  // The reason is that in the function entityFromObject from /packages/physics-with-jolt/src/lib/getGeometryFromObject.ts
  // we check if an object is tied to an entity,
  // by checking its .entity property
  // as well as its parent .entity property.
  // Therefore objects that we'll store under here
  // would be searched for, and that could crash as the function assumes there should always be a parent.
  // That said, I've updated it to return null if no parent is found,
  // so this hierarchy may not fully be required anymore. But I've kept it for tidiness anyway.
  private _generatedObjectsGroup = _createGroup(
    BrushStrokeEntityGroupName.GROUP_CONTAINER
  );
  private _wallLowResGroup = _createGroup(
    BrushStrokeEntityGroupName.WALL_LOW_RES
  );
  private _wallHighResGroup = _createGroup(
    BrushStrokeEntityGroupName.WALL_HIGH_RES
  );
  private _grassGroup = _createGroup(BrushStrokeEntityGroupName.GRASS);
  private _waterGroup = _createGroup(BrushStrokeEntityGroupName.WATER);
  private _debugStats: DebugStats = { toJSONCount: 0, fromJSONCount: 0 };

  constructor(id: EntityId | null = null) {
    super(true, id, true); // persistent
    this.setRootNode(this.root);
    this.root.name = "brush_not_set-" + this.id;

    this.root.add(this._generatedObjectsGroup);
    this._generatedObjectsGroup.add(
      ...[
        this._wallLowResGroup,
        this._wallHighResGroup,
        this._grassGroup,
        this._waterGroup,
      ]
    );
  }
  addedToClient(): boolean {
    return this._addedToClient;
  }
  addToClient() {
    if (this.disposed()) {
      throw new Error(
        "we can't add an entity that has been disposed: " + this.id
      );
    }
    if (this._addedToClient == true) {
      console.warn("already added, WHY??", this.id, this.brushStrokeId());
      return;
    }
    Client.addEntityToWorld(this);
    this._addedToClient = true;
  }
  public brushType() {
    return this._brushType;
  }
  public brushStrokeId() {
    return this._brushStrokeId;
  }
  public addWallLowResObject(object: Object3D) {
    if (object.parent == this._wallLowResGroup) {
      return;
    }
    this._wallLowResGroup.add(object);
  }
  public addWallHighResObject(object: Object3D) {
    if (object.parent == this._wallHighResGroup) {
      return;
    }
    this._wallHighResGroup.add(object);
  }
  public addGrassObject(object: Object3D) {
    if (object.parent == this._grassGroup) {
      return;
    }
    this._grassGroup.add(object);
  }
  public addWaterObject(object: Object3D) {
    if (object.parent == this._waterGroup) {
      return;
    }
    this._waterGroup.add(object);
  }

  setBrushType(
    brushType: BrushType,
    brushStrokeId: number,
    controls: BrushControls
  ) {
    if (brushType == this._brushType) {
      return;
    }
    if (this._brushType == null) {
      this._brushType = brushType;
      this.root.name = `${LABEL_BY_BRUSH_TYPE[this._brushType]}-${this.id}`;
      const _setBrushTypeWithControls = (controls: BrushControls) => {
        this._brushStrokeId =
          brushStrokeId ?? brushStrokeUniqueIdGenerate(brushType);
        registerBrushStrokeEntity(this);
        controls.startNewBrushStroke(brushType, this._brushStrokeId);
      };
      if (controls) {
        _setBrushTypeWithControls(controls);
      } else {
        withBrush(_setBrushTypeWithControls);
      }
    } else {
      console.error(
        "changing the brush type after creation is not yet implemented"
      );
    }
  }
  private _setPointPosition(
    index: number,
    position: BrushPoint,
    controls: BrushControls
  ) {
    const brushType = this._brushType;
    const brushStrokeId = this._brushStrokeId;
    if (brushType == null || brushStrokeId == null) {
      console.error(
        "BrushStrokeEntity.addPoint: brushType or brushStrokeId is null",
        this
      );
      return;
    }
    const notify = false; // not sure if that should be true of false anymore
    const _setPointPositionWithControls = (controls: BrushControls) => {
      _p0.set(position[0], position[1], position[2]);
      this._positions[index] = [...position];
      controls.setPointPosition(brushType, brushStrokeId, index, _p0, notify);
    };
    if (controls) {
      _setPointPositionWithControls(controls);
    } else {
      withBrush(_setPointPositionWithControls);
    }
  }
  addPoint(
    position: BrushPoint,
    options: AddPointOptions,
    controls?: BrushControls
  ) {
    const brushType = this._brushType;
    const brushStrokeId = this._brushStrokeId;
    if (brushType == null || brushStrokeId == null) {
      console.error(
        "BrushStrokeEntity.addPoint: brushType or brushStrokeId is null",
        this
      );
      return;
    }
    const _addPointWithControls = (controls: BrushControls) => {
      if (position == null) {
        console.error("pushing a null for position");
        return;
      }
      this._positions.push(position);
      _p0.set(position[0], position[1], position[2]);
      controls.addPointAtPosition(
        brushType,
        brushStrokeId,
        _p0,
        options.notify
      );

      if (
        this.addedToClient() == false &&
        this._positions.length >= MIN_POINTS_COUNT
      ) {
        // we could add the entity to the client when we instantiate it,
        // but we want to avoid this for 2 reasons:
        // - currently the pointerdown event is triggered when we click on the radial menu,
        //   which then leads to the creation of an entity when we switch brush type, and that entity will have no points,
        //   and therefore no mesh
        // - we may click on the viewport at various point, without dragging the cursor, and we probably don't want to create
        //   empty entities in those situations
        this.addToClient();
      }

      if (options.sync == true && this._addedToClient == true) {
        // do not call markEntityAsDirty if the entity has not yet been added to the client,
        // as otherwise a second one will be created from the JSON.
        Client.markEntityAsDirty(this, false);
      }
    };

    if (controls) {
      _addPointWithControls(controls);
    } else {
      withBrush(_addPointWithControls);
    }
  }

  private _setPointsFromJSON(positions: BrushPoint[], controls: BrushControls) {
    if (positions.length < this._positions.length) {
      console.error(
        "the new positions count is less than the current positions count, this is NOT yet implemented. Such is life."
      );
      return;
    }
    const minCount = Math.min(positions.length, this._positions.length);
    // for (let i = 0; i < minCount; i++) {
    //   const position = positions[i];
    //   const currentPosition = this._positions[i];
    //   if (
    //     position[0] != currentPosition[0] ||
    //     position[1] != currentPosition[1] ||
    //     position[2] != currentPosition[2]
    //   ) {
    //     console.error("different positions", position, currentPosition);
    //   }
    // }
    for (let i = 0; i < minCount; i++) {
      const newPosition = positions[i];
      // const isLast: boolean = i == positions.length - 1;

      this._setPointPosition(i, newPosition, controls);
    }
    for (let i = this._positions.length; i < positions.length; i++) {
      const isLast: boolean = i == positions.length - 1;

      this.addPoint(
        positions[i],
        {
          sync: isLast,
          notify: isLast,
        },
        controls
      );
    }
  }
  private _setPointsFromSplit(positions: BrushPoint[]) {
    this._positions.length = positions.length;
    for (let i = 0; i < positions.length; i++) {
      const position = positions[i];
      this._positions[i] = [...position];
    }
    this._completed = true;
  }

  rebuildWithIndices(
    reportItem: KeptPointsSetContainer,
    controls: BrushControls,
    eraserControls: EraserControls
  ) {
    if (reportItem.startIndex < 0 && reportItem.endIndex < 0) {
      eraserControls.addDeletedEntity(this);
      this.remove({ removePoints: false, computeAllNodes: true }, controls);
      return;
    }

    // make sure to store the entity as a rebuiltEntity
    // before actually update it, so we can store its initial state
    eraserControls.addRebuiltEntity(this);

    const newPointsCount = reportItem.endIndex - reportItem.startIndex + 1;
    const newPoints: BrushPoint[] = new Array(newPointsCount);

    let newPointIndex = 0;
    for (let i = reportItem.startIndex; i <= reportItem.endIndex; i++) {
      const position = this._positions[i];
      if (position) {
        newPoints[newPointIndex] = [...position];
        newPointIndex++;
      } else {
        console.error(`no position at i=${i}`, [...this._positions]);
      }
    }
    arrayCopy(newPoints, this._positions);
    this._syncToClient();
  }
  createNewEntityFromIndices(
    reportItem: KeptPointsSetContainer,
    controls: BrushControls
  ): BrushStrokeEntity | undefined {
    if (this._brushType == null) {
      console.error(
        "createNewEntityFromIndices: this._brushType should not be null"
      );
      return;
    }
    const entity = new BrushStrokeEntity();

    entity.setBrushType(this._brushType, reportItem.newId, controls);
    const newPointsCount = reportItem.endIndex - reportItem.startIndex + 1;
    const newPoints: BrushPoint[] = new Array(newPointsCount);
    let newPointIndex = 0;
    for (let i = reportItem.startIndex; i <= reportItem.endIndex; i++) {
      const position = this._positions[i];
      newPoints[newPointIndex] = brushPointClone(position);
      newPointIndex++;
    }
    entity._setPointsFromSplit(newPoints);
    registerBrushStrokeEntity(entity);
    entity.addToClient();
    return entity;
  }
  // setFromEraserRedo(json: BrushStrokeEntityData, controls: BrushControls) {
  //   const brushType = this.brushType();
  //   const brushStrokeId = this.brushStrokeId();
  //   if (brushType != null && brushStrokeId != null) {
  //     console.debug(
  //       "setFromEraserRedo addEntity",
  //       this.id,
  //       brushType,
  //       brushStrokeId,
  //       json
  //     );
  //     controls.startNewBrushStroke(brushType, brushStrokeId);
  //     registerBrushStrokeEntity(this);
  //     this._setPointsFromJSON(json.positions, controls);
  //     this.fromJSON(json);
  //     this.addToClient();
  //   } else {
  //     console.error(
  //       `missing brushType ${brushType} or brushStrokeId ${brushStrokeId}`
  //     );
  //   }
  // }
  remove(options: RemoveEntityOptions, controls: BrushControls) {
    deregisterBrushStrokeEntity(this);
    removeEntity(this);
    this._addedToClient = false;
    if (
      options.removePoints &&
      this._brushType != null &&
      this._brushStrokeId != null
    ) {
      const brushType = this._brushType;
      const brushStrokeId = this._brushStrokeId;
      controls.removeBrushStroke(brushType, brushStrokeId);
      if (options.computeAllNodes == true) {
        controls.computeAllNodes();
      }
      // we reset the points so that if we re-add the entity,
      // such as with the redo of the eraser,
      // when going through toJSON, it will update and add its points to wasm
      this._positions.length = 0;
    }
  }

  markAsCompleted(controls?: BrushControls) {
    if (this._completed == true) {
      return;
    }
    const _markAsCompletedWithControls = (controls: BrushControls) =>
      controls.onDrawCompleted();
    if (controls) {
      _markAsCompletedWithControls(controls);
    } else {
      withBrush(_markAsCompletedWithControls);
    }
    if (BRUSH_STROKE_ENTITY_RAYCASTABLE) {
      this.root.traverse(setObjectRaycastable);
    }
    this._completed = true;
    this._syncToClient();
  }
  setCompleted(completed: boolean, controls: BrushControls) {
    if (this._completed == completed) {
      return;
    }
    if (completed) {
      this.markAsCompleted(controls);
    }
    this._completed = completed;
  }
  private _syncToClient() {
    Client.markEntityAsDirty(this, false);
  }

  //#region JSON
  positionsValid(target: BrushStrokeEntityValidity) {
    brushStrokeEntityPositionsValid(this._positions, target);
  }
  jsonValid(): boolean {
    if (this._brushType == null) {
      return false;
    }
    if (this._brushStrokeId == null) {
      return false;
    }
    if (this._positions.length == 0) {
      return false;
    }
    if (this._positions[0] == null) {
      return false;
    }

    return true;
  }
  public override toJSON(): BrushStrokeEntityData {
    if (this._brushType == null) {
      console.error("BrushStrokeEntity.brushType is null", this);
      this._brushType = BrushType.WALL;
    }
    if (this._brushStrokeId == null) {
      console.error("BrushStrokeEntity.brushStrokeId is null", this);
      throw new Error("brushStroke error");
    }
    // if (this._positions.length == 0) {
    //   throw new Error("positions should not be empty");
    // }
    // if (this._positions[0] == null) {
    //   throw new Error("positions should not be null");
    // }
    // ensurePositionsValid(this._positions);
    const validity = brushStrokeEntityValidity(this);
    if (validity.valid == false) {
      throw new Error(validity.reason);
    }

    this._debugStats.toJSONCount++;
    return {
      ...super.toJSON(),
      type: this.type,
      brushType: this._brushType,
      brushStrokeId: this._brushStrokeId,
      // cloning the positions here isn't ideal,
      // but it's much safer to be sure it is cloned,
      // so that we know that if we save the return of this method somewhere,
      // it will not change under our feet.
      // Our Stinky Feet!!
      positions: this._positions.map((p) => [...p]),
      completed: this._completed,
    };
  }

  public override fromJSON(json: BrushStrokeEntityData) {
    super.fromJSON(json);
    const validity = brushStrokeEntityValidity(this);
    if (validity.valid == true) {
      // Only check that both json are equals if the current entity is valid.
      // If it isn't valid, we can assume that the entity hasn't yet been setup,
      // so no check is required yet.
      const currentJSON = this.toJSON();
      const jsonEquals = brushStrokeEntityDataEquals(json, currentJSON);
      if (jsonEquals == true) {
        return;
      }
    }

    withBrush((controls) => {
      this.setBrushType(json.brushType, json.brushStrokeId, controls);
      // check
      brushStrokeEntityPositionsValid(json.positions, validity);
      if (validity.valid == false) {
        throw new Error(validity.reason);
      }
      // apply if check passed
      this._setPointsFromJSON(json.positions, controls);
      this.setCompleted(json.completed, controls);
      this._debugStats.fromJSONCount++;
    });
  }
}
