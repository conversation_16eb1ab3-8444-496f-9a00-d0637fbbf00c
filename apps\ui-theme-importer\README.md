# UI Theme Importer

A monorepo package for importing and processing Figma design tokens into Tailwind CSS theme variables.

## Usage

From the root of the monorepo:

```bash
# Update the UI theme (runs all three scripts in sequence)
pnpm update:ui-theme

# Or run individual scripts from the ui-theme-importer package
pnpm --filter @nilo/ui-theme-importer write-files           # Full pipeline
pnpm --filter @nilo/ui-theme-importer write-files:no-docs   # Skip annotation
pnpm --filter @nilo/ui-theme-importer write-files:no-resolve # Skip variable resolution
pnpm --filter @nilo/ui-theme-importer write-files:minimal   # Skip both annotation and resolution
```

## Scripts

- **`write-files`**: Full pipeline - converts Figma tokens, annotates with utility classes, and resolves variables
- **`write-files:no-docs`**: Skips annotation step (no utility class examples in comments)
- **`write-files:no-resolve`**: Skips variable resolution step (keeps var() references)
- **`write-files:minimal`**: Minimal conversion - just converts Figma to CSS, no annotation or resolution

## Pipeline Steps

1. **Convert Figma → Tailwind CSS**: Always runs, converts design tokens to CSS custom properties
2. **Annotate with utility examples**: Optional, adds Tailwind utility class examples as comments
3. **Resolve CSS variables**: Optional, replaces var() references with actual values

## Files

- `src/main.js` - Main orchestrator that runs the pipeline
- `src/config.js` - Common configuration for paths and settings
- `src/convert-tokens-to-tailwind.js` - Figma to Tailwind conversion logic
- `src/annotate-tailwind-theme-vars.js` - Annotation logic
- `src/resolve-css-variables.js` - Variable resolution logic
- `tokens/figma-plugin-export.json` - Source Figma design tokens

## Configuration

The `src/config.js` file contains all the common configuration including:

- Paths to input tokens and output CSS files
- Default prefix for CSS variables
- Semantic variable replacements
