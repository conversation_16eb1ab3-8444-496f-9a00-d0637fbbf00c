import { Object3D } from "three";
import { JsonObject } from "@liveblocks/client";
import { EnvironmentType, IEnvironment } from "./environment/types";
import { SkyEnvironment } from "./environment/Sky";
import { TextureEnvironment } from "./environment/Texture";
import { LiveblocksEntity } from "./NetworkEntity";
import { EnvironmentEntityData } from "@/liveblocks.config";
import { makeUniqueKey } from "@nilo/ecs";

export class EnvironmentEntity extends LiveblocksEntity {
  // entity type
  public override readonly type: string = "EnvironmentEntity";

  public static override readonly UNIQUE_ID =
    makeUniqueKey("EnvironmentEntity");

  // three.js local scenegraph
  private root = new Object3D();

  private environment: IEnvironment = new TextureEnvironment();
  // private environment: IEnvironment = new SkyEnvironment();

  private stateCopy: EnvironmentEntityData | null = null;

  constructor() {
    super(true); // persistent
    this.entity = "environment"; // Override default id, so it's the same for all clients

    this.root.name = "EnvironmentEntity";
    this.root.add(this.environment);

    this.setRootNode(this.root);
  }

  public saveState() {
    if (this.stateCopy === null) {
      this.stateCopy = this.toJSON();
    }
  }

  public restoreState() {
    if (this.stateCopy !== null) {
      this.fromJSON(this.stateCopy);
      this.stateCopy = null;
    }
  }

  public resetState() {
    this.stateCopy = null;
  }

  getCurrent() {
    return this.environment;
  }

  getEnvironmentType() {
    return this.environment.type;
  }

  setEnvironmentType(type: EnvironmentType) {
    if (this.environment.type === type) {
      return;
    }

    this.environment.onClear();
    this.root.remove(this.environment);

    switch (type) {
      case EnvironmentType.Sky:
        this.environment = new SkyEnvironment();
        break;
      case EnvironmentType.Texture:
        this.environment = new TextureEnvironment();
        break;
      default:
        throw new Error(`Invalid environment type: ${type}`);
    }

    this.root.add(this.environment);
  }

  public override toJSON(): EnvironmentEntityData {
    return {
      ...(super.toJSON() as EnvironmentEntityData),
      environmentType: this.getEnvironmentType(),
      environment: this.environment.toJSON() as unknown as JsonObject,
    };
  }

  public override fromJSON(json: EnvironmentEntityData) {
    super.fromJSON(json);

    // TODO: This is blocked to avoid network updates, e.g. it makes environment static since we do not have a way to update it yet

    // this.setEnvironmentType(json?.environmentType || this.getEnvironmentType());
    // this.environment.fromJSON(json?.environment || {});

    // if (this.stateCopy !== null) {
    //   this.stateCopy = this.toJSON();
    // }
  }
}
