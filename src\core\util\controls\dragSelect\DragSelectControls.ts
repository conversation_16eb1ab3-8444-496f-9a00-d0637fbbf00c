import {
  Vector2,
  Box2,
  Vector3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  PerspectiveC<PERSON>ra,
  Matrix4,
  Box3,
  Object3D,
} from "three";
import { EntitySelectionMode } from "../../EntitySelectionMode";
import { Client } from "@/core/client";
import { GameEntity } from "@/core/entity";
import { EntityType } from "@/liveblocks.config";
import { runCommands } from "@/core/command";
import SelectEntitiesCommand from "@/core/command/commands/selectEntities";
import { getFeatureFlag } from "@/utils/feature-flags";

export interface DragSelectControls {
  enable: () => void;
  disable: () => void;
}
interface Object3DWithEntity extends Object3D {
  entity?: GameEntity;
}

export let isMultiSelectionActive: boolean = false;

export function dragSelectControlsCreate(
  _domElement: HTMLElement
): DragSelectControls {
  const SELECTION_RECT_ELEMENT = selectionRectCreate();
  let dragStartSelectedEntities: Game<PERSON>nti<PERSON>[] = [];
  const _rectStart = new Vector2();
  const _rectEnd = new Vector2();
  const _rectSize = new Vector2();
  const v3 = new Vector3();
  const v2 = new Vector2();
  const v21 = new Vector2();
  const v22 = new Vector2();
  const v3tl = new Vector3();
  const v3bl = new Vector3();
  const v3tr = new Vector3();
  const v3br = new Vector3();
  const planeNear = new Plane();
  const planeFar = new Plane();
  const planeRight = new Plane();
  const planeLeft = new Plane();
  const planeTop = new Plane();
  const planeBottom = new Plane();
  const _box2D = new Box2();
  const _box2DClipSpace = new Box2();
  const frustum = new Frustum();
  const _entitiesInRect: GameEntity[] = [];
  const _objectBbox = new Box3();
  const _posOnScreen = new Vector3();
  const _posOnScreen2D = new Vector2();
  let _userHasDraggedFarEnough: boolean = false;
  const SELECTABLE_ENTITY_TYPES: Set<EntityType> = new Set([
    EntityType.MeshEntity,
    EntityType.PrimitiveEntity,
  ]);
  let enabled: boolean = true;

  dragSelectControlsAddEvents();

  function dragSelectControlsAddEvents() {
    document.body.addEventListener("mousedown", onMouseDown);
  }
  function onMouseDown(event: MouseEvent) {
    _userHasDraggedFarEnough = false;
    // enabled, not added listeners yet, and only left mouse button
    if (
      !enabled ||
      isMultiSelectionActive ||
      event.buttons !== 1 ||
      !event.ctrlKey
    )
      return;
    dragStartSelectedEntities = Client.userEntity.getSelectedEntities();
    // no rectangle selection if clicked on entity
    isMultiSelectionActive = true;
    Client.userEntity.setTransformControlsVisibility(false);

    document.body.style.cursor = "auto";

    document.body.addEventListener("pointerup", onPointerUp);
    document.body.addEventListener("pointermove", onPointerMove);
    _rectStart.set(event.clientX, event.clientY);
    _rectEnd.set(event.clientX, event.clientY);
    updateRectangle();
  }
  function onPointerMove(event: PointerEvent) {
    if (enabled == false) return;
    _rectEnd.set(event.clientX, event.clientY);

    if (
      isMultiSelectionActive == true &&
      _userHasDraggedFarEnough == false &&
      _rectSize.length() > 10
    ) {
      _userHasDraggedFarEnough = true;
      addRectangle();
    }
    updateRectangle();

    if (getFeatureFlag("intuitiveMultiSelection") && _userHasDraggedFarEnough) {
      entitiesInRect(_entitiesInRect);
      Client.userEntity.selectEntities(
        [...new Set([..._entitiesInRect, ...dragStartSelectedEntities])],
        EntitySelectionMode.REPLACE,
        false
      );
    }
  }
  function onPointerUp(_event: PointerEvent) {
    isMultiSelectionActive = false;
    Client.userEntity.setTransformControlsVisibility(true);

    document.body.removeEventListener("pointerup", onPointerUp);
    document.body.removeEventListener("pointermove", onPointerMove);

    if (!_userHasDraggedFarEnough) return;
    removeRectangle();
    entitiesInRect(_entitiesInRect);
    runCommands(
      new SelectEntitiesCommand(
        [...new Set([..._entitiesInRect, ...dragStartSelectedEntities])].map(
          (e) => e.id
        ),
        EntitySelectionMode.REPLACE
      )
    );
  }

  function entitiesInRect(target: GameEntity[]) {
    target.length = 0;
    const camera = Client.userEntity.getCamera();
    if (!camera) {
      return;
    }
    if (getFeatureFlag("intuitiveMultiSelection")) {
      const entities = Client.getAllEntities();
      for (let i = 0; i < entities.length; i++) {
        if (
          entities[i] &&
          SELECTABLE_ENTITY_TYPES.has(entities[i].type as EntityType)
        ) {
          //selection happens if entity is inside of the frame even partially
          // (at least one edge at least partially is inside of the frame)
          const rootNode = entities[i].getRootNode();
          if (!rootNode) continue;

          let result = false;
          rootNode.traverse((obj) => {
            if (!result && (obj as Mesh).isMesh) {
              result =
                frustum.intersectsObject(obj) ||
                meshEdgeIntersectsRect(obj as Mesh, camera, _box2DClipSpace);
            }
          });
          if (result) target.push(entities[i]);
        }
      }
    } else {
      const objects = Client.scene.children;
      for (let i = 0; i < objects.length; i++) {
        const object = objects[i];
        const entity = (object as Object3DWithEntity).entity;
        if (entity && SELECTABLE_ENTITY_TYPES.has(entity.type as EntityType)) {
          // we could select the object when:
          // -1. all its points are inside the rect
          // -2. its position is inside the rect
          // -3. the center of its bounding box is inside the rect
          // 1 is too expensive, 2 not super intuitive, especially if objects are rotated, we therefore pick method 3.
          // _posOnScreen.copy(object.position).project(camera); // that's for method 2
          _objectBbox.setFromObject(object);
          _objectBbox.getCenter(_posOnScreen).project(camera);
          _posOnScreen2D.set(_posOnScreen.x, _posOnScreen.y);

          if (_box2DClipSpace.containsPoint(_posOnScreen2D)) {
            target.push(entity);
          }
        }
      }
    }
  }

  function projectPoint(
    point: Vector3,
    world: Matrix4,
    camera: PerspectiveCamera,
    target: Vector2
  ) {
    point.applyMatrix4(world);
    point.project(camera);
    target.set(point.x, point.y);
  }

  function meshEdgeIntersectsRect(
    mesh: Mesh,
    camera: PerspectiveCamera,
    rect: Box2
  ): boolean {
    const geometry = mesh.geometry;
    const position = geometry.attributes.position;
    if (!geometry || !position) return false;

    // Get edges from geometry index or assume triangles
    if (geometry.index) {
      const index = geometry.index.array;
      for (let i = 0; i < index.length; i += 3) {
        projectPoint(
          v3.fromBufferAttribute(position, index[i]),
          mesh.matrixWorld,
          camera,
          v2
        );
        if (rect.containsPoint(v2)) return true;
        projectPoint(
          v3.fromBufferAttribute(position, index[i + 1]),
          mesh.matrixWorld,
          camera,
          v21
        );
        if (rect.containsPoint(v21)) return true;
        projectPoint(
          v3.fromBufferAttribute(position, index[i + 2]),
          mesh.matrixWorld,
          camera,
          v22
        );
        if (rect.containsPoint(v22)) return true;

        if (lineIntersectsRect(v2, v21, rect)) return true;
        if (lineIntersectsRect(v21, v22, rect)) return true;
        if (lineIntersectsRect(v22, v2, rect)) return true;
      }
    } else {
      for (let i = 0; i < position.count; i += 3) {
        projectPoint(
          v3.fromBufferAttribute(position, i),
          mesh.matrixWorld,
          camera,
          v2
        );
        if (rect.containsPoint(v2)) return true;

        projectPoint(
          v3.fromBufferAttribute(position, i + 1),
          mesh.matrixWorld,
          camera,
          v21
        );
        if (rect.containsPoint(v21)) return true;
        projectPoint(
          v3.fromBufferAttribute(position, i + 2),
          mesh.matrixWorld,
          camera,
          v22
        );

        if (rect.containsPoint(v22)) return true;

        if (lineIntersectsRect(v2, v21, rect)) return true;
        if (lineIntersectsRect(v21, v22, rect)) return true;
        if (lineIntersectsRect(v22, v2, rect)) return true;
      }
    }

    return false;
  }

  // Helper: check if a line segment intersects a rectangle
  function lineIntersectsRect(a: Vector2, b: Vector2, rect: Box2): boolean {
    // Rectangle edges
    const min = rect.min,
      max = rect.max;

    // Check intersection with each rectangle edge
    if (segmentsIntersect(a.x, a.y, b.x, b.y, min.x, min.y, max.x, min.y))
      return true;
    if (segmentsIntersect(a.x, a.y, b.x, b.y, max.x, min.y, max.x, max.y))
      return true;
    if (segmentsIntersect(a.x, a.y, b.x, b.y, max.x, max.y, min.x, max.y))
      return true;
    if (segmentsIntersect(a.x, a.y, b.x, b.y, min.x, max.y, min.x, min.y))
      return true;

    return false;
  }

  // Helper: check if two line segments intersect
  function segmentsIntersect(
    p1X: number,
    p1Y: number,
    p2X: number,
    p2Y: number,
    q1X: number,
    q1Y: number,
    q2X: number,
    q2Y: number
  ): boolean {
    return (
      ccw(p1X, p1Y, q1X, q1Y, q2X, q2Y) !== ccw(p2X, p2Y, q1X, q1Y, q2X, q2Y) &&
      ccw(p1X, p1Y, p2X, p2Y, q1X, q1Y) !== ccw(p1X, p1Y, p2X, p2Y, q2X, q2Y)
    );
  }
  function ccw(
    aX: number,
    aY: number,
    bX: number,
    bY: number,
    cX: number,
    cY: number
  ) {
    return (cY - aY) * (bX - aX) > (bY - aY) * (cX - aX);
  }

  function updateRectangle() {
    _box2D.min.x = Math.min(_rectStart.x, _rectEnd.x);
    _box2D.min.y = Math.min(_rectStart.y, _rectEnd.y);
    _box2D.max.x = Math.max(_rectStart.x, _rectEnd.x);
    _box2D.max.y = Math.max(_rectStart.y, _rectEnd.y);
    _box2D.getSize(_rectSize);
    SELECTION_RECT_ELEMENT.style.left = _box2D.min.x + "px";
    SELECTION_RECT_ELEMENT.style.top = _box2D.min.y + "px";
    SELECTION_RECT_ELEMENT.style.width = _rectSize.x + "px";
    SELECTION_RECT_ELEMENT.style.height = _rectSize.y + "px";

    _box2DClipSpace.copy(_box2D);
    _box2DClipSpace.min.x = (_box2D.min.x / window.innerWidth) * 2 - 1;
    _box2DClipSpace.min.y = -(_box2D.max.y / window.innerHeight) * 2 + 1;
    _box2DClipSpace.max.x = (_box2D.max.x / window.innerWidth) * 2 - 1;
    _box2DClipSpace.max.y = -(_box2D.min.y / window.innerHeight) * 2 + 1;
    updateFrustumFromScreenRect();
  }

  function updateFrustumFromScreenRect() {
    const camera = Client.userEntity.getCamera() as PerspectiveCamera;
    // Get 4 corners in NDC
    const ndcCorners = [
      v3tl.set(_box2DClipSpace.min.x, _box2DClipSpace.max.y, 0.5), // top-left
      v3tr.set(_box2DClipSpace.max.x, _box2DClipSpace.max.y, 0.5), // top-right
      v3br.set(_box2DClipSpace.max.x, _box2DClipSpace.min.y, 0.5), // bottom-right
      v3bl.set(_box2DClipSpace.min.x, _box2DClipSpace.min.y, 0.5), // bottom-left
    ];
    // Unproject corners to world space at near and far planes
    const nearCorners = ndcCorners.map((ndc) =>
      ndc.clone().setZ(-1).unproject(camera)
    );

    const farCorners = ndcCorners.map((ndc) =>
      ndc.clone().setZ(1).unproject(camera)
    );

    const camPos = camera.position;

    planeLeft.setFromCoplanarPoints(camPos, nearCorners[0], nearCorners[3]);
    planeRight.setFromCoplanarPoints(camPos, nearCorners[2], nearCorners[1]);
    planeTop.setFromCoplanarPoints(camPos, nearCorners[1], nearCorners[0]);
    planeBottom.setFromCoplanarPoints(camPos, nearCorners[3], nearCorners[2]);
    planeNear.setFromCoplanarPoints(
      nearCorners[2],
      nearCorners[1],
      nearCorners[0]
    );
    planeFar.setFromCoplanarPoints(farCorners[0], farCorners[1], farCorners[2]);

    frustum.set(
      planeLeft,
      planeRight,
      planeTop,
      planeBottom,
      planeNear,
      planeFar
    );
  }

  function selectionRectCreate() {
    const element = document.createElement("div");
    element.style.position = "absolute";
    element.style.border = "4px solid green";
    element.style.pointerEvents = "none";
    element.style.opacity = "0.5";
    return element;
  }
  function enable() {
    enabled = true;
    // do not add rectangle here,
    // as this is done on pointerdown
    // addRectangle();
  }
  function disable() {
    enabled = false;
    removeRectangle();
  }
  function addRectangle() {
    if (SELECTION_RECT_ELEMENT.parentElement != document.body) {
      document.body.appendChild(SELECTION_RECT_ELEMENT);
    }
  }
  function removeRectangle() {
    if (SELECTION_RECT_ELEMENT.parentElement == document.body) {
      document.body.removeChild(SELECTION_RECT_ELEMENT);
    }
  }
  return {
    enable,
    disable,
  };
}
