// resolve-css-variables.ts
import fs from "node:fs";
import path from "node:path";

/**
 * Resolve CSS variable references in the CSS content.
 * This function will:
 * 1. Extract all CSS custom properties
 * 2. Build a map of variable names to their values
 * 3. Replace var() references with their resolved values
 *
 * @param {string} inputPath - Path to input CSS file
 * @param {string} outputPath - Path to output CSS file (can be same as input)
 */
export async function resolveCssVariables(
  inputPath: string,
  outputPath: string
): Promise<void> {
  assertExists(inputPath, "Input CSS file not found.");

  const css = fs.readFileSync(inputPath, "utf-8");
  const resolvedCss = resolveVariables(css);

  ensureDir(path.dirname(outputPath));
  fs.writeFileSync(outputPath, resolvedCss, "utf-8");
  console.debug(
    `Resolved CSS variables in ${path.relative(process.cwd(), outputPath)}`
  );
}

/**
 * Resolve CSS variable references in the CSS content.
 * This function will:
 * 1. Extract all CSS custom properties
 * 2. Build a map of variable names to their values
 * 3. Replace var() references with their resolved values
 */
function resolveVariables(css: string): string {
  // Extract all CSS custom properties into a map
  const variableMap = new Map<string, string>();

  // Match CSS custom properties: --name: value;
  const varRegex = /--([a-z0-9-]+):\s*([^;]+);/gi;
  let match;

  while ((match = varRegex.exec(css)) !== null) {
    const [, name, value] = match;
    variableMap.set(`--${name}`, value.trim());
  }

  // Function to resolve a single var() reference
  function resolveVarReference(varRef: string): string {
    const varName = varRef.match(/var\(([^)]+)\)/)?.[1];
    if (!varName) return varRef;

    const value = variableMap.get(varName);
    if (!value) return varRef;

    // If the value itself contains var() references, resolve them recursively
    if (value.includes("var(")) {
      return resolveVarReference(value);
    }

    return value;
  }

  // Replace all var() references with their resolved values
  let resolvedCss = css;

  // Find all var() references and resolve them
  const varRefRegex = /var\([^)]+\)/g;
  const varRefs = css.match(varRefRegex) || [];

  for (const varRef of varRefs) {
    const resolvedValue = resolveVarReference(varRef);
    resolvedCss = resolvedCss.replace(
      new RegExp(escapeRegex(varRef), "g"),
      resolvedValue
    );
  }

  return resolvedCss;
}

/**
 * Helper functions
 */

function assertExists(p: string, message: string): void {
  if (!fs.existsSync(p)) {
    console.error(message, p);
    process.exit(1);
  }
}

function ensureDir(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function escapeRegex(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}
