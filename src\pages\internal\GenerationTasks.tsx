import {
  collection,
  CollectionReference,
  limit,
  QueryDocumentSnapshot,
} from "firebase/firestore";
import { useEffect, useState, useMemo } from "react";
import { ExternalLink } from "lucide-react";
import { db, auth } from "@/config/firebase";
import {
  DbCollections,
  DbModelGenerationTask,
  ModelGenerationTaskStatus,
} from "@nilo/firebase-schema";
import { orderByT, queryT, useFirebaseQuery } from "@/hooks/firebaseHooks";
import { Typography } from "@/components/ui-nilo/Typography";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ModelPreview } from "@/components/ModelPreview";
import { FirestoreDocumentUrl } from "@/pages/internal/devUrls";

const LIMIT = 100;

const formatDate = (timestamp: DbModelGenerationTask["createdAt"]) => {
  if (!timestamp) return "N/A";
  return new Date(timestamp.toDate()).toLocaleString();
};

const getDuration = (
  start: DbModelGenerationTask["requestedAt"],
  end: DbModelGenerationTask["finishedAt"]
) => {
  if (!start) {
    return "N/A";
  }
  let endTime = end?.toDate().getTime();
  if (!endTime) {
    endTime = new Date().getTime();
  }
  const duration = endTime - start.toDate().getTime();
  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  return `${minutes}m ${seconds % 60}s${end ? "" : "+"}`;
};

const getStatusVariant = (status?: ModelGenerationTaskStatus) => {
  switch (status) {
    case "success":
      return "default";
    case "failed":
      return "destructive";
    case "running":
      return "secondary";
    case "queued":
      return "outline";
    case "cancelled":
      return "secondary";
    default:
      return "secondary";
  }
};

type TaskSummary = {
  [type: string]: {
    [status in ModelGenerationTaskStatus | "unknown"]?: number;
  };
};

const GenerationTask = ({
  task,
  currentUserId,
  copiedId,
  copyToClipboard,
}: {
  task: QueryDocumentSnapshot<DbModelGenerationTask>;
  currentUserId: string | null;
  copiedId: string | null;
  copyToClipboard: (text: string) => void;
}) => {
  const data = task.data();
  const providerTaskId = data.providerTaskId;
  const isCurrentUser = data.userId === currentUserId;

  return (
    <Card
      key={task.id}
      className={`bg-surface-primary border-accessories-divider ${
        isCurrentUser ? "ring-2 ring-blue-500" : ""
      }`}
    >
      <div id={task.id} />
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Typography.Title level={3} color="brand">
                {data.taskBody.type}
              </Typography.Title>
              <a href={`#${task.id}`} aria-label={`Link to task ${task.id}`}>
                <Typography.Body level={1} color="light">
                  #
                </Typography.Body>
              </a>
            </div>
            {isCurrentUser && (
              <Badge variant="secondary" className="text-xs">
                Your task
              </Badge>
            )}
          </div>
          <div className="flex gap-2">
            <Badge variant={getStatusVariant(data.status)} className="text-xl">
              {data.status || "unknown"}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Typography.Body level={1} color="light" weight="medium">
            Model Generation Task ID:{" "}
            <FirestoreDocumentUrl
              path={[DbCollections.modelGenerationTasks, task.id]}
            >
              {task.id}
            </FirestoreDocumentUrl>
          </Typography.Body>
          <Typography.Body level={1} color="light">
            User ID: {data.userId}{" "}
            <a
              href={`/user/${data.userId}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="w-4 h-4 inline-block" />
            </a>
          </Typography.Body>
          {data.provider && (
            <Typography.Body level={1} color="light">
              Provider: {data.provider.name} - {data.provider.version}
            </Typography.Body>
          )}
          {providerTaskId && (
            <Typography.Body level={1} color="light">
              Provider Task ID:{" "}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(providerTaskId)}
                className="h-auto p-0 text-left justify-start font-mono text-sm"
              >
                {providerTaskId}
                {copiedId === data.providerTaskId && (
                  <span className="text-green-500 ml-1">✓</span>
                )}
              </Button>
            </Typography.Body>
          )}
        </div>

        {data.taskBody.type === "text_to_model" &&
          data.taskBody.prompt !== undefined && (
            <div className="space-y-2">
              <Typography.Body level={2} color="light" weight="medium">
                Prompt:
              </Typography.Body>
              <pre className="bg-muted text-muted-foreground p-3 rounded-md text-sm font-mono whitespace-pre-wrap break-words border border-accessories-divider">
                {data.taskBody.prompt}
              </pre>
            </div>
          )}

        {data.taskBody.type === "image_to_model" &&
          data.taskBody.imageUrl !== undefined && (
            <div className="space-y-2">
              <img
                src={data.taskBody.imageUrl}
                alt="Input"
                className="max-w-xs max-h-xs w-auto h-auto object-contain rounded-md border border-accessories-divider"
              />
            </div>
          )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <Typography.Heading level={1} color="light">
              Task Body
            </Typography.Heading>
            <pre className="bg-muted text-muted-foreground p-3 rounded-md text-sm font-mono whitespace-pre-wrap break-words border border-accessories-divider">
              {JSON.stringify(data.taskBody, null, 2)}
            </pre>

            {/* Moderation Card */}
            {data.moderation && (
              <Card
                className={`border-2 ${
                  data.moderation.status === "passed"
                    ? "bg-green-900/30 border-green-700"
                    : data.moderation.status === "flagged"
                      ? "bg-red-900/30 border-red-700"
                      : data.moderation.status === "error"
                        ? "bg-orange-900/30 border-orange-700"
                        : data.moderation.status === "pending"
                          ? "bg-yellow-900/30 border-purple-700"
                          : "bg-slate-800/50 border-slate-600"
                }`}
              >
                <CardContent className="p-1">
                  <div className="text-center space-y-3">
                    <Typography.Heading
                      level={1}
                      color="light"
                      className="uppercase tracking-wide text-xl font-nilo-display"
                    >
                      Moderation: {data.moderation.status}
                    </Typography.Heading>
                    {(data.moderation.status === "flagged" ||
                      data.moderation.status === "error") &&
                      data.moderation.flagReason && (
                        <Typography.Body
                          level={1}
                          color="light"
                          className={
                            data.moderation.status === "flagged"
                              ? "text-red-300"
                              : "text-orange-300"
                          }
                        >
                          {data.moderation.flagReason}
                        </Typography.Body>
                      )}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          <div className="space-y-3">
            <Typography.Heading level={1} color="light">
              Timestamps
            </Typography.Heading>
            <div className="space-y-1 text-sm">
              <Typography.Body level={2} color="light">
                Created: {formatDate(data.createdAt)}
              </Typography.Body>
              <Typography.Body level={2} color="light">
                Requested: {formatDate(data.requestedAt)}
              </Typography.Body>
              <Typography.Body level={2} color="light">
                Finished: {formatDate(data.finishedAt)}
              </Typography.Body>
              <Typography.Body level={2} color="light">
                ETA: {formatDate(data.eta)}
              </Typography.Body>
            </div>

            <Typography.Heading level={1} color="light">
              Details
            </Typography.Heading>
            <div className="space-y-1 text-sm">
              <Typography.Body level={2} color="light">
                Duration: {getDuration(data.requestedAt, data.finishedAt)}
              </Typography.Body>
              <Typography.Body level={2} color="light">
                Progress: {data.progress || 0}%
              </Typography.Body>
              {data.cacheKey && (
                <Typography.Body level={2} color="light">
                  Cache Key: {data.cacheKey}
                </Typography.Body>
              )}
              {data.message && (
                <Typography.Body
                  level={2}
                  color="light"
                  className="text-red-400"
                >
                  Error: {data.message}
                </Typography.Body>
              )}
              {data.suggestion && (
                <Typography.Body
                  level={2}
                  color="light"
                  className="text-red-400"
                >
                  Suggestion: {data.suggestion}
                </Typography.Body>
              )}
            </div>
          </div>
        </div>

        {/* Model Preview for successful tasks with model URLs */}
        {data.status === "success" && data.modelUrl && (
          <div className="mt-6">
            <Typography.Heading level={1} color="light" className="mb-3">
              Model Preview
              {(data.moderation?.status === "flagged" ||
                data.moderation?.status === "error") && (
                <Badge
                  variant={
                    data.moderation.status === "flagged"
                      ? "destructive"
                      : "secondary"
                  }
                  className="ml-2"
                >
                  {data.moderation.status === "flagged"
                    ? "Moderation Flagged - Debug Only"
                    : "Moderation Error - Debug Only"}
                </Badge>
              )}
            </Typography.Heading>
            <ModelPreview
              modelUrl={data.modelUrl}
              modelName={`${data.taskBody.type}_${task.id}`}
              isAnimation={data.taskBody.type === "animate_rig"}
              autoPlay={false}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

const GenerationTasks = () => {
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(text);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setCurrentUserId(user?.uid ?? null);
    });
    return () => unsubscribe();
  }, []);

  const tasksCollection = collection(
    db,
    DbCollections.modelGenerationTasks
  ) as CollectionReference<DbModelGenerationTask, DbModelGenerationTask>;
  const q = queryT(
    tasksCollection,
    orderByT("createdAt", "desc"),
    limit(LIMIT)
  );
  const tasks = useFirebaseQuery(q);

  const calculateSummary = useMemo(() => {
    const summary: TaskSummary = {};
    const columnTotals: {
      [status in ModelGenerationTaskStatus]: number;
    } = {
      queued: 0,
      running: 0,
      success: 0,
      failed: 0,
      cancelled: 0,
      unknown: 0,
    };

    tasks.forEach((task) => {
      const data = task.data();
      const type = data.taskBody.type;
      const status = data.status || "unknown";

      if (!summary[type]) {
        summary[type] = {};
      }
      if (!summary[type][status]) {
        summary[type][status] = 0;
      }
      summary[type][status]! += 1;
      columnTotals[status] += 1;
    });

    return { summary, columnTotals };
  }, [tasks]);

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto">
      <div className="mx-auto space-y-12 pt-8 pb-24 max-w-7xl px-4">
        {/* Header */}
        <header className="text-center">
          <Typography.Title level={1} color="light" className="mb-4">
            Generation Tasks
          </Typography.Title>
          <Typography.Body level={1} color="light" weight="medium">
            Last {LIMIT} tasks
          </Typography.Body>
        </header>

        {/* Summary Table */}
        <Card className="bg-surface-primary border-accessories-divider">
          <CardHeader>
            <CardTitle>
              <Typography.Title level={3} color="light">
                Task Summary
              </Typography.Title>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-accessories-divider">
                    <th className="text-left py-3 px-4">
                      <Typography.Label level={1} color="light" weight="medium">
                        Task Type
                      </Typography.Label>
                    </th>
                    <th className="text-center py-3 px-4">
                      <Typography.Label level={1} color="light" weight="medium">
                        Queued
                      </Typography.Label>
                    </th>
                    <th className="text-center py-3 px-4">
                      <Typography.Label level={1} color="light" weight="medium">
                        Running
                      </Typography.Label>
                    </th>
                    <th className="text-center py-3 px-4">
                      <Typography.Label level={1} color="light" weight="medium">
                        Success
                      </Typography.Label>
                    </th>
                    <th className="text-center py-3 px-4">
                      <Typography.Label level={1} color="light" weight="medium">
                        Failed
                      </Typography.Label>
                    </th>
                    <th className="text-center py-3 px-4">
                      <Typography.Label level={1} color="light" weight="medium">
                        Cancelled
                      </Typography.Label>
                    </th>
                    <th className="text-center py-3 px-4">
                      <Typography.Label level={1} color="light" weight="medium">
                        Unknown
                      </Typography.Label>
                    </th>
                    <th className="text-center py-3 px-4">
                      <Typography.Label level={1} color="light" weight="bold">
                        Total
                      </Typography.Label>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(calculateSummary.summary).map(
                    ([type, statuses]) => {
                      const rowTotal = Object.values(statuses).reduce(
                        (sum, count) => sum + (count ?? 0),
                        0
                      );
                      return (
                        <tr
                          key={type}
                          className="border-b border-accessories-divider hover:bg-surface-secondary/50"
                        >
                          <td className="py-3 px-4">
                            <Typography.Body
                              level={1}
                              color="light"
                              weight="medium"
                            >
                              {type}
                            </Typography.Body>
                          </td>
                          <td className="text-center py-3 px-4">
                            <Typography.Body level={1} color="light">
                              {statuses.queued ?? 0}
                            </Typography.Body>
                          </td>
                          <td className="text-center py-3 px-4">
                            <Typography.Body level={1} color="light">
                              {statuses.running ?? 0}
                            </Typography.Body>
                          </td>
                          <td className="text-center py-3 px-4">
                            <Typography.Body level={1} color="light">
                              {statuses.success ?? 0}
                            </Typography.Body>
                          </td>
                          <td className="text-center py-3 px-4">
                            <Typography.Body level={1} color="light">
                              {statuses.failed ?? 0}
                            </Typography.Body>
                          </td>
                          <td className="text-center py-3 px-4">
                            <Typography.Body level={1} color="light">
                              {statuses.cancelled ?? 0}
                            </Typography.Body>
                          </td>
                          <td className="text-center py-3 px-4">
                            <Typography.Body level={1} color="light">
                              {statuses.unknown ?? 0}
                            </Typography.Body>
                          </td>
                          <td className="text-center py-3 px-4">
                            <Typography.Body
                              level={1}
                              color="light"
                              weight="bold"
                            >
                              {rowTotal}
                            </Typography.Body>
                          </td>
                        </tr>
                      );
                    }
                  )}
                  <tr className="border-b border-accessories-divider bg-surface-secondary/30">
                    <td className="py-3 px-4">
                      <Typography.Body level={1} color="light" weight="bold">
                        Total
                      </Typography.Body>
                    </td>
                    <td className="text-center py-3 px-4">
                      <Typography.Body level={1} color="light" weight="bold">
                        {calculateSummary.columnTotals.queued}
                      </Typography.Body>
                    </td>
                    <td className="text-center py-3 px-4">
                      <Typography.Body level={1} color="light" weight="bold">
                        {calculateSummary.columnTotals.running}
                      </Typography.Body>
                    </td>
                    <td className="text-center py-3 px-4">
                      <Typography.Body level={1} color="light" weight="bold">
                        {calculateSummary.columnTotals.success}
                      </Typography.Body>
                    </td>
                    <td className="text-center py-3 px-4">
                      <Typography.Body level={1} color="light" weight="bold">
                        {calculateSummary.columnTotals.failed}
                      </Typography.Body>
                    </td>
                    <td className="text-center py-3 px-4">
                      <Typography.Body level={1} color="light" weight="bold">
                        {calculateSummary.columnTotals.cancelled}
                      </Typography.Body>
                    </td>
                    <td className="text-center py-3 px-4">
                      <Typography.Body level={1} color="light" weight="bold">
                        {calculateSummary.columnTotals.unknown}
                      </Typography.Body>
                    </td>
                    <td className="text-center py-3 px-4">
                      <Typography.Body level={1} color="light" weight="bold">
                        {Object.values(calculateSummary.columnTotals).reduce(
                          (sum, count) => sum + count,
                          0
                        )}
                      </Typography.Body>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Task Cards */}
        <div className="space-y-4">
          {tasks.map((task) => (
            <GenerationTask
              key={task.id}
              task={task}
              currentUserId={currentUserId}
              copiedId={copiedId}
              copyToClipboard={copyToClipboard}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default GenerationTasks;
