// Prettier: printWidth 80
/* eslint-disable no-console */

import fs from "node:fs";
import path from "node:path";
import { config } from "./config.js";

interface TokenNode {
  value: string | number;
  type?: string;
}

interface PrimitiveTokens {
  color?: Record<string, Record<string, TokenNode>>;
  typography?: {
    font?: Record<string, TokenNode>;
    weight?: Record<string, TokenNode>;
    size?: Record<string, TokenNode>;
    "line-height"?: Record<string, TokenNode>;
  };
  spacing?: Record<string, TokenNode>;
}

interface SemanticTokens {
  bg?: Record<string, TokenNode>;
  icon?: Record<string, TokenNode>;
  text?: Record<string, TokenNode>;
  border?: Record<string, TokenNode>;
  "corner-radius"?: Record<string, TokenNode>;
  padding?: Record<string, TokenNode>;
  spacing?: Record<string, TokenNode>;
  height?: Record<string, TokenNode>;
}

interface ComponentTokens {
  typography?: Record<string, Record<string, Record<string, TokenNode>>>;
}

interface FigmaTokens {
  "primitives-nilo-uikit"?: PrimitiveTokens;
  "semantic-nilo-uikit"?: SemanticTokens;
  "component-nilo-uikit"?: ComponentTokens;
}

interface TokenSections {
  primitive: PrimitiveTokens;
  semantic: SemanticTokens;
  component: ComponentTokens;
}

/**
 * Convert Figma variables to Tailwind CSS
 * @param {string} inputPath - Path to figma-plugin-export.json
 * @param {string} outputPath - Path to output CSS file
 * @param {string} prefix - Custom prefix for variables
 */
export async function figmaVariablesToTailwind(
  inputPath: string,
  outputPath: string,
  prefix: string
): Promise<void> {
  assertExists(inputPath, "Input tokens file not found.");

  const raw = fs.readFileSync(inputPath, "utf-8");
  const tokens: FigmaTokens = JSON.parse(raw);

  // The input sample shows three top-level groups:
  // - primitives-nilo-uikit
  // - component-nilo-uikit
  // - semantic-nilo-uikit
  // We will map them into @theme blocks separated by section.
  // Also, we will generate CSS custom properties prefixed with `--${prefix}`.
  const sections: TokenSections = {
    primitive: tokens["primitives-nilo-uikit"] || {},
    semantic: tokens["semantic-nilo-uikit"] || {},
    component: tokens["component-nilo-uikit"] || {},
  };

  const cssBlocks: string[] = [];

  // 1) Primitive tokens to CSS variables
  cssBlocks.push(
    emitThemeBlock(
      `/** Primitive (Design Primitives) */`,
      buildPrimitiveVars(sections.primitive, prefix)
    )
  );

  // 2) Semantic tokens to CSS variables
  cssBlocks.push(
    emitThemeBlock(
      `/** Semantic (Design Semantics) */`,
      buildSemanticVars(sections.semantic, prefix)
    )
  );

  // 3) Component tokens to CSS variables
  cssBlocks.push(
    emitThemeBlock(
      `/** Component (Component-level tokens) */`,
      buildComponentVars(sections.component, prefix)
    )
  );

  // 4) Special case: Generate CSS classes for font sizes
  cssBlocks.push(emitPrimitiveFontSizeClasses(sections.primitive, prefix));
  cssBlocks.push(emitComponentFontSizeClasses(sections.component, prefix));

  const outCss = [
    "/**",
    " * ⚠️  WARNING: This file is auto-generated from Figma tokens",
    " *",
    " * DO NOT EDIT THIS FILE MANUALLY!",
    " * Any manual changes will be overwritten the next time the tokens are imported.",
    " *",
    " * To modify these values:",
    " * 1. Update the Figma design tokens",
    " * 2. Re-run the import script: pnpm write-files",
    " *",
    ` * Source: ${path.relative(process.cwd(), inputPath)}`,
    ` * Generator: ${config.scriptPaths.figmaToTailwind}`,
    " */",
    "",
    cssBlocks.join("\n\n"),
    "",
  ].join("\n");

  ensureDir(path.dirname(outputPath));
  fs.writeFileSync(outputPath, outCss, "utf-8");
  console.debug(
    `Wrote Tailwind @theme CSS to ${path.relative(process.cwd(), outputPath)}`
  );
}

/**
 * Build primitive variables into CSS custom properties.
 * Maps:
 *  - color.*.* -> --color-nilo-<group>-<step>
 *  - typography.font.* -> --font-nilo-<name>
 *  - typography.weight.* -> --font-weight-nilo-<name>
 *  - typography.size.* -> --spacing-nilo-size-<step>
 *  - typography.line-height.* -> --leading-nilo-<name>
 *  - spacing.* -> --spacing-nilo-<step>
 */
function buildPrimitiveVars(
  primitive: PrimitiveTokens,
  prefix: string
): string[] {
  const out: string[] = [];

  // Colors: nested groups with numbered scales + specials
  if (primitive.color) {
    for (const [group, groupObj] of Object.entries(primitive.color)) {
      if (typeof groupObj !== "object") continue;
      for (const [key, node] of Object.entries(groupObj)) {
        if (!node || typeof node !== "object") continue;
        if (!("value" in node)) continue;
        // Use Tailwind v4 convention: --color-<prefix><group>-<step>
        const varName = `--color-${prefix}${group}-${key}`;
        out.push(`${varName}: ${normalizeHexColor(node.value)};`);
      }
    }
  }

  // Typography: font families, weights, sizes, line-heights
  if (primitive.typography) {
    const typo = primitive.typography;

    if (typo.font) {
      for (const [k, node] of Object.entries(typo.font)) {
        if (!node || typeof node !== "object") continue;
        // Add generic fallback fonts
        const fontValue = appendFallbackFonts(node.value);
        // Use Tailwind v4 convention: --font-<prefix><name>
        const varName = `--font-${prefix}${k}`;
        out.push(`${varName}: ${fontValue};`);
      }
    }

    // Skip font-weight primitives as requested
    // if (typo.weight) {
    //   for (const [k, node] of Object.entries(typo.weight)) {
    //     if (!node || typeof node !== "object") continue;
    //     // Use Tailwind v4 convention: --font-weight-<prefix><name>
    //     const varName = `--font-weight-${prefix}${k}`;
    //     out.push(`${varName}: ${node.value};`);
    //   }
    // }

    if (typo.size) {
      for (const [k, node] of Object.entries(typo.size)) {
        if (!node || typeof node !== "object") continue;
        // Use Tailwind v4 convention: --spacing-<prefix>size-<step>
        const varName = `--spacing-${prefix}size-${k}`;
        out.push(`${varName}: ${appendPxIfNumber(node.value)};`);
      }
    }

    if (typo["line-height"]) {
      for (const [k, node] of Object.entries(typo["line-height"])) {
        if (!node || typeof node !== "object") continue;
        // Use Tailwind v4 convention: --leading-<prefix><name>
        const varName = `--leading-${prefix}${k}`;
        out.push(`${varName}: ${node.value};`);
      }
    }
  }

  // Spacing scale
  if (primitive.spacing) {
    for (const [k, node] of Object.entries(primitive.spacing)) {
      if (!node || typeof node !== "object") continue;
      // Use Tailwind v4 convention: --spacing-<prefix><step>
      const varName = `--spacing-${prefix}${k}`;
      out.push(`${varName}: ${appendPxIfNumber(node.value)};`);
    }
  }

  return out;
}

/**
 * Build semantic variables into CSS custom properties.
 * Maps:
 *  - bg.* -> --color-nilo-fill-<name> (renamed to avoid confusion with bg- utility)
 *  - icon.* -> --color-nilo-icon-<name>
 *  - text.* -> --color-nilo-text-<name>
 *  - border.* -> --color-nilo-border-<name>
 *  - corner-radius.* -> --radius-nilo-<name>
 *  - padding.* -> --spacing-nilo-padding-<name>
 *  - spacing.* -> --spacing-nilo-<name>
 *  - height.* -> --spacing-nilo-size-<name>
 */
function buildSemanticVars(semantic: SemanticTokens, prefix: string): string[] {
  const out: string[] = [];
  const simpleColorSets = ["bg", "icon", "text", "border"];

  for (const key of simpleColorSets) {
    if (!semantic[key as keyof SemanticTokens]) continue;
    const section = semantic[key as keyof SemanticTokens] as Record<
      string,
      TokenNode
    >;
    for (const [name, node] of Object.entries(section)) {
      if (!node || typeof node !== "object") continue;
      // Use Tailwind v4 convention for colors
      const cleanedName = removeDuplicateWords([key, name]).join("-");

      // Apply semantic variable replacements to avoid confusion
      let finalName = cleanedName;
      for (const [pattern, replacement] of Object.entries(
        config.semanticVariableReplacements
      )) {
        finalName = finalName.replace(new RegExp(pattern, "g"), replacement);
      }

      const varName = `--color-${prefix}${finalName}`;
      const resolvedValue = resolveSemanticRef(node.value, prefix);
      out.push(`${varName}: ${normalizeHexColor(resolvedValue)};`);
    }
  }

  // corner-radius -> radius
  if (semantic["corner-radius"]) {
    for (const [name, node] of Object.entries(semantic["corner-radius"])) {
      if (!node || typeof node !== "object") continue;
      const varName = `--radius-${prefix}${name}`;
      const resolvedValue = appendPxIfNumber(
        resolveSemanticRef(node.value, prefix)
      );
      out.push(`${varName}: ${resolvedValue};`);
    }
  }

  // padding.*
  if (semantic.padding) {
    for (const [name, node] of Object.entries(semantic.padding)) {
      if (!node || typeof node !== "object") continue;
      // Remove duplicate "padding" if it appears in the name
      const cleanedName = name.replace(/^padding-/, "");
      const varName = `--spacing-${prefix}padding-${cleanedName}`;
      const resolvedValue = appendPxIfNumber(
        resolveSemanticRef(node.value, prefix)
      );
      out.push(`${varName}: ${resolvedValue};`);
    }
  }

  // spacing.*
  if (semantic.spacing) {
    for (const [name, node] of Object.entries(semantic.spacing)) {
      if (!node || typeof node !== "object") continue;
      const cleanedName = name.replace(/^spacing-/, "");
      const varName = `--spacing-${prefix}${cleanedName}`;
      const resolvedValue = appendPxIfNumber(
        resolveSemanticRef(node.value, prefix)
      );
      out.push(`${varName}: ${resolvedValue};`);
    }
  }

  // height.*
  if (semantic.height) {
    for (const [name, node] of Object.entries(semantic.height)) {
      if (!node || typeof node !== "object") continue;
      const cleanedName = name.replace(/^height-/, "");
      const varName = `--spacing-${prefix}size-${cleanedName}`;
      const resolvedValue = appendPxIfNumber(
        resolveSemanticRef(node.value, prefix)
      );
      out.push(`${varName}: ${resolvedValue};`);
    }
  }

  return out;
}

/**
 * Build component variables into CSS custom properties.
 * The component block is largely typography sets with nested groups.
 * We flatten keys like:
 *  component.typography.Title["Title-1"].size -> --spacing-nilo-size-component-title-1
 */
function buildComponentVars(
  component: ComponentTokens,
  prefix: string
): string[] {
  const out: string[] = [];
  if (!component.typography) return out;

  const group = component.typography;

  for (const [familyGroup, familyObj] of Object.entries(group)) {
    if (!familyObj || typeof familyObj !== "object") continue;

    for (const [variant, variantObj] of Object.entries(familyObj)) {
      if (!variantObj || typeof variantObj !== "object") continue;

      for (const [prop, node] of Object.entries(variantObj)) {
        if (!node || typeof node !== "object") continue;

        // Remove duplicate words and normalize
        const cleanedFamilyGroup = familyGroup.toLowerCase();
        const cleanedVariant = normalizeTokenKey(variant);

        // Remove duplicates like "body-body-1" -> "body-1", "label-label-1" -> "label-1"
        const variantParts = cleanedVariant.split("-");
        const finalVariant =
          cleanedFamilyGroup === variantParts[0]
            ? variantParts.slice(1).join("-") || variantParts[0]
            : cleanedVariant;

        // Map to appropriate Tailwind namespace
        let namespace = "";
        const variantName =
          prop === "size"
            ? `${prefix}size-component-${cleanedFamilyGroup}-${finalVariant}`
            : `${prefix}component-${cleanedFamilyGroup}-${finalVariant}`;

        if (prop === "size") {
          namespace = "spacing";
        } else if (prop === "line-height") {
          namespace = "leading";
        } else if (prop === "letter-spacing") {
          namespace = "tracking";
        } else if (prop === "family") {
          namespace = "font";
        } else if (prop === "weight") {
          namespace = "font-weight";
        } else {
          namespace = prop;
        }

        const varName = `--${namespace}-${variantName}`;
        const value = resolveSemanticRef(node.value, prefix);
        const finalValue =
          prop === "family"
            ? appendFallbackFonts(value)
            : prop === "size" ||
                prop === "line-height" ||
                prop === "letter-spacing"
              ? appendPxIfNumber(value)
              : value;

        out.push(`${varName}: ${finalValue};`);
      }
    }
  }

  return out;
}

/**
 * Produce an @theme block with given declarations.
 */
function emitThemeBlock(comment: string, declarations: string[]): string {
  const lines = Array.from(new Set(declarations)).sort(); // stable and dedup
  return [comment, "@theme {", ...lines.map((l) => `  ${l}`), "}"].join("\n");
}

/**
 * Generate CSS classes for font sizes using the primitive typography.size tokens.
 * This creates classes like .text-nilo-size-1 { font-size: 12px; }
 */
function emitPrimitiveFontSizeClasses(
  primitive: PrimitiveTokens,
  prefix: string
): string {
  if (!primitive.typography || !primitive.typography.size) {
    return "";
  }

  const classes: string[] = [];
  const comment = `/** Font Size Classes (Auto-generated from primitive typography.size tokens) */`;

  // Sort by size value to ensure consistent ordering
  const sizeEntries = Object.entries(primitive.typography.size)
    .filter(([_, node]) => node && typeof node === "object" && "value" in node)
    .sort(([_, a], [__, b]) => {
      const aVal = parseFloat(a.value as string) || 0;
      const bVal = parseFloat(b.value as string) || 0;
      return aVal - bVal;
    });

  for (const [key, node] of sizeEntries) {
    const sizeValue = appendPxIfNumber(node.value);
    const className = `.${prefix}size-${key}`;
    const cssRule = `${className} {\n  font-size: ${sizeValue};\n}`;
    classes.push(cssRule);
  }

  if (classes.length === 0) {
    return "";
  }

  return [comment, "", ...classes, ""].join("\n");
}

/**
 * Generate CSS classes for component font sizes using the component typography tokens.
 * This creates classes like .text-nilo-size-component-title-1 { font-size: 72px; }
 */
function emitComponentFontSizeClasses(
  component: ComponentTokens,
  prefix: string
): string {
  if (!component.typography) {
    return "";
  }

  const classes: string[] = [];
  const comment = `/** Component Font Size Classes (Auto-generated from component typography.size tokens) */`;

  // Process each typography family (Title, Header, Body, Label)
  for (const [familyGroup, familyObj] of Object.entries(component.typography)) {
    if (!familyObj || typeof familyObj !== "object") continue;

    for (const [variant, variantObj] of Object.entries(familyObj)) {
      if (!variantObj || typeof variantObj !== "object") continue;
      if (!variantObj.size || !variantObj.size.value) continue;

      // Get the size value and resolve any references
      const sizeValue = resolveSemanticRef(variantObj.size.value, prefix);

      // Clean up the variant name (e.g., "Title-1" -> "title-1")
      const cleanedFamilyGroup = familyGroup.toLowerCase();
      const cleanedVariant = normalizeTokenKey(variant);

      // Remove duplicates like "title-title-1" -> "title-1"
      const variantParts = cleanedVariant.split("-");
      const finalVariant =
        cleanedFamilyGroup === variantParts[0]
          ? variantParts.slice(1).join("-") || variantParts[0]
          : cleanedVariant;

      const className = `.${prefix}size-component-${cleanedFamilyGroup}-${finalVariant}`;
      const cssRule = `${className} {\n  font-size: ${sizeValue};\n}`;
      classes.push(cssRule);
    }
  }

  if (classes.length === 0) {
    return "";
  }

  return [comment, "", ...classes, ""].join("\n");
}

/**
 * Helpers
 */

function assertExists(p: string, message: string): void {
  if (!fs.existsSync(p)) {
    console.error(message, p);
    process.exit(1);
  }
}

function ensureDir(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function appendPxIfNumber(
  v: string | number | null | undefined
): string | number | null | undefined {
  if (v == null) return v;
  const s = String(v).trim();
  if (s === "") return s;
  if (/^-?\d+(\.\d+)?$/.test(s)) {
    // a bare number -> px
    return `${s}px`;
  }
  return s;
}

/**
 * Strip "ff" suffix from 8-character hex colors when they represent 100% opacity
 * Example: #0b0034ff -> #0b0034
 */
function normalizeHexColor(
  v: string | number | null | undefined
): string | number | null | undefined {
  if (v == null) return v;

  const s = String(v).trim().toLowerCase();
  if (s === "") return s;

  // Check if it's an 8-character hex color ending with "ff"
  if (/^#[0-9a-fA-F]{8}$/.test(s) && s.toLowerCase().endsWith("ff")) {
    return s.slice(0, -2); // Remove last 2 characters
  }

  return s;
}

/**
 * Resolve a {typography.size.11} style reference into the prefixed CSS var.
 * This is specifically for semantic tokens that reference primitive tokens.
 * Example:
 *   "{color.primary.500}" -> var(--color-nilo-primary-500)
 *   "{typography.size.11}" -> var(--spacing-nilo-size-11)
 *   "{spacing.4}" -> var(--spacing-nilo-4)
 */
function resolveSemanticRef(
  value: string | number,
  prefix: string
): string | number {
  if (typeof value !== "string") return value;

  return value.replace(/\{([^}]+)\}/g, (_, pathStr) => {
    const parts = pathStr.split(".");

    // Map the reference to the correct Tailwind namespace
    if (parts[0] === "color" && parts.length >= 3) {
      // {color.primary.500} -> var(--color-nilo-primary-500)
      return `var(--color-${prefix}${parts[1]}-${parts[2]})`;
    } else if (parts[0] === "typography") {
      if (parts[1] === "size" && parts[2]) {
        // {typography.size.11} -> var(--spacing-nilo-size-11)
        return `var(--spacing-${prefix}size-${parts[2]})`;
      } else if (parts[1] === "font" && parts[2]) {
        // {typography.font.primary} -> var(--font-nilo-primary)
        return `var(--font-${prefix}${parts[2]})`;
      } else if (parts[1] === "weight" && parts[2]) {
        // {typography.weight.bold} -> var(--font-weight-nilo-bold)
        return `var(--font-weight-${prefix}${parts[2]})`;
      } else if (parts[1] === "line-height" && parts[2]) {
        // {typography.line-height.normal} -> var(--leading-nilo-normal)
        return `var(--leading-${prefix}${parts[2]})`;
      } else if (parts[1] === "letter-spacing" && parts[2]) {
        // {typography.letter-spacing.normal} -> var(--tracking-nilo-normal)
        return `var(--tracking-${prefix}${parts[2]})`;
      }
    } else if (parts[0] === "spacing" && parts[1]) {
      // {spacing.4} -> var(--spacing-nilo-4)
      return `var(--spacing-${prefix}${parts[1]})`;
    }

    // Fallback for any unmatched patterns
    return `var(--${prefix}${parts.join("-")})`;
  });
}

/**
 * Normalize certain token keys like "Title-1" -> "title-1"
 */
function normalizeTokenKey(k: string): string {
  return String(k).trim().toLowerCase();
}

/**
 * Append generic fallback fonts to a font family value
 */
function appendFallbackFonts(fontValue: string | number): string | number {
  if (!fontValue || typeof fontValue !== "string") return fontValue;

  // Check if it already has fallbacks (contains comma)
  if (fontValue.includes(",")) return fontValue;

  // Don't add fallbacks if it's a reference (contains {path} or var())
  if (fontValue.includes("{") || fontValue.includes("var(")) return fontValue;

  // Determine appropriate fallbacks based on font name
  const font = fontValue.trim();
  const lowerFont = font.toLowerCase();

  if (lowerFont.includes("display") || lowerFont.includes("fliper")) {
    // Display/decorative fonts
    return `${font}, ui-serif, serif`;
  } else if (lowerFont.includes("mono") || lowerFont.includes("code")) {
    // Monospace fonts
    return `${font}, ui-monospace, monospace`;
  } else {
    // Default to sans-serif for most fonts (Inter, etc.)
    return `${font}, ui-sans-serif, system-ui, sans-serif`;
  }
}

/**
 * Remove duplicate words when they appear consecutively
 * e.g., ["body", "body-1"] -> ["body", "1"]
 *      ["label", "label-2"] -> ["label", "2"]
 */
function removeDuplicateWords(parts: string[]): string[] {
  const result: string[] = [];

  for (let i = 0; i < parts.length; i++) {
    const current = parts[i];
    const prev = i > 0 ? parts[i - 1] : null;

    if (prev && current.startsWith(prev + "-")) {
      // If current starts with previous word + dash, remove the duplicate
      result.push(current.substring(prev.length + 1));
    } else if (prev === current) {
      // Skip exact duplicates
      continue;
    } else {
      result.push(current);
    }
  }

  return result;
}
