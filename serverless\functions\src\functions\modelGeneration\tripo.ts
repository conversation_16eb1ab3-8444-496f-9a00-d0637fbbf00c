/* eslint-disable no-redeclare */
import WebSocket from "ws";
import { z } from "zod";
import {
  DbModelGenerationTask,
  ModelGenerationProvider,
  ModelGenerationTaskStatus,
  TripoTaskBody,
  TripoTaskStatus,
  TripoVersions,
} from "@nilo/firebase-schema";
import { runWithRetry, sleepMs } from "../../utils";
import { Limit, reserveUserAccess } from "../../limit";
import { Logger } from "../../logger";
import { ModelGenerationProviderTaskOptions } from "./config";
import { ModelGenerationContext, ModelGenerationListener } from ".";

const apiKey = "tsk_1IkHnjVcRTTb2L7dT9hb_i9T-PdYMCLU_OeKNyLmho1";
const websocketTimeoutMs = 60 * 1000;
const websocketMessageTimeoutMs = 30 * 1000;
// using 8 minutes to have time to save results (9 minutes is an absolute maximum for function processing)
const functionDeadlineMs = 8 * 60 * 1000;
const rateLimit: Limit = {
  calls: 25,
  durationSeconds: 60,
};

/**
 * Represents the output of a task, containing model and image URLs
 *
 * From: https://platform.tripo3d.ai/docs/task#response-1
 */
const TaskBase = z.object({
  /**
   * URL to download the base model
   * @note Expires after one minute by default
   */
  base_model: z.string().optional(),

  /**
   * URL for a preview image of the model
   * @note Expires after one minute by default
   */
  rendered_image: z.string().optional(),
});
const TaskModel = z.object({
  /**
   * URL to download the model
   * @note Expires after one minute by default
   */
  model: z.string(),
});

const TaskPBRModel = z.object({
  /**
   * URL to download the PBR (Physically Based Rendering) model
   * @note Expires after one minute by default
   */
  pbr_model: z.string(),
});

const TaskOutput = z.intersection(TaskBase, z.union([TaskModel, TaskPBRModel]));
type TaskOutput = z.infer<typeof TaskOutput>;

/**
 * Represents the complete response for a task
 *
 * From: https://platform.tripo3d.ai/docs/task#response-1
 */
const TaskResponse = z.object({
  /**
   * Unique identifier for the task
   * @remarks Should match the identifier used in the original request
   */
  task_id: z.string(),

  /**
   * Specifies the type of task
   */
  type: z.string(),

  /**
   * Current status of the task
   */
  status: TripoTaskStatus,

  /**
   * Input data for the task
   * @remarks Structure varies depending on task type
   */
  input: z.record(z.unknown()),

  /**
   * Results of the task
   */
  output: TaskOutput.optional(),

  /**
   * Progress of the task
   * @remarks
   * - 0 when queued
   * - Indicates progress when running
   * - 100 when successful
   * - Not meaningful for other statuses
   */
  progress: z.number(),

  /**
   * Timestamp of task creation
   */
  create_time: z.number(),
});
type TaskResponse = z.infer<typeof TaskResponse>;

/**
 * Represents an event from the WebSocket connection
 *
 * From: https://platform.tripo3d.ai/docs/task#response
 */
const TaskEvent = z.object({
  event: z.enum(["update", "finalized"]),
  data: TaskResponse,
});
type TaskEvent = z.infer<typeof TaskEvent>;

function extractTripoResponse<T>(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  response: any
):
  | { success: true; data: T }
  | { success: false; code: number; message: string; suggestion: string } {
  if ("code" in response) {
    if (response.code === 0 && "data" in response) {
      return { success: true, data: response.data as T };
    } else if ("message" in response && "suggestion" in response) {
      return {
        success: false,
        code: response.code,
        message: response.message,
        suggestion: response.suggestion,
      };
    }
  }
  return {
    success: false,
    code: -1,
    message: "Invalid response",
    suggestion: "Programming error? Outdated API?",
  };
}

export function convertStatus(
  status: TripoTaskStatus
): ModelGenerationTaskStatus {
  if (status === "banned") {
    return "failed";
  }
  return status;
}

function getTripoTaskBody(
  task: DbModelGenerationTask,
  providerOptions: ModelGenerationProviderTaskOptions,
  originalTask?: DbModelGenerationTask
): TripoTaskBody {
  const taskBody = task.taskBody;
  const provider = task.provider as ModelGenerationProvider;
  switch (taskBody.type) {
    case "text_to_model":
      return {
        type: taskBody.type,
        model_version: provider.version as TripoVersions,
        prompt: taskBody.prompt,
        negativePrompt: taskBody.negativePrompt,
        ...providerOptions,
      };
    case "animate_rig": {
      const originalTripoTaskId = originalTask?.providerTaskId;
      if (!originalTripoTaskId) {
        throw new Error("Original task ID not found");
      }
      return {
        type: taskBody.type,
        original_model_task_id: originalTripoTaskId,
        out_format: taskBody.outFormat,
        ...providerOptions,
      };
    }
    case "image_to_model": {
      return {
        type: taskBody.type,
        model_version: provider.version as TripoVersions,
        file: {
          url: taskBody.imageUrl,
          type: taskBody.imageType as string,
        },
        ...providerOptions,
      };
    }
    default:
      throw new Error("Unknown type in task body");
  }
}

export async function processTripoRequest(
  {
    task,
    providerOptions,
    originalTask,
    logger,
    functionStartedAtMs,
  }: ModelGenerationContext,
  listener: ModelGenerationListener
): Promise<void> {
  const taskBody = getTripoTaskBody(task, providerOptions, originalTask);
  // rate limit requests to tripo
  const success = await reserveUserAccess(task.userId, "tripo", rateLimit);
  if (!success) {
    logger.error("❌ Tripo rate limit exceeded", { userId: task.userId });
    await listener.onFinished({
      status: "failed",
      message: "Rate limit exceeded",
      suggestion: "Please try again later",
    });
    return;
  }

  // create tripo task
  let tripoTaskId: string;
  try {
    await replaceInputFileUrlWithUploadedFileToken(taskBody, logger);
    const creationResponse = await runWithRetry(() =>
      createTripoTask(taskBody, logger)
    );
    logger.debug("🚀 Tripo task created", { creationResponse });
    if (!creationResponse.success) {
      logger.warn("❌ Task creation failed", { creationResponse });
      await listener.onFinished({
        status: "failed",
        message: creationResponse.message,
        suggestion: creationResponse.suggestion,
        providerCode: creationResponse.code,
      });
      return;
    }
    tripoTaskId = creationResponse.data.task_id;
  } catch (err) {
    logger.error("❌ Task creation failed", { error: err });
    await listener.onFinished({
      status: "failed",
      message: String(err),
      suggestion: "Please try again later",
    });
    return;
  }
  await listener.onRequested({ providerTaskId: tripoTaskId });

  // watch tripo task
  const onTaskUpdated = async ({ status, progress }: TaskResponse) => {
    await listener.onProgress({
      status: convertStatus(status),
      progress,
    });
  };
  const onTaskFinished = async (taskResponse: TaskResponse) => {
    const updatePromise = listener.onFinished({ status: "success" });

    let uploadPromise = Promise.resolve(true);
    const tripoModelUrl =
      taskResponse.output?.pbr_model ?? taskResponse.output?.model;
    if (tripoModelUrl !== undefined) {
      uploadPromise = listener.onModelReady(tripoModelUrl);
    }
    const [updateResult, uploadResult] = await Promise.all([
      updatePromise,
      uploadPromise,
    ]);
    return updateResult && uploadResult;
  };

  let lastError: unknown;
  let watchingFinished = false;
  try {
    const success = await Promise.race([
      runWithRetry(() =>
        watchTripoTask(tripoTaskId, logger, {
          onTaskUpdated,
          onTaskFinished,
        })
      ),
      new Promise<boolean>((resolve) =>
        setTimeout(
          () => {
            if (!watchingFinished) {
              logger.error("❌ WebSocket timeout did not resolve");
            }
            resolve(false);
          },
          // 3x timeout + 15s buffer (for exponential backoff)
          websocketTimeoutMs * 3 + 15000
        )
      ),
    ]);
    logger.debug("🚀 Finished tripo request", { success });
    return;
  } catch (err) {
    logger.warn("❌ Watching task failed", { error: err });
    lastError = err;
  } finally {
    watchingFinished = true;
  }

  logger.info(
    `⏳ Falling back to polling, time left: ${functionStartedAtMs + functionDeadlineMs - performance.now()}ms`
  );
  while (performance.now() < functionStartedAtMs + functionDeadlineMs) {
    try {
      const taskResponse = await pollTripoTask(tripoTaskId, logger);
      logger.debug("🚀 Polled task", { taskResponse });
      if (
        taskResponse.status !== "running" &&
        taskResponse.status !== "queued"
      ) {
        const success = await onTaskFinished(taskResponse);
        logger.debug("🚀 Finished tripo request (polling)", { success });
        return;
      } else {
        try {
          await onTaskUpdated(taskResponse);
        } catch (err) {
          logger.error("❌ onTaskUpdated failed", { error: err });
        }
      }
    } catch (err) {
      logger.warn("❌ Polling failed", { error: err });
      lastError = err;
    }
    await sleepMs(3000);
  }

  logger.error("❌ Watching and polling failed", { error: lastError });
  await listener.onFinished({
    status: "failed",
    message: String(lastError),
    suggestion: "Please try again later",
  });
}

/**
 * Watch a Tripo task via WebSocket and handle the task events
 * @param taskId The Tripo task ID to watch
 * @param requestId The request ID for logging
 * @param options Callbacks for handling task events, errors and timeouts
 * @returns A promise that resolves to true if the task completes successfully
 */
async function watchTripoTask<T>(
  tripoTaskId: string,
  logger: Logger,
  options: {
    onTaskUpdated: (taskResponse: TaskResponse) => Promise<unknown>;
    onTaskFinished: (taskResponse: TaskResponse) => Promise<T>;
    timeoutMs?: number;
    messageTimeoutMs?: number;
  }
): Promise<T> {
  const timeoutMs = options.timeoutMs ?? websocketTimeoutMs;
  const messageTimeoutMs =
    options.messageTimeoutMs ?? websocketMessageTimeoutMs;

  // Create WebSocket connection
  const ws = new WebSocket(
    `wss://api.tripo3d.ai/v2/openapi/task/watch/${tripoTaskId}`,
    {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    }
  );

  // Return a promise that resolves when the task is complete
  return new Promise((resolve, reject) => {
    let messageTimeout: ReturnType<typeof setTimeout>;
    const resetMessageTimeout = () => {
      clearTimeout(messageTimeout);
      messageTimeout = setTimeout(() => {
        if (
          ws.readyState === WebSocket.OPEN ||
          ws.readyState === WebSocket.CONNECTING
        ) {
          logger.warn("❌ Message timeout");
          ws.terminate();
          reject(new Error("Message timeout"));
        }
      }, messageTimeoutMs);
    };
    resetMessageTimeout();

    // Handle incoming messages
    ws.on("message", async (message) => {
      logger.debug("📡 Received message");
      resetMessageTimeout();
      try {
        // Parse the message
        const rawEvent = JSON.parse(message.toString());
        logger.debug("📡 Raw event", { rawEvent });
        const taskEvent = TaskEvent.parse(rawEvent);
        logger.debug("📡 Parsed message", { taskEvent });

        // Check if the task is complete
        const { status } = taskEvent.data;
        if (status !== "running" && status !== "queued") {
          // Task is finalized
          ws.close();
          const result = await options.onTaskFinished(taskEvent.data);
          // Resolve with the result of onTaskFinished if provided, or true if status is "success"
          resolve(result);
        } else {
          try {
            await options.onTaskUpdated(taskEvent.data);
          } catch (err) {
            logger.error("❌ onTaskUpdated failed", { error: err });
          } finally {
            logger.debug("🔄 onTaskUpdated done", { taskEvent });
          }
        }
      } catch (err) {
        ws.close();
        logger.warn("❌ Watching task failed", { error: err });
        reject(err);
      }
    });

    // Handle WebSocket errors
    ws.on("error", async (err) => {
      logger.warn("❌ WebSocket error", { error: err });
      ws.terminate();
      reject(err);
    });

    ws.on("close", () => {
      logger.debug("❌ WebSocket closed");
    });
    ws.on("unexpected-response", (request, response) => {
      logger.warn("❌ Unexpected response", { request, response });
    });
    ws.on("upgrade", () => {
      logger.debug("🔄 WebSocket upgraded");
    });
    ws.on("open", () => {
      logger.debug("🔄 WebSocket opened");
    });
    ws.on("ping", () => {
      logger.debug("🔄 WebSocket ping");
    });
    ws.on("pong", () => {
      logger.debug("🔄 WebSocket pong");
    });

    // Handle timeouts
    setTimeout(async () => {
      logger.debug("🔄 WebSocket timeout processing started", {
        wsReadyState: ws.readyState,
      });
      if (
        ws.readyState === WebSocket.OPEN ||
        ws.readyState === WebSocket.CONNECTING
      ) {
        logger.warn("❌ WebSocket timeout");
        ws.terminate();
        reject(new Error("WebSocket timeout"));
      }
    }, timeoutMs);
  });
}

async function pollTripoTask(
  tripoTaskId: string,
  logger: Logger
): Promise<TaskResponse> {
  const response = await fetch(
    `https://api.tripo3d.ai/v2/openapi/task/${tripoTaskId}`,
    {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    }
  );
  if (!response.ok) {
    logger.error("❌ HTTP error", { response });
    throw new Error(
      `HTTP error! status: ${response.status}, info: ${response.statusText}`
    );
  }
  const taskResponseResult = extractTripoResponse<TaskResponse>(
    await response.json()
  );
  if (!taskResponseResult.success) {
    logger.error("❌ Task response error", { taskResponseResult });
    throw new Error(
      `API error! code: ${taskResponseResult.code}, message: ${taskResponseResult.message}`
    );
  }
  return TaskResponse.parse(taskResponseResult.data);
}

async function replaceInputFileUrlWithUploadedFileToken(
  taskBody: TripoTaskBody,
  logger: Logger
): Promise<void> {
  if (taskBody.type !== "image_to_model") {
    return;
  }
  const inputFile = taskBody.file;
  const inputUrl = inputFile && "url" in inputFile ? inputFile.url : undefined;

  if (inputUrl !== undefined) {
    logger.warn("🚀 Uploading image", { inputUrl });
    const inputImage = await fetch(inputUrl);
    const inputImageBlob = await inputImage.blob();
    const imageToken = await runWithRetry(() =>
      uploadImage(inputImageBlob, logger)
    );
    logger.debug("🚀 Uploaded image", { imageToken });
    taskBody.file = {
      type: inputFile.type,
      file_token: imageToken,
    };
    logger.debug("🚀 Updated task", { taskBody });
  }
}

async function uploadImage(image: Blob, logger: Logger): Promise<string> {
  const formData = new FormData();
  formData.append("file", image);

  const response = await fetch("https://api.tripo3d.ai/v2/openapi/upload", {
    method: "POST",
    headers: {
      origin: "*",
      Authorization: `Bearer ${apiKey}`,
    },
    body: formData,
  });
  if (!response.ok) {
    throw new Error(
      `HTTP error! status: ${response.status}, info: ${response.statusText}`
    );
  }
  const responseBody = await response.json();
  if (!responseBody) {
    throw new Error("Invalid response body");
  }
  logger.debug("🚀 Uploaded image", { responseBody });
  return responseBody.data.image_token;
}

async function createTripoTask(taskBody: TripoTaskBody, logger: Logger) {
  const response = await fetch("https://api.tripo3d.ai/v2/openapi/task", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${apiKey}`,
    },
    body: JSON.stringify(taskBody),
  });
  logger.info("🚀 Requested tripo", { response });
  if (!response.ok && response.status >= 500) {
    const responseBody = await response
      .text()
      .catch(() => "(failed to read error body)");
    logger.error("❌ HTTP error", { response, responseBody });
    throw new Error(
      `HTTP error! status: ${response.status}, info: ${response.statusText}`
    );
  }
  if (response.status === 403) {
    logger.error("❌ Unauthorized");
    // check balance - just in case
    checkBalance().then((balance) => {
      logger.info(`💰 Balance ${JSON.stringify(balance)}`);
    }, logger.error);
  }
  return extractTripoResponse<{
    task_id: string;
  }>(await response.json());
}

async function checkBalance() {
  const response = await fetch(
    "https://api.tripo3d.ai/v2/openapi/user/balance",
    {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    }
  );
  if (!response.ok) {
    throw new Error(
      `HTTP error! status: ${response.status}, info: ${response.statusText}`
    );
  }
  const res = await response.json();
  if (!res) {
    throw new Error("Invalid response body");
  }
  const balanceResponse = extractTripoResponse<{
    balance: number;
    frozen: number;
  }>(res);
  if (!balanceResponse.success) {
    throw new Error(
      `API error! code: ${balanceResponse.code}, message: ${balanceResponse.message}`
    );
  }
  return balanceResponse.data;
}
