import { <PERSON>b<PERSON><PERSON><PERSON><PERSON>, DbWhitelistedEmails } from "@nilo/firebase-schema";
import { HttpsError } from "firebase-functions/v2/https";
import { getAuth } from "firebase-admin/auth";
import { defineSecret } from "firebase-functions/params";
import { Logger } from "./logger";
import {
  beforeUserCreated,
  AuthBlockingEvent,
  isInternalEmail,
  beforeUserSignedIn,
} from "./functions/utils";
import { collection } from "./typedFirebase";
import { isEmulator } from "./utils";

const AIRTABLE_API_KEY = defineSecret("AIRTABLE_API_KEY");
const AIRTABLE_BASE_ID = defineSecret("AIRTABLE_BASE_ID");
const AIRTABLE_TABLE_ID = defineSecret("AIRTABLE_TABLE_ID");

/**
 * Triggered before a user is created in Firebase Auth.
 * This is used to check if the user is whitelisted.
 */
export const beforeUserAccountCreated = beforeUserCreated(
  {
    secrets: ["AIRTABLE_API_KEY", "AIRTABLE_BASE_ID", "AIRTABLE_TABLE_ID"],
  },
  async (event: AuthBlockingEvent) => {
    const user = event.data;

    if (!user) {
      throw new HttpsError(
        "invalid-argument",
        "User data is not available in the event"
      );
    }

    const logger = Logger.create({ userId: user.uid });

    logger.info("User account creation initiated", {
      userId: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      providerData: user.providerData?.map((p) => p.providerId) || [],
    });

    // if emulator, skip whitelist check
    if (isEmulator) {
      logger.info("Emulator detected, skipping whitelist check", {
        userId: user.uid,
        email: user.email,
      });
      return;
    }

    if (isInternalEmail(user.email)) {
      logger.info("User is internal", {
        userId: user.uid,
        email: user.email,
      });
      return;
    }

    if (!user.email) {
      logger.warn("No email provided for whitelist check", {
        userId: user.uid,
      });
      throw new HttpsError(
        "invalid-argument",
        "Email is required for account creation"
      );
    }

    let isWhitelisted = false;

    const airtableBaseId =
      AIRTABLE_BASE_ID.value() ?? process.env.AIRTABLE_BASE_ID;
    const airtableTableId =
      AIRTABLE_TABLE_ID.value() ?? process.env.AIRTABLE_TABLE_ID;
    const airtableApiKey =
      AIRTABLE_API_KEY.value() ?? process.env.AIRTABLE_API_KEY;

    const encodedEmail = encodeURIComponent(user.email.trim().toLowerCase());
    const cohort = encodeURIComponent("1 – September"); // TODO: Make this dynamic

    const url = `https://api.airtable.com/v0/${airtableBaseId}/${airtableTableId}?filterByFormula=AND({Email}="${encodedEmail}",{Cohort}="${cohort}")`;

    logger.info("Checking user in AirTable", {
      userId: user.uid,
      email: user.email,
      url,
    });

    isWhitelisted = await checkUserInWhitelistedEmailsAirTable(
      user.email,
      url,
      airtableApiKey,
      logger
    );

    if (isWhitelisted) {
      logger.info("User whitelist check passed via AirTable", {
        userId: user.uid,
        email: user.email,
        source: "airtable",
      });

      logger.info("User account creation approved", {
        userId: user.uid,
      });
      return;
    }

    logger.info(
      `Checking user in ${DbCollections.whitelistedEmails} collection`,
      {
        userId: user.uid,
        email: user.email,
      }
    );

    isWhitelisted = await checkUserInWhitelistedEmailsCollection(
      user.email,
      logger
    );

    if (!isWhitelisted) {
      logger.warn(
        `User not whitelisted in ${DbCollections.whitelistedEmails} collection, blocking account creation`,
        {
          userId: user.uid,
          email: user.email,
        }
      );
      throw new HttpsError(
        "permission-denied",
        "Your email is not whitelisted for account creation.",
        { isWhitelisted }
      );
    }

    logger.info("User whitelist check passed", {
      userId: user.uid,
      email: user.email,
      source: "whitelistedEmails",
    });

    logger.info("User account creation approved", {
      userId: user.uid,
    });
  }
);

async function checkUserInWhitelistedEmailsAirTable(
  email: string,
  url: string,
  apiKey: string,
  logger: Logger
): Promise<boolean> {
  try {
    if (!apiKey) {
      logger.error("AirTable API key is missing", {
        email,
      });
      return false;
    }

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
    });

    if (!response.ok) {
      logger.error(
        `AirTable API request failed with status ${response.status}`,
        {
          email,
          status: response.status,
          statusText: response.statusText,
        }
      );
      return false;
    }

    const data = (await response.json()) as {
      records: { fields: unknown }[];
    };
    return data.records.length > 0;
  } catch (error) {
    logger.error(`Error checking AirTable API ${url}`, {
      email,
      error: error instanceof Error ? error.message : String(error),
    });
    return false;
  }
}

/**
 * Check if user email exists in Firestore whitelistedEmails collection
 */
async function checkUserInWhitelistedEmailsCollection(
  email: string,
  logger: Logger
): Promise<boolean> {
  try {
    const docRef = collection<DbWhitelistedEmails>(
      DbCollections.whitelistedEmails
    ).doc(email.toLowerCase());
    const doc = await docRef.get();

    if (!doc.exists) {
      logger.info(
        `Email not found in ${DbCollections.whitelistedEmails} collection`,
        {
          email,
        }
      );
      return false;
    }

    const data = doc.data();
    const isWhitelisted = data?.allow === true;

    logger.info(
      `${DbCollections.whitelistedEmails} collection check completed`,
      {
        email,
        isWhitelisted,
        allowValue: data?.allow,
      }
    );

    return isWhitelisted;
  } catch (error) {
    logger.error(
      `Error checking ${DbCollections.whitelistedEmails} collection`,
      {
        email,
        error: error instanceof Error ? error.message : String(error),
      }
    );
    return false;
  }
}

export const beforeSignedIn = beforeUserSignedIn(
  {},
  async (event: AuthBlockingEvent) => {
    const user = event.data;
    if (!user) {
      throw new HttpsError(
        "invalid-argument",
        "User data is not available in the event"
      );
    }
    const logger = Logger.create({ userId: user.uid });
    const auth = getAuth();

    if (isEmulator) {
      // if on emulator then if email or displayName contains "admin" -> mark as admin
      const admin =
        [user.email, user.displayName].some((data) =>
          data?.toLowerCase().includes("admin")
        ) || isInternalEmail(user.email);
      logger.info("Sign in on emulator detected", {
        userId: user.uid,
        email: user.email,
        admin,
      });
      if (admin) {
        await auth.setCustomUserClaims(user.uid, { admin });
      }
      return;
    }

    if (isInternalEmail(user.email)) {
      logger.info("Internal user signed in", {
        userId: user.uid,
        email: user.email,
      });
      await auth.setCustomUserClaims(user.uid, { admin: true });
      return;
    }

    logger.info("Regular user signed in", {
      userId: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      providerData: user.providerData?.map((p) => p.providerId) || [],
    });
  }
);
