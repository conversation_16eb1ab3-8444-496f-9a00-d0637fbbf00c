import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

type ButtonVariantKey =
  | "default"
  | "solid"
  | "primary"
  | "secondary"
  | "tertiary"
  | "outline"
  | "ghost"
  | "destructive"
  | "danger"
  | "link"
  | "navigation"
  | "hud"
  | "label";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium font-nilo-primary transition-all disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-nilo-primary-800 focus-visible:ring-offset-2 focus-visible:ring-offset-nilo-neutral-400 cursor-pointer",
  {
    variants: {
      variant: {
        default:
          "bg-nilo-fill-primary text-nilo-text-primary hover:bg-nilo-fill-primary-hover active:bg-nilo-fill-primary-pressed [&_svg]:text-nilo-icon-primary disabled:bg-nilo-fill-primary disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        solid:
          "bg-nilo-fill-primary text-nilo-text-primary hover:bg-nilo-fill-primary-hover active:bg-nilo-fill-primary-pressed [&_svg]:text-nilo-icon-primary disabled:bg-nilo-fill-primary disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        primary:
          "bg-nilo-fill-primary text-nilo-text-primary hover:bg-nilo-fill-primary-hover active:bg-nilo-fill-primary-pressed [&_svg]:text-nilo-icon-primary disabled:bg-nilo-fill-primary disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        secondary:
          "bg-nilo-fill-secondary text-nilo-text-secondary hover:bg-nilo-fill-secondary-hover active:bg-nilo-fill-secondary-pressed [&_svg]:text-nilo-icon-secondary disabled:bg-nilo-fill-secondary disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        tertiary:
          "ring-2 ring-inset ring-nilo-border-secondary bg-transparent text-nilo-text-secondary hover:bg-nilo-fill-tertiary-hover active:bg-nilo-fill-tertiary-pressed [&_svg]:text-nilo-icon-secondary disabled:ring-nilo-border-disabled disabled:bg-transparent disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        outline:
          "ring-2 ring-inset ring-nilo-border-secondary bg-transparent text-nilo-text-secondary hover:bg-nilo-fill-tertiary-hover active:bg-nilo-fill-tertiary-pressed [&_svg]:text-nilo-icon-secondary disabled:ring-nilo-border-disabled disabled:bg-transparent disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        ghost:
          "bg-transparent text-nilo-text-secondary ring-2 ring-inset ring-transparent hover:ring-nilo-border-secondary active:ring-nilo-border-primary [&_svg]:text-nilo-icon-secondary disabled:ring-transparent disabled:bg-transparent disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        destructive:
          "bg-nilo-fill-error text-nilo-text-secondary hover:bg-nilo-fill-error-hover active:bg-nilo-fill-error-pressed [&_svg]:text-nilo-icon-secondary disabled:bg-nilo-fill-error disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        danger:
          "bg-nilo-fill-error text-nilo-text-secondary hover:bg-nilo-fill-error-hover active:bg-nilo-fill-error-pressed [&_svg]:text-nilo-icon-secondary disabled:bg-nilo-fill-error disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled",
        link: "text-nilo-text-quaternary underline-offset-4 hover:underline hover:text-nilo-text-quaternary-hover disabled:text-nilo-text-disabled disabled:no-underline",
        navigation:
          "bg-transparent text-nilo-text-secondary hover:bg-nilo-fill-secondary active:bg-nilo-fill-secondary-pressed [&_svg]:text-nilo-icon-secondary disabled:bg-nilo-fill-secondary disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled focus-visible:ring-2 focus-visible:ring-nilo-primary-800 focus-visible:ring-offset-0",
        hud: "bg-gray-800/25 text-nilo-text-secondary hover:bg-nilo-fill-secondary-hover active:bg-nilo-fill-secondary-pressed [&_svg]:text-nilo-icon-secondary disabled:bg-nilo-fill-secondary disabled:text-nilo-text-disabled disabled:[&_svg]:text-nilo-icon-disabled focus-visible:ring-2 focus-visible:ring-nilo-primary-800 focus-visible:ring-offset-2 focus-visible:ring-offset-nilo-neutral-400 focus-visible:bg-gray-800/75",
        label:
          "bg-transparent text-nilo-text-secondary text-base font-bold font-nilo-primary leading-tight p-0 h-5 justify-center self-stretch",
      } satisfies Record<ButtonVariantKey, string>,
      size: {
        default: "h-10 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 px-3 py-2 has-[>svg]:px-2.5",
        lg: "h-11 px-8 py-2 has-[>svg]:px-4",
        icon: "size-10 p-3",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

type ButtonProps = React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  };

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";

    return (
      <Comp
        ref={ref}
        data-slot="button"
        className={cn(buttonVariants({ variant, size, className }))}
        {...props}
      />
    );
  }
);

Button.displayName = "Button";

export { Button, buttonVariants, type ButtonVariantKey };
