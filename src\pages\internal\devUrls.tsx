import React, { useEffect, useState } from "react";
import { Settings } from "lucide-react";
import { useEmulator } from "@/config/firebase";
import { isolatedEnvironment } from "@/config/isolatedEnv";
import { DbCollections } from "@nilo/firebase-schema";
import { Typography } from "@/components/ui-nilo/Typography";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const googleUserIndexStorageKey = "googleUserIndexStorage";

function useGoogleUserIndex(): [number, (googleUserIndex: number) => void] {
  const [googleUserIndex, setGoogleUserIndex] = useState<number>(1);
  useEffect(() => {
    const googleUserIndex = localStorage.getItem(googleUserIndexStorageKey);
    setGoogleUserIndex(googleUserIndex ? parseInt(googleUserIndex) : 1);
  }, []);
  return [
    googleUserIndex,
    (googleUserIndex: number) => {
      setGoogleUserIndex(googleUserIndex);
      localStorage.setItem(
        googleUserIndexStorageKey,
        googleUserIndex.toString()
      );
    },
  ];
}

export function DevUrls() {
  const [googleUserIndex, setGoogleUserIndex] = useGoogleUserIndex();

  return (
    <div className="h-screen bg-black text-icon-primary overflow-y-auto scrollbar-thin-nilo">
      <div className="mx-auto space-y-12 pt-8 pb-24 max-w-6xl">
        {/* Header */}
        <header className="text-center">
          <Typography.Title level={1} color="light" className="mb-4">
            Development URLs
          </Typography.Title>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Google User Index Settings Card */}
          <Card className="bg-card border-border">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Google User Index
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="googleUserIndex">
                  <Typography.Label level={2} color="light" weight="medium">
                    User Index
                  </Typography.Label>
                </Label>
                <Input
                  id="googleUserIndex"
                  type="number"
                  value={googleUserIndex}
                  onChange={(e) =>
                    setGoogleUserIndex(parseInt(e.target.value) || 0)
                  }
                  className="w-32"
                  min="0"
                />
                <Typography.Body level={3} color="light" className="text-xs">
                  Used for Google Cloud console and Firebase console URLs to
                  switch between different Google accounts. Change this value if
                  debug urls lead to the wrong account.
                </Typography.Body>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export function FirestoreDocumentUrl({
  className,
  children,
  collection,
  id,
  path,
}: {
  className?: string;
  children: React.ReactNode;
} & (
  | {
      collection: DbCollections;
      id: string;
      path?: never;
    }
  | {
      path: string[];
      collection?: never;
      id?: never;
    }
)) {
  const [googleUserIndex] = useGoogleUserIndex();
  const pathString = (path ?? [collection, id]).join("/");
  let url;
  if (useEmulator) {
    url = `http://localhost:4000/firestore/default/data/${pathString}`;
  } else {
    const dbName = isolatedEnvironment.database
      ? isolatedEnvironment.database
      : "-default-";
    url = `https://console.firebase.google.com/u/${googleUserIndex}/project/nilo-technologies/firestore/databases/${dbName}/data/~2F${pathString.replace("/", "~2F")}`;
  }
  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className={className}
    >
      {children}
    </a>
  );
}

type LogsQuery =
  | [string, string | RegExp]
  | { type: "and"; queries: LogsQuery[] }
  | { type: "or"; queries: LogsQuery[] };

function logsQuery(key: string, value: string | RegExp): LogsQuery {
  return [key, value];
}
function logsQueryAnd(...queries: LogsQuery[]): LogsQuery {
  return { type: "and", queries };
}
function logsQueryOr(...queries: LogsQuery[]): LogsQuery {
  return { type: "or", queries };
}

function getLogsQueryString(query: LogsQuery, nested: boolean = false): string {
  if (Array.isArray(query)) {
    const [key, value] = query;
    if (value instanceof RegExp) {
      return `${key}=~"${value.source}"`;
    }
    return `${key}="${value}"`;
  }
  if (query.type === "and") {
    const result = query.queries
      .map((query) => getLogsQueryString(query, true))
      .join(nested ? " AND " : "\n");
    return nested ? `(${result})` : result;
  }
  if (query.type === "or") {
    const result = query.queries
      .map((query) => getLogsQueryString(query, true))
      .join(" OR ");
    return nested ? `(${result})` : result;
  }
  return "";
}

export function getGCPLogsUrl(googleUserIndex: number, query: LogsQuery) {
  const encodedQueryString = encodeURIComponent(getLogsQueryString(query))
    .replace(/\(/g, "%28")
    .replace(/\)/g, "%29");
  return `https://console.cloud.google.com/logs/query;query=${encodedQueryString};duration=PT1H?authuser=${googleUserIndex}&project=nilo-technologies`;
}

export function FirebaseFunctionLogsUrl({
  group,
  name,
  filterByLogValues,
  extraQueries,
  className,
  children,
}: {
  group?: string;
  name?: string;
  filterByLogValues?: Record<string, string | number | boolean>;
  extraQueries?: LogsQuery[];
  className?: string;
  children: React.ReactNode;
}) {
  const [googleUserIndex] = useGoogleUserIndex();
  const functionsGroup = group ?? isolatedEnvironment.functions;
  let url;
  if (useEmulator) {
    url = "http://localhost:4000/logs";
  } else {
    const query: LogsQuery[] = [
      logsQuery("resource.type", "cloud_run_revision"),
      logsQuery(
        "resource.labels.service_name",
        name
          ? `${functionsGroup}-${name.toLowerCase()}`
          : new RegExp(`^${functionsGroup}-.*`)
      ),
      ...Object.entries(filterByLogValues ?? {}).map(([key, value]) =>
        logsQuery(`jsonPayload.${key}`, value.toString())
      ),
      ...(extraQueries ?? []),
    ];
    url = getGCPLogsUrl(googleUserIndex, logsQueryAnd(...query));
  }
  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className={className}
    >
      {children}
    </a>
  );
}

export function FirebaseFunctionLogsForInternalDataJobs({
  jobId,
  className,
  children,
}: {
  jobId: string;
  className?: string;
  children: React.ReactNode;
}) {
  const functionsGroup = isolatedEnvironment.functions;
  return (
    <FirebaseFunctionLogsUrl
      className={className}
      extraQueries={[
        logsQueryOr(
          logsQueryAnd(
            logsQuery(
              "resource.labels.service_name",
              `${functionsGroup}-ondatajobcreated`
            ),
            logsQuery("jsonPayload.jobId", jobId)
          ),
          logsQueryAnd(
            logsQuery(
              "resource.labels.service_name",
              `${functionsGroup}-ondatasubjobcreated`
            ),
            logsQuery("jsonPayload.parentJobId", jobId)
          )
        ),
      ]}
    >
      {children}
    </FirebaseFunctionLogsUrl>
  );
}

export function FirebaseStorageUrl({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) {
  const [googleUserIndex] = useGoogleUserIndex();
  let url;
  if (useEmulator) {
    url = "http://localhost:4000/storage/nilo-technologies.firebasestorage.app";
  } else {
    url = `https://console.firebase.google.com/u/${googleUserIndex}/project/nilo-technologies/storage/${isolatedEnvironment.bucket}/files`;
  }
  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className={className}
    >
      {children}
    </a>
  );
}

export function GoogleCloudStorageUrl({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) {
  const [googleUserIndex] = useGoogleUserIndex();
  let url;
  if (useEmulator) {
    url = "http://localhost:4000/storage/nilo-technologies.firebasestorage.app";
  } else {
    url = `https://console.cloud.google.com/storage/browser/${isolatedEnvironment.bucket};tab=objects?authuser=${googleUserIndex}&project=nilo-technologies`;
  }
  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className={className}
    >
      {children}
    </a>
  );
}

export function GameServerContainerLogsUrl({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) {
  const [googleUserIndex] = useGoogleUserIndex();
  let url;
  if (useEmulator) {
    url = "#";
  } else {
    url = getGCPLogsUrl(
      googleUserIndex,
      logsQueryAnd(
        logsQuery("resource.type", "k8s_container"),
        logsQuery("resource.labels.container_name", "nilo-server"),
        logsQuery(
          'labels."k8s-pod/agones_dev/gameserver"',
          new RegExp(`^${isolatedEnvironment.fleet}-.*`)
        )
      )
    );
  }
  return (
    <a
      href={url}
      target="_blank"
      rel="noopener noreferrer"
      className={className}
    >
      {children}
    </a>
  );
}

export function LiveblocksRoomUrl({
  roomId,
  className,
  children,
}: {
  roomId: string;
  className?: string;
  children: React.ReactNode;
}) {
  return (
    <a
      href={`https://liveblocks.io/dashboard/vII9FNyFAePbhNw3xQqW9/projects/66bf359860be01ec9f9d5a6f/rooms/${roomId}`}
      target="_blank"
      rel="noopener noreferrer"
      className={className}
    >
      {children}
    </a>
  );
}
