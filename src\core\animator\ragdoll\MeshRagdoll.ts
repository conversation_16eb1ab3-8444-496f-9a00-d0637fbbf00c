import { Bone, Quatern<PERSON>, Skeleton, Skinned<PERSON>esh, Vector3 } from "three";
import { HumanBoneType, HumanoidSkeleton } from "../retarget/HumanoidSkeleton";
import { SkeletonBoneData } from "../utils/SkeletonBoneData";
import { AnimatorRetargeter } from "../retarget/AnimatorRetargeter";
import { SkeletonBoneShapes } from "./SkeletonBoneShapes";
import { getBoneOffset } from "./ragdoll-utils";
import { getSkeletonPoseForPhysics } from "./utils/getSkeletonPoseForPhysics";
import {
  RagdollBone,
  RagdollMotionType,
  BonePose,
} from "@nilo/physics-with-jolt/src/types/Ragdoll";
import { Client } from "@/core/client";
import { applyMatrixQuaternionToVector } from "@/core/util/TransformUtils";
import { addPhysicsToRagdoll } from "@/physics/PhysicsSimulation";
import { RagdollControlWrapper } from "packages/physics-with-jolt/src/webworker/wrappers/RagdollControlWrapper";

export enum RagdollState {
  Idle,
  Kinematic,
  Ragdoll,
}

export class MeshRagdoll {
  private _mesh: SkinnedMesh;
  private _skeleton: Skeleton;
  private _humanoidSkeleton: HumanoidSkeleton;
  private _boneData: SkeletonBoneData;
  private _skeletonBoneShapes: SkeletonBoneShapes;

  // might not be necessary
  private _ragdollBonesMap: Map<HumanBoneType, RagdollBone> = new Map();

  private _ragdollBones: RagdollBone[] = [];

  private _ragdollControl: RagdollControlWrapper | undefined = undefined;

  private _ragdollState: RagdollState = RagdollState.Kinematic;

  private _scale: Vector3 = new Vector3(); //skinnedMesh scale

  //Because physics is async, we add a flag to prevent updating the skeleton while changing states
  private _canUpdate: boolean = true;

  constructor(mesh: SkinnedMesh, retargeter: AnimatorRetargeter) {
    this._mesh = mesh;
    this._mesh.getWorldScale(this._scale);
    this._skeleton = mesh.skeleton;
    this._humanoidSkeleton = retargeter.retargetSkeleton as HumanoidSkeleton;
    this._boneData = retargeter.getSkeletonBoneData();
    this._skeletonBoneShapes = new SkeletonBoneShapes(retargeter, this._mesh);

    this.setupRagdollBones();

    if (Client.physics) {
      this.setupRagdoll(RagdollState.Kinematic);
    } else {
      Client.events.once("physics-started", () => {
        this.setupRagdoll(RagdollState.Kinematic);
      });
    }
  }

  private async setupRagdoll(ragdollState: RagdollState) {
    this._ragdollControl = await addPhysicsToRagdoll({
      bones: this._ragdollBones,
      motion_type:
        ragdollState == RagdollState.Kinematic
          ? RagdollMotionType.KINEMATIC
          : RagdollMotionType.DYNAMIC,
    });
  }

  update(deltaTime: number) {
    if (!this._canUpdate) return;
    switch (this._ragdollState) {
      case RagdollState.Idle:
        this._ragdollControl?.updatePoseWithOriginal(
          getSkeletonPoseForPhysics(
            this._ragdollBones,
            this._humanoidSkeleton,
            this._boneData
          ),
          deltaTime
        );
        break;
      case RagdollState.Kinematic:
        this._ragdollControl?.updatePoseWithOriginal(
          getSkeletonPoseForPhysics(
            this._ragdollBones,
            this._humanoidSkeleton,
            this._boneData
          ),
          deltaTime
        );
        break;
      case RagdollState.Ragdoll:
        this.updateSkeletonToPhysicsRagdoll(deltaTime);
        break;
    }
  }

  private async updateSkeletonToPhysicsRagdoll(_deltaTime: number) {
    const ragdollPose = await this._ragdollControl?.getPose();
    if (!ragdollPose) return;
    for (const bone of this._ragdollBones) {
      const three_bone = this._boneData.getBone(
        this._humanoidSkeleton.getBoneName(bone.bone_part_name)
      );
      if (!three_bone) continue;
      const ragdollBonePose = ragdollPose[bone.bone_part_name];
      if (ragdollBonePose) {
        this.setBoneTransform(three_bone, ragdollBonePose);
      }
    }
  }

  private _v1 = new Vector3();
  private _q1 = new Quaternion();

  private setBoneTransform(bone: Bone, jointTransform: BonePose) {
    // Set the global position and rotation from physics
    const worldPosition = this._v1.set(
      jointTransform.position.x,
      jointTransform.position.y,
      jointTransform.position.z
    );

    this._q1.set(
      jointTransform.rotation.x,
      jointTransform.rotation.y,
      jointTransform.rotation.z,
      jointTransform.rotation.w
    );

    if (bone.parent) {
      bone.parent.worldToLocal(worldPosition);
      bone.position.copy(worldPosition);

      // Convert global rotation to local rotation relative to parent
      const parentWorldQuat = new Quaternion();
      bone.parent.getWorldQuaternion(parentWorldQuat);
      bone.quaternion.copy(this._q1);
      bone.quaternion.premultiply(parentWorldQuat.invert());
    } else {
      //This should not happen, but just in case
      bone.position.copy(worldPosition);
      bone.quaternion.copy(this._q1);
    }

    // Update the bone's matrix
    bone.updateMatrix();
    bone.updateMatrixWorld(true);
  }

  private setupRagdollBones() {
    const rootBone = this.createRagdollBone("Pelvis", null, "Spine01", {
      twist_angle: 0,
      twist_axis: { x: 0, y: 0, z: 0 },
      normal_angle: 10,
      plane_angle: 0,
    });

    const spineBone = this.createRagdollBone("Spine01", rootBone, "Spine03", {
      twist_angle: 30,
      twist_axis: { x: 0, y: 1, z: 0 },
      normal_angle: 10,
      plane_angle: 10,
    });

    /* const spineBone2 = this.createRagdollBone("Spine02", spineBone, "Spine03", {
      twist_angle: 30,
      twist_axis: { x: 0, y: 1, z: 0 },
      normal_angle: 10,
      plane_angle: 10,
    }); */

    const spineBone3 = this.createRagdollBone("Spine03", spineBone, "Neck", {
      twist_angle: 45,
      twist_axis: { x: 0, y: 1, z: 0 },
      normal_angle: 10,
      plane_angle: 10,
    });

    const neckBone = this.createRagdollBone("Neck", spineBone3, "Head", {
      twist_angle: 45,
      twist_axis: { x: 0, y: 1, z: 0 },
      normal_angle: 10,
      plane_angle: 10,
    });

    this.createRagdollBone("Head", neckBone, null, {
      twist_angle: 45,
      twist_axis: { x: 0, y: 1, z: 0 },
      normal_angle: 45,
      plane_angle: 45,
    });

    //Arms
    const leftShoulderBone = this.createRagdollBone(
      "LeftShoulder",
      rootBone,
      "LeftArm",
      {
        twist_angle: 45,
        twist_axis: { x: -1, y: 0, z: 0 },
        normal_angle: 90,
        plane_angle: 90,
      }
    );

    const leftArmBone = this.createRagdollBone(
      "LeftArm",
      leftShoulderBone,
      "LeftForeArm",
      {
        twist_angle: 45,
        twist_axis: { x: -1, y: 0, z: 0 },
        normal_angle: 0,
        plane_angle: 90,
      }
    );

    const leftForeArmBone = this.createRagdollBone(
      "LeftForeArm",
      leftArmBone,
      "LeftHand",
      {
        twist_angle: 45,
        twist_axis: { x: -1, y: 0, z: 0 },
        normal_angle: 0,
        plane_angle: 90,
      }
    );

    this.createRagdollBone("LeftHand", leftForeArmBone, null, {
      twist_angle: 45,
      twist_axis: { x: -1, y: 0, z: 0 },
      normal_angle: 0,
      plane_angle: 90,
    });

    const rightShoulderBone = this.createRagdollBone(
      "RightShoulder",
      rootBone,
      "RightArm",
      {
        twist_angle: 45,
        twist_axis: { x: 1, y: 0, z: 0 },
        normal_angle: 90,
        plane_angle: 90,
      }
    );

    const rightArmBone = this.createRagdollBone(
      "RightArm",
      rightShoulderBone,
      "RightForeArm",
      {
        twist_angle: 45,
        twist_axis: { x: 1, y: 0, z: 0 },
        normal_angle: 0,
        plane_angle: 90,
      }
    );

    const rightForeArmBone = this.createRagdollBone(
      "RightForeArm",
      rightArmBone,
      "RightHand",
      {
        twist_angle: 45,
        twist_axis: { x: 1, y: 0, z: 0 },
        normal_angle: 0,
        plane_angle: 90,
      }
    );

    this.createRagdollBone("RightHand", rightForeArmBone, null, {
      twist_angle: 45,
      twist_axis: { x: 1, y: 0, z: 0 },
      normal_angle: 0,
      plane_angle: 90,
    });

    //Legs
    const leftUpLegBone = this.createRagdollBone(
      "LeftUpLeg",
      rootBone,
      "LeftLeg",
      {
        twist_angle: 45,
        twist_axis: { x: 0, y: -1, z: 0 },
        normal_angle: 45,
        plane_angle: 45,
      }
    );

    const leftLegBone = this.createRagdollBone(
      "LeftLeg",
      leftUpLegBone,
      "LeftFoot",
      {
        twist_angle: 45,
        twist_axis: { x: 0, y: -1, z: 0 },
        normal_angle: 0,
        plane_angle: 45,
      }
    );

    this.createRagdollBone("LeftFoot", leftLegBone, "LeftToe", {
      twist_angle: 45,
      twist_axis: { x: 0, y: -1, z: 0 },
      normal_angle: 0,
      plane_angle: 45,
    });

    const rightUpLegBone = this.createRagdollBone(
      "RightUpLeg",
      rootBone,
      "RightLeg",
      {
        twist_angle: 45,
        twist_axis: { x: 0, y: -1, z: 0 },
        normal_angle: 45,
        plane_angle: 45,
      }
    );

    const rightLegBone = this.createRagdollBone(
      "RightLeg",
      rightUpLegBone,
      "RightFoot",
      {
        twist_angle: 45,
        twist_axis: { x: 0, y: -1, z: 0 },
        normal_angle: 0,
        plane_angle: 45,
      }
    );

    this.createRagdollBone("RightFoot", rightLegBone, "RightToe", {
      twist_angle: 45,
      twist_axis: { x: 0, y: -1, z: 0 },
      normal_angle: 0,
      plane_angle: 45,
    });
  }

  private createRagdollBone(
    boneType: HumanBoneType,
    parent: RagdollBone | null,
    nextBoneType: HumanBoneType | null,
    constraintSettings: Partial<RagdollBone>
  ): RagdollBone {
    const centerOffset = new Vector3();
    const bone = this._boneData.getBone(
      this._humanoidSkeleton.getBoneName(boneType)
    );
    if (!bone) {
      throw new Error(`Bone ${boneType} not found in humanoid skeleton`);
    }

    const matrixWorld = bone.matrixWorld.clone();

    const boneShape = this._skeletonBoneShapes.getBoneShape(boneType);
    if (!boneShape) {
      throw new Error(`Bone shape for ${boneType} not found`);
    }

    if (nextBoneType && this._skeletonBoneShapes.hasBone(nextBoneType)) {
      centerOffset.copy(
        getBoneOffset(
          boneType,
          nextBoneType,
          this._humanoidSkeleton,
          this._boneData,
          false
        ).multiplyScalar(0.5)
      );
    }

    //Center offset is in world position right now, and so is boneShape.position
    centerOffset.add(boneShape.position);

    if (parent) {
      applyMatrixQuaternionToVector(
        bone.matrixWorld.clone().invert(),
        centerOffset
      );
    }

    const ragdollBone: RagdollBone = {
      bone_part_name: boneType,
      //three_bone: bone,
      localMatrix: matrixWorld.toArray(),
      center_offset: centerOffset.clone(),
      parent: parent,
      children: [],
      shape: boneShape,
      twist_angle: constraintSettings.twist_angle ?? 15,
      twist_axis: constraintSettings.twist_axis ?? { x: 0, y: 1, z: 0 },
      normal_angle: constraintSettings.normal_angle ?? 15,
      plane_angle: constraintSettings.plane_angle ?? 15,
    };
    this._ragdollBonesMap.set(boneType, ragdollBone);
    this._ragdollBones.push(ragdollBone);
    return ragdollBone;
  }

  public async setRagdollState(state: RagdollState) {
    if (this._ragdollState == state) return;
    if (this._ragdollControl == null) {
      await this.setupRagdoll(state);
    }

    this._canUpdate = false;
    switch (state) {
      case RagdollState.Idle:
        await this._ragdollControl?.removeFromPhysicsSystem();
        break;
      case RagdollState.Kinematic:
        this._boneData.resetBonesToBindPose();
        await this._ragdollControl?.setKinematic();
        break;
      case RagdollState.Ragdoll:
        // Update ragdoll bone positions to current animated positions before enabling physics
        await this._ragdollControl?.setDynamic();
        break;
    }
    this._ragdollState = state;
    this._canUpdate = true;
  }

  public dispose() {
    if (this._ragdollControl && Client.physics) {
      Client.physics.unregisterRagdollFromPhysics(this._ragdollControl);
    }
    this._ragdollControl = undefined;
    this._ragdollBones = [];
    this._ragdollBonesMap.clear();
    this._ragdollState = RagdollState.Idle;
  }

  public get ragdollControl() {
    return this._ragdollControl;
  }

  public get ragdollState() {
    return this._ragdollState;
  }

  public get mesh() {
    return this._mesh;
  }

  public get skeleton() {
    return this._skeleton;
  }
}
