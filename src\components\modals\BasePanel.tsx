import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { ReactNode } from "react";
import { ErrorBoundary } from "react-error-boundary";

import { Typography } from "../ui-nilo/Typography";
import { Card, CardContent, CardHeader } from "../ui/card";
import { cn } from "@/lib/utils";

interface BasePanelProps {
  title: string;
  onClose?: () => void;
  onPin?: () => void;
  isPinned?: boolean;
  children: ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
}

export function BasePanel({
  title,
  onClose,
  onPin,
  isPinned = false,
  children,
  className = "",
  headerClassName = "",
  contentClassName = "",
}: BasePanelProps) {
  return (
    <Card
      className={cn(
        "relative w-md max-h-full max-w-full lg:h-[1024px] flex flex-col gap-y-0 pointer-events-auto text-nilo-icon-secondary",
        className
      )}
      onPointerDown={(e) => e.stopPropagation()}
    >
      {onPin && (
        <button
          onClick={onPin}
          className="ring-offset-nilo-fill-tertiary focus:ring-nilo-border-primary data-[state=open]:bg-nilo-fill-secondary data-[state=open]:text-nilo-icon-tertiary absolute top-nilo-6 left-nilo-6 opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-6 [&_svg]:stroke-2 text-nilo-icon-tertiary cursor-pointer rounded-full"
        >
          {isPinned ? (
            <Pin className="h-6 w-6" />
          ) : (
            <PinOff className="h-6 w-6" />
          )}
          <span className="sr-only">{isPinned ? "Unpin" : "Pin"}</span>
        </button>
      )}
      {onClose && (
        <button
          onClick={onClose}
          className="ring-offset-nilo-fill-tertiary focus:ring-nilo-border-primary data-[state=open]:bg-nilo-fill-secondary data-[state=open]:text-nilo-icon-tertiary absolute top-nilo-6 right-nilo-6 opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-6 [&_svg]:stroke-2 text-nilo-icon-tertiary cursor-pointer rounded-full"
        >
          <XIcon />
          <span className="sr-only">Close</span>
        </button>
      )}
      <CardHeader
        className={cn(
          headerClassName,
          "flex flex-row items-center justify-between space-y-0 pb-3"
        )}
      >
        <Typography.Heading
          level={1}
          className="w-full text-center text-xl text-nilo-text-secondary tracking-nilo-component-title-1 font-['Fliper']"
        >
          {title}
        </Typography.Heading>
      </CardHeader>
      <CardContent className={cn(contentClassName, "space-y-4 overflow-auto")}>
        <ErrorBoundary
          fallback={
            <div className="flex flex-col items-center justify-center p-8 text-center space-y-4">
              <div className="text-nilo-icon-tertiary">
                <XIcon className="h-12 w-12 mx-auto" />
              </div>
              <Typography.Heading
                level={3}
                className="text-nilo-text-secondary"
              >
                Something went wrong
              </Typography.Heading>
              <Typography.Body
                level={2}
                className="text-nilo-text-tertiary max-w-sm"
              >
                This panel encountered an error and couldn&apos;t render
                properly. Try refreshing or closing and reopening the panel.
              </Typography.Body>
            </div>
          }
        >
          {children}
        </ErrorBoundary>
      </CardContent>
    </Card>
  );
}
