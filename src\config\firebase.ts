// src/config/firebase.ts
import { initializeApp } from "firebase/app";
import {
  connectAuthEmulator,
  getAuth,
  GoogleAuthProvider,
  GithubAuthProvider,
} from "firebase/auth";
import {
  connectFirestoreEmulator,
  // eslint-disable-next-line no-restricted-imports -- the only allowed usage of initializeFirestore
  initializeFirestore,
} from "firebase/firestore";
// eslint-disable-next-line no-restricted-imports -- the only allowed usage of getStorage
import { connectStorageEmulator, getStorage } from "firebase/storage";
import {
  connectFunctionsEmulator,
  // eslint-disable-next-line no-restricted-imports -- the only allowed usage of getFunctions
  getFunctions,
  httpsCallable,
  HttpsCallableResult,
} from "firebase/functions";
import { isolatedEnvironment } from "./isolatedEnv";

export const useEmulator = process.env.USE_FIREBASE_EMULATOR === "true";

const firebaseConfig = {
  apiKey: "AIzaSyAX0EXyD_SxwdhihGmasB3i_IhmC3vathY",
  authDomain: "auth.nilo.io",
  projectId: "nilo-technologies",
  storageBucket: isolatedEnvironment.bucket,
  messagingSenderId: "************",
  appId: "1:************:web:5ab536595d1235adc63045",
};

const app = initializeApp(firebaseConfig);

const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();
googleProvider.setCustomParameters({
  prompt: "select_account",
});

const githubProvider = new GithubAuthProvider();

const storage = getStorage(app);

const db = initializeFirestore(
  app,
  { ignoreUndefinedProperties: true },
  isolatedEnvironment.database
);

const functions = getFunctions(app, isolatedEnvironment.functionsRegion);

if (useEmulator) {
  const hostname = window.location.hostname;
  connectAuthEmulator(auth, `http://${hostname}:9099`);
  connectFunctionsEmulator(functions, hostname, 5001);
  connectFirestoreEmulator(db, hostname, 5002);
  connectStorageEmulator(storage, hostname, 9199);
  console.debug("🤖 Using Firebase Emulator");
}

export function bindFunction<Input, Output>(name: string) {
  // httpsCallable returns a function that can be called with the input data but it allows null as input
  // so we need to cast it to a more restrictive type to prevent errors
  return httpsCallable<Input, Output>(
    functions,
    `${isolatedEnvironment.functions}-${name}`
  ) as (data: Input) => Promise<HttpsCallableResult<Output>>;
}

export { app, auth, db, storage, googleProvider, githubProvider };
