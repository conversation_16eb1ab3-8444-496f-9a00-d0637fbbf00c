import { collection, doc, getDoc } from "firebase/firestore";
import { urlParams } from "@/utils/urlParams";
import { getHash } from "@/core/util/hash/xxhash";
import {
  AssetCacheLookupRequest,
  AssetCacheLookupResponse,
  DbCollections,
  ModelGenerationProviderKeys,
  ModelGenerationProviderKeysSchema,
  ModelGenerationTaskBody,
  ModelGenerationTaskId,
  ModelGenerationTaskType,
  WorldId,
} from "@nilo/firebase-schema";
import {
  ModelGenerationTaskError,
  runModelGenerationTask,
  trackModelGenerationTask,
} from "@/utils/api-tasks/runModelGenerationTask";
import { ModelData } from "@/liveblocks.config";
import { FirebaseStorageIntegration } from "@/core/util/firebase";
import { db, bindFunction } from "@/config/firebase";

const lookupAssetCache = bindFunction<
  AssetCacheLookupRequest,
  AssetCacheLookupResponse
>("lookupAssetCache");

export class ModelGenerator {
  private firebaseStorage = new FirebaseStorageIntegration();

  public async generate({
    input,
    type,
    worldId,
  }: {
    input: string;
    type: string;
    worldId?: WorldId | undefined;
  }): Promise<ModelGenerationTaskId> {
    const { prompt, providerVersion } = extractProviderVersion(input);
    if (prompt === "") {
      throw new ModelGenerationTaskError(
        "Empty prompt appeared",
        "Ensure input is not empty"
      );
    }

    let cacheKey: string;
    let taskBody: ModelGenerationTaskBody;

    if (type === "text") {
      cacheKey = formatPrompt(prompt);
      taskBody = {
        type: ModelGenerationTaskType.text_to_model,
        prompt,
      };
    } else {
      // For images, upload first
      const uploadResult =
        await this.firebaseStorage.uploadTempImageToFirestore(prompt, type);
      const imageUrl = uploadResult;
      if (!imageUrl) {
        throw new ModelGenerationTaskError(
          "Failed to upload image",
          "Try again later"
        );
      }

      cacheKey = `image-${await generateImageHash(prompt)}`;
      taskBody = {
        type: ModelGenerationTaskType.image_to_model,
        imageUrl,
        imageType: type.split("/").pop()!,
      };
    }

    // lookup cache
    if (urlParams.allowPromptCache) {
      try {
        const lookupResult = await lookupAssetCache({
          type: "3d-model",
          cacheKey,
          rigged: false,
          provider: providerVersion ?? urlParams.modelGenerator,
        });
        if (lookupResult.data.type !== "3d-model") {
          throw new Error("Lookup result is not a 3d model");
        }
        if (lookupResult.data.assets.length > 0) {
          const cachedModelData = lookupResult.data.assets[0];
          return cachedModelData.taskId;
        }
      } catch (error) {
        console.error("❌ Error looking up cache", error);
      }
    }

    try {
      return await runModelGenerationTask({
        taskBody,
        cacheKey: cacheKey,
        providerVersion: providerVersion ?? urlParams.modelGenerator,
        worldId,
      });
    } catch (error) {
      const err = error as Error;
      console.error("❌ Error generating task", err);
      if (error instanceof ModelGenerationTaskError) {
        throw error;
      }
      if (
        "message" in err &&
        "suggestion" in err &&
        typeof err.suggestion === "string"
      ) {
        throw new ModelGenerationTaskError(err.message, err.suggestion);
      } else {
        throw new ModelGenerationTaskError(String(err), "Try again later");
      }
    }
  }

  public async trackGeneration({
    taskId,
    onProgress,
  }: {
    taskId: string;
    onProgress: (progress: number, timeLeftMs: number) => void;
  }): Promise<ModelData> {
    try {
      const result = await trackModelGenerationTask({
        taskId,
        onProgress: (progress: number, etaSeconds: number | undefined) => {
          onProgress(progress, (etaSeconds ?? 100) * 1000);
        },
      });
      console.debug("🐛 Completed task", result);
      return {
        ...result,
        taskId,
      };
    } catch (error) {
      const err = error as Error;
      console.error("❌ Error running task", err);
      if (error instanceof ModelGenerationTaskError) {
        throw error;
      }
      if (
        "message" in err &&
        "suggestion" in err &&
        typeof err.suggestion === "string"
      ) {
        throw new ModelGenerationTaskError(err.message, err.suggestion);
      } else {
        throw new ModelGenerationTaskError(String(err), "Try again later");
      }
    }
  }

  public async rig({
    modelData,
    taskId,
    worldId,
  }: {
    modelData: ModelData;
    taskId: string;
    worldId?: WorldId | undefined;
  }): Promise<ModelGenerationTaskId> {
    try {
      if (taskId === undefined) {
        throw new Error("No task ID provided, cannot rig");
      }
      let cacheKey: string;
      if (modelData.cacheKey === undefined) {
        console.debug(
          "🐛 No storage cache key provided, looking up previous task"
        );
        const tasksCollection = collection(
          db,
          DbCollections.modelGenerationTasks
        );
        const previousTask = (
          await getDoc(doc(tasksCollection, taskId))
        ).data();
        if (previousTask === undefined) {
          throw new Error("Previous task not found");
        }
        cacheKey = previousTask.cacheKey;
      } else {
        cacheKey = modelData.cacheKey;
      }

      // lookup cache
      if (urlParams.allowPromptCache) {
        try {
          const lookupResult = await lookupAssetCache({
            type: "3d-model",
            cacheKey,
            rigged: true,
            provider: modelData.provider ?? urlParams.modelGenerator,
          });
          if (lookupResult.data.type !== "3d-model") {
            throw new Error("Lookup result is not a 3d model");
          }
          if (lookupResult.data.assets.length > 0) {
            const cachedModelData = lookupResult.data.assets[0];
            return cachedModelData.taskId;
          }
        } catch (error) {
          console.error("❌ Error looking up cache", error);
        }
      }

      const newTaskId = await runModelGenerationTask({
        taskBody: {
          type: ModelGenerationTaskType.animate_rig,
          taskId,
        },
        cacheKey,
        worldId,
      });
      return newTaskId;
    } catch (error) {
      console.error("❌ Error running task", error);
      let message;
      let suggestion = "Try again later";
      if (error instanceof ModelGenerationTaskError) {
        message = error.message;
        suggestion = error.suggestion;
      } else {
        message = String(error);
      }
      throw new ModelGenerationTaskError(message, suggestion);
    }
  }
}

function extractProviderVersion(input: string): {
  providerVersion: ModelGenerationProviderKeys | undefined;
  prompt: string;
} {
  const providerVersion = input.match(/^@([\w_]+)\s+/)?.[1] ?? undefined;
  const parsedProviderVersion =
    ModelGenerationProviderKeysSchema.safeParse(providerVersion);
  if (!parsedProviderVersion.success) {
    return { providerVersion: undefined, prompt: input };
  }
  const prompt = input.replace(/^@[\w_]+\s+/, "");
  return { providerVersion: parsedProviderVersion.data, prompt };
}

function formatPrompt(prompt: string): string {
  // Convert to lowercase
  prompt = prompt.toLowerCase();

  // Trim leading and trailing whitespace
  prompt = prompt.trim();

  // Replace multiple spaces with a single space
  prompt = prompt.replace(/\s+/g, " ");

  // Replace single space with an underscore
  prompt = prompt.replace(/ /g, "_");

  // Replace slashes with underscores
  prompt = prompt.replace(/\//g, "_");

  return prompt;
}

async function generateImageHash(b64: string): Promise<string> {
  // const b64Image = await this.fileToBase64(image);
  const hash = (await getHash(b64)).toString();
  return hash;
}
