// main.ts
import path from "node:path";
import { config } from "./config.js";
import { figmaVariablesToTailwind } from "./convert-tokens-to-tailwind.js";
import { figmaVariablesToTypeScript } from "./convert-tokens-to-typescript.js";
import { annotateTailwindThemeVars } from "./annotate-tailwind-theme-vars.js";
import { resolveCssVariables } from "./resolve-css-variables.js";
import { cleanTokensJson } from "./clean-tokens-json.js";

interface Args {
  in?: string;
  out?: string;
  prefix?: string;
  "no-docs"?: string;
  "no-resolve"?: string;
  "no-typescript"?: string;
  "typescript-only"?: string;
  "ts-out"?: string;
  "clean-tokens-json"?: string;
}

/**
 * Main orchestrator for the UI theme importer pipeline
 *
 * Run: node apps/ui-theme-importer/src/main.ts
 * Options:
 *  --in=<path to figma-plugin-export.json>
 *  --out=<path to output css>
 *  --prefix=<custom prefix, default "nilo-">
 *  --no-docs=<skip annotation step>
 *  --no-resolve=<skip variable resolution step>
 *  --no-typescript=<skip TypeScript generation (enabled by default)>
 *  --typescript-only=<generate only TypeScript, skip CSS generation>
 *  --clean-tokens-json=<clean tokens JSON by removing scope and $extensions>
 */
async function main(): Promise<void> {
  const args = parseArgs(process.argv.slice(2));
  const inputPath = args.in || config.tokensPath;
  const outputPath = args.out || config.outputCssPath;
  const prefix = args.prefix || config.prefix;
  const skipDocs = args["no-docs"] === "true";
  const skipResolve = args["no-resolve"] === "true";
  const skipTypescript = args["no-typescript"] === "true";
  const typescriptOnly = args["typescript-only"] === "true";
  const tsOutputPath = args["ts-out"] || config.outputTsPath;
  const cleanTokensOnly = args["clean-tokens-json"] === "true";

  // If typescript-only or clean-tokens-json is specified, skip CSS generation
  const skipCss = typescriptOnly || cleanTokensOnly;

  console.debug("🚀 Starting UI theme import pipeline...");
  console.debug(`📁 Input: ${path.relative(process.cwd(), inputPath)}`);
  if (!skipCss) {
    console.debug(`📄 Output: ${path.relative(process.cwd(), outputPath)}`);
  }
  console.debug(`🏷️  Prefix: ${prefix}`);
  console.debug(`📚 Skip docs: ${skipDocs}`);
  console.debug(`🔧 Skip resolve: ${skipResolve}`);
  console.debug(`📝 Skip TypeScript: ${skipTypescript}`);
  console.debug(`🎯 TypeScript only: ${typescriptOnly}`);
  console.debug(`🧹 Clean tokens only: ${cleanTokensOnly}`);
  if (!skipTypescript) {
    console.debug(
      `📄 TypeScript output: ${path.relative(process.cwd(), tsOutputPath)}`
    );
  }

  // Step 1: Clean tokens JSON (if --clean-tokens-json is specified)
  if (cleanTokensOnly) {
    console.debug("\n🧹 Step 1: Cleaning Figma tokens JSON...");
    cleanTokensJson(inputPath);
    console.debug("\n✅ Token cleanup completed successfully!");
    return; // Exit early after cleanup
  }

  // Step 2: Convert Figma variables to Tailwind CSS (skip if typescript-only)
  if (!skipCss) {
    console.debug("\n📝 Step 1: Converting Figma variables to Tailwind CSS...");
    await figmaVariablesToTailwind(inputPath, outputPath, prefix);
  } else {
    console.debug("\n⏭️  Step 1: Skipping CSS generation (--typescript-only)");
  }

  // Step 2: Annotate Tailwind theme variables (optional, skip if typescript-only)
  if (!skipDocs && !skipCss) {
    console.debug("\n🏷️  Step 2: Annotating Tailwind theme variables...");
    await annotateTailwindThemeVars(outputPath);
  } else if (skipCss) {
    console.debug("\n⏭️  Step 2: Skipping annotation (--typescript-only)");
  } else {
    console.debug("\n⏭️  Step 2: Skipping annotation (--no-docs)");
  }

  // Step 3: Resolve CSS variables (optional, skip if typescript-only)
  if (!skipResolve && !skipCss) {
    console.debug("\n🔍 Step 3: Resolving CSS variables...");
    await resolveCssVariables(outputPath, outputPath);
  } else if (skipCss) {
    console.debug(
      "\n⏭️  Step 3: Skipping variable resolution (--typescript-only)"
    );
  } else {
    console.debug("\n⏭️  Step 3: Skipping variable resolution (--no-resolve)");
  }

  // Step 4: Generate TypeScript tokens (default behavior, unless --no-typescript)
  if (!skipTypescript) {
    console.debug("\n📝 Step 4: Generating TypeScript tokens...");
    await figmaVariablesToTypeScript(inputPath, tsOutputPath, config);
  } else {
    console.debug(
      "\n⏭️  Step 4: Skipping TypeScript generation (--no-typescript specified)"
    );
  }

  console.debug("\n✅ UI theme import pipeline completed successfully!");
  if (!skipCss && !skipResolve) {
    console.debug(
      `📄 Final CSS output: ${path.relative(process.cwd(), outputPath)}`
    );
  }
  if (!skipTypescript) {
    console.debug(
      `📄 TypeScript output: ${path.relative(process.cwd(), tsOutputPath)}`
    );
  }
}

/**
 * Parse command line arguments
 */
function parseArgs(args: string[]): Args {
  const out: Args = {};
  for (const a of args) {
    const m = /^--([^=]+)(?:=(.*))?$/.exec(a);
    if (m) {
      out[m[1] as keyof Args] = m[2] || "true"; // Handle --no-resolve without value
    }
  }
  return out;
}

main().catch((err) => {
  console.error("❌ Pipeline failed:", err);
  process.exit(1);
});
