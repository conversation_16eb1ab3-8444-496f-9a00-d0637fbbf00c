import Jolt from "jolt-physics";

import { createDelegateFunction, DelegateFunction } from "@nilo/utilities";

import { Matrix4, Quaternion, Vector3 } from "three";
import { BodyControl } from "./controller/BodyControl";
import { BodyControlFactory } from "./factories/BodyControlFactory";
import { createContactsService } from "./factories/ContactsService";
import {
  LAYER_CHARACTER_COLLIDERS,
  LAYER_CHARACTER_SHAPE,
  LAYER_MOVING,
  LAYER_NON_MOVING,
  NUM_OBJECT_LAYERS,
} from "./lib/constants";
import { PhysicsOptions, PhysicsShapeType } from "./types/PhysicsOptions";
import { RagdollSettings } from "./types/Ragdoll";
import {
  RagdollControl,
  RagdollControlFactory,
} from "./factories/RagdollControlFactory";
import { BodyGeometry, PhysicsBody, PhysicsBodyUpdate } from "./types/Body";
import { JoltBody, JoltShape } from "./types/debug";
import { bodyToJoltBody, buildJoltShape } from "./debug/bodyToJoltBody";
import { CharacterPhysicsSettings } from "./types/character";
import { CharacterControl } from "./controller/CharacterControl";
import { JoltInternals } from "./types/jolt";
import { PhysicsStepInfo } from "./types/PhysicsStep";
import { DynamicProperties } from "./types/webworker";
import { joltVector3ToThreeVector3 } from "./util/jolt_types_utils";
import { isValidGeometry } from "./util/isValidGeometry";

// Not much thought went into this value
export const MIN_MEMORY_ALLOWED = 1000000; // 1MB

export type PhysicsWorldDelegates = {
  readonly beforeFrameUpdate: DelegateFunction<[]>;
  readonly afterFrameUpdate: DelegateFunction<[]>;
  readonly afterObjectBodyAdded: DelegateFunction<
    [object: PhysicsBody, bodyCtrl: BodyControl]
  >;
  readonly afterObjectBodyRemoved: DelegateFunction<
    [object: PhysicsBody, bodyCtrl: BodyControl]
  >;
  readonly afterObjectChanged: DelegateFunction<
    [object: PhysicsBody, bodyCtrl: BodyControl]
  >;
  readonly startLoop: DelegateFunction<[]>;
  readonly stopLoop: DelegateFunction<[]>;
  readonly bodyChanged: DelegateFunction<
    [object: PhysicsBody, bodyID: Jolt.BodyID | Jolt.CharacterID]
  >;
};
export async function createJoltPhysicsService() {
  const dirtyBodies = new Map<string, PhysicsBodyUpdate>();

  const bodyChangedDelegate =
    createDelegateFunction<
      [object: PhysicsBody, bodyId: Jolt.BodyID | Jolt.CharacterID]
    >();

  const delegates: PhysicsWorldDelegates = {
    beforeFrameUpdate: createDelegateFunction(),
    afterFrameUpdate: createDelegateFunction(),
    afterObjectBodyAdded:
      createDelegateFunction<[object: PhysicsBody, bodyCtrl: BodyControl]>(),
    afterObjectBodyRemoved:
      createDelegateFunction<[object: PhysicsBody, bodyCtrl: BodyControl]>(),
    afterObjectChanged:
      createDelegateFunction<[object: PhysicsBody, bodyCtrl: BodyControl]>(),
    startLoop: createDelegateFunction(),
    stopLoop: createDelegateFunction(),
    bodyChanged: bodyChangedDelegate,
  } as PhysicsWorldDelegates;

  const joltModule = await Jolt();
  const freeMemoryAfterJoltModuleLoad = getFreeMemory();
  // let lastMeasurement: number = freeMemoryAfterJoltModuleLoad;
  let freeMemoryBeforeSettingsCreated: number = -1;

  console.debug("🎈 Jolt Physics Service Created");

  function initJoltPhysics() {
    freeMemoryBeforeSettingsCreated = getFreeMemory();
    const settings = new joltModule.JoltSettings();

    // Set up collision filtering
    const objectFilter = new joltModule.ObjectLayerPairFilterTable(
      NUM_OBJECT_LAYERS
    );
    objectFilter.EnableCollision(LAYER_NON_MOVING, LAYER_MOVING);
    objectFilter.EnableCollision(LAYER_MOVING, LAYER_MOVING);
    objectFilter.EnableCollision(LAYER_CHARACTER_COLLIDERS, LAYER_MOVING);
    objectFilter.EnableCollision(LAYER_CHARACTER_COLLIDERS, LAYER_NON_MOVING);
    objectFilter.EnableCollision(
      LAYER_CHARACTER_COLLIDERS,
      LAYER_CHARACTER_COLLIDERS
    );
    objectFilter.EnableCollision(LAYER_CHARACTER_SHAPE, LAYER_NON_MOVING);
    objectFilter.EnableCollision(LAYER_CHARACTER_SHAPE, LAYER_MOVING);
    objectFilter.EnableCollision(LAYER_CHARACTER_SHAPE, LAYER_CHARACTER_SHAPE);

    const BP_LAYER_NON_MOVING = new joltModule.BroadPhaseLayer(0);
    const BP_LAYER_MOVING = new joltModule.BroadPhaseLayer(1);
    const NUM_BROAD_PHASE_LAYERS = 2;

    const bpInterface = new joltModule.BroadPhaseLayerInterfaceTable(
      NUM_OBJECT_LAYERS,
      NUM_BROAD_PHASE_LAYERS
    );
    bpInterface.MapObjectToBroadPhaseLayer(
      LAYER_NON_MOVING,
      BP_LAYER_NON_MOVING
    );
    bpInterface.MapObjectToBroadPhaseLayer(LAYER_MOVING, BP_LAYER_MOVING);
    bpInterface.MapObjectToBroadPhaseLayer(
      LAYER_CHARACTER_COLLIDERS,
      BP_LAYER_MOVING
    );
    bpInterface.MapObjectToBroadPhaseLayer(
      LAYER_CHARACTER_SHAPE,
      BP_LAYER_MOVING
    );
    settings.mObjectLayerPairFilter = objectFilter;
    settings.mBroadPhaseLayerInterface = bpInterface;
    settings.mObjectVsBroadPhaseLayerFilter =
      new joltModule.ObjectVsBroadPhaseLayerFilterTable(
        settings.mBroadPhaseLayerInterface,
        NUM_BROAD_PHASE_LAYERS,
        settings.mObjectLayerPairFilter,
        NUM_OBJECT_LAYERS
      );
    const joltInterface = new joltModule.JoltInterface(settings);
    const physicsSystem = joltInterface.GetPhysicsSystem();
    const bodyInterface = physicsSystem.GetBodyInterface();

    joltModule.destroy(settings);

    return {
      joltInterface,
      physicsSystem,
      bodyInterface,
      BP_LAYER_NON_MOVING,
      BP_LAYER_MOVING,
    };
  }

  const hasEnoughMemory = (minMemoryAllowed = MIN_MEMORY_ALLOWED) => {
    const memoryBefore = getFreeMemory();
    const hasEnoughMemory = memoryBefore >= minMemoryAllowed;
    if (!hasEnoughMemory) {
      console.error(
        "💛 Low memory before initializing Jolt Physics Service, skipping object creation",
        memoryBefore
      );
    }
    return hasEnoughMemory;
  };

  const {
    joltInterface,
    physicsSystem,
    bodyInterface,
    BP_LAYER_NON_MOVING,
    BP_LAYER_MOVING,
  } = initJoltPhysics();

  const contactsService = createContactsService(joltModule);
  physicsSystem.SetContactListener(contactsService.listener);

  const bodyControlFactory = new BodyControlFactory(
    joltModule,
    bodyInterface,
    delegates,
    contactsService
  );

  const ragdollControlFactory = new RagdollControlFactory(
    joltModule,
    physicsSystem,
    bodyControlFactory
  );

  const characterControls = new Map<number, CharacterControl>();
  const ragdollControls = new Map<number, RagdollControl>();

  const objectBodiesDictionary = new Map<string, BodyControl>();
  const objectBodiesDictionaryEntries: [string, BodyControl][] = [];
  let objectBodiesDictionaryEntriesDirty = true;

  function _updateBodiesDictionaryEntriesIfDirty() {
    if (!objectBodiesDictionaryEntriesDirty) {
      return;
    }

    objectBodiesDictionaryEntriesDirty = false;
    objectBodiesDictionaryEntries.length = 0;
    const entries = [...objectBodiesDictionary.entries()];
    objectBodiesDictionaryEntries.push(...entries);
  }

  let staticBodiesCount = 0;
  let dynamicBodiesCount = 0;
  const updateBodiesCount = () => {
    const bodyControls = [...objectBodiesDictionary.values()];
    for (const bodyControl of bodyControls) {
      try {
        if (bodyControl.getStatic()) {
          staticBodiesCount++;
        } else {
          dynamicBodiesCount++;
        }
      } catch (error) {
        console.warn("💥 Error in updateBodiesCount", { error });
        continue;
      }
    }
  };
  delegates.bodyChanged.on((_object, _bodyCtrl) => {
    updateBodiesCount();
  });

  let runningPhysicsLoop = false;
  function startPhysicsLoop() {
    if (runningPhysicsLoop) {
      console.warn("⚠️ Physics loop is already running");
      return;
    }
    runningPhysicsLoop = true;
    delegates.startLoop.invoke();
  }

  function hasAnythingDynamic(): boolean {
    return (
      dynamicBodiesCount > 0 ||
      characterControls.size > 0 ||
      ragdollControls.size > 0
    );
  }

  let lastStepNumber = 0;
  function physicsStep(
    deltaTime: number,
    numberOfSteps: number,
    stepNumber: number
  ) {
    if (!hasAnythingDynamic() || !runningPhysicsLoop) {
      return;
    }
    try {
      delegates.beforeFrameUpdate.invoke();

      updatePhysics(deltaTime, numberOfSteps);
      lastStepNumber = stepNumber;
      delegates.afterFrameUpdate.invoke();
    } catch (error) {
      console.error("💥 Error in physics loop", { error });
      stopPhysicsLoop();
    }
  }

  function stopPhysicsLoop() {
    if (!runningPhysicsLoop) {
      console.warn("⚠️ Physics loop is already stopped");
      return;
    }
    runningPhysicsLoop = false;
    delegates.stopLoop.invoke();
  }

  function registerRagdollForPhysics(
    ragdollSettings: RagdollSettings
  ): RagdollControl | undefined {
    if (!hasEnoughMemory()) {
      return undefined;
    }
    const ragdollControl = ragdollControlFactory.createRagdoll(ragdollSettings);

    ragdollControl.ragdoll.AddToPhysicsSystem(Jolt.EActivation_Activate);
    ragdollControls.set(ragdollControl.ragdollGroupID, ragdollControl);

    return ragdollControl;
  }

  const defaultPhysicsOptions: PhysicsOptions = {
    isStatic: false,
    shapeType: "convexHull",
    friction: 0.5,
    restitution: 0.05,
  };
  async function registerObjectForPhysics(
    physicsBody: PhysicsBody,
    geometry: BodyGeometry
  ): Promise<BodyControl | undefined> {
    if (!hasEnoughMemory()) {
      return;
    }
    if (!isValidGeometry(geometry)) {
      //Should never happen
      console.error(
        "💛 Invalid geometry for object:",
        physicsBody.id,
        geometry.geometryId
      );
      return;
    }

    const physicsOptions: PhysicsOptions = {
      ...defaultPhysicsOptions,
      ...physicsBody.physicsOptions,
    };
    const bodyControl = await bodyControlFactory.create(
      physicsBody,
      geometry,
      physicsOptions
    );
    if (!bodyControl) {
      console.warn(
        "💛 Failure to create body control for object:",
        physicsBody.id,
        geometry.geometryId
      );
      return;
    }

    objectBodiesDictionary.set(physicsBody.id, bodyControl);
    objectBodiesDictionaryEntriesDirty = true;
    updateBodiesCount();
    return bodyControl;

    // (window as any).debugFreeMemory("registerObjectForPhysics B");
  }

  function unregisterObjectFromPhysics(id: string) {
    const bodyControl = objectBodiesDictionary.get(id);
    if (!bodyControl) {
      return;
    }
    bodyControl.destroyBody();

    objectBodiesDictionary.delete(id);
    objectBodiesDictionaryEntriesDirty = true;
    updateBodiesCount();
    delegates.afterObjectBodyRemoved.invoke(
      bodyControl.physicsBody,
      bodyControl
    );
    return bodyControl;
  }

  function unregisterRagdollFromPhysics(id: number) {
    const ragdollControl = ragdollControls.get(id);
    if (!ragdollControl) {
      console.error("Ragdoll not found in ragdollControls");
      return;
    }
    ragdollControl.dispose();
  }

  const initialGravity = physicsSystem.GetGravity();
  const _zeroGravity = new joltModule.Vec3(0, 0, 0);
  function setGravityEnabled(enabled: boolean) {
    if (enabled) {
      physicsSystem.SetGravity(initialGravity);
    } else {
      physicsSystem.SetGravity(_zeroGravity);
    }
  }

  const isLoggingPhysicsUpdate = false;
  const updateTimes: number[] = [];
  let updateTimesTotal = 0;
  let totalFrames = 0;

  const _offsetMatrix = new Matrix4();
  const _position = new Vector3();
  const _rotation = new Quaternion();
  const _scale = new Vector3();
  function updatePhysics(deltaTime: number, numberOfSteps: number) {
    const startTime = isLoggingPhysicsUpdate ? performance.now() : 0;

    joltInterface.Step(deltaTime, numberOfSteps);

    //Update characters
    for (const characterControl of characterControls) {
      characterControl[1].update(deltaTime);
    }

    _updateBodiesDictionaryEntriesIfDirty();
    for (let i = 0; i < objectBodiesDictionaryEntries.length; i++) {
      const pair = objectBodiesDictionaryEntries[i];
      const obj = pair[0];
      const bodyControl = pair[1];
      if (bodyControl.shouldPauseUpdatesFromPhysics) {
        continue;
      }

      const body = bodyControl.getBodyRaw();
      if (!body) {
        continue;
      }

      const { position, rotation } = bodyControl.getWorldTransform();
      const activeDynamicProperties = bodyControl.getActiveDynamicProperties();
      const dynamicProperties = {} as DynamicProperties;
      activeDynamicProperties.forEach((property) => {
        switch (property) {
          case "linearVelocity":
            dynamicProperties.linearVelocity = joltVector3ToThreeVector3(
              body.GetLinearVelocity()
            );
            break;
          case "angularVelocity":
            dynamicProperties.angularVelocity = joltVector3ToThreeVector3(
              body.GetAngularVelocity()
            );
            break;
          case "contactCount":
            dynamicProperties.contactCount = bodyControl.getContactCount();
            break;
          case "isActive":
            dynamicProperties.isActive = bodyControl.isActive();
        }
      });

      dirtyBodies.set(obj, {
        id: obj,
        position: position,
        rotation: {
          x: rotation.x,
          y: rotation.y,
          z: rotation.z,
          w: rotation.w,
        },
        dynamicProperties,
      });
    }

    if (isLoggingPhysicsUpdate) {
      const endTime = performance.now();
      const updateTime = endTime - startTime;
      updateTimesTotal += updateTime;
      updateTimes.push(updateTime);
      totalFrames++;
      const FRAMES_TO_MEASURE = 60;
      if (updateTimes.length > FRAMES_TO_MEASURE) {
        updateTimes.shift();
      }

      if (updateTimes.length === FRAMES_TO_MEASURE) {
        const averageTime =
          updateTimes.reduce((a, b) => a + b, 0) / FRAMES_TO_MEASURE;
        console.debug(
          `Average physics update over last ${FRAMES_TO_MEASURE} frames: ${averageTime.toFixed(2)}ms for ${
            objectBodiesDictionaryEntries.length
          } objects. TotalAverage: ${updateTimesTotal / totalFrames}ms.`
        );
      }
    }
  }
  function getStaticBodiesCount() {
    return staticBodiesCount;
  }
  function getDynamicBodiesCount() {
    return dynamicBodiesCount;
  }
  function getFreeMemory(): number {
    return joltModule.JoltInterface.prototype.sGetFreeMemory() ?? Infinity;
  }

  // function debugFreeMemory(label: string) {
  //   const currentFreeMemory = getFreeMemory();
  //   const usedMemory = freeMemoryAfterJoltModuleLoad - currentFreeMemory;
  //   const usedMemorySinceLastMeasurement = lastMeasurement - currentFreeMemory;
  //   console.debug("💥💥💥", label, usedMemory, usedMemorySinceLastMeasurement);
  //   lastMeasurement = currentFreeMemory;
  // }
  // (window as any).debugFreeMemory = debugFreeMemory;
  // debugFreeMemory("init");

  function destroy() {
    for (const body of objectBodiesDictionary.values()) {
      body.destroyBody();
    }
    objectBodiesDictionary.clear();

    for (const body of characterControls) {
      body[1].dispose();
    }
    characterControls.clear();

    bodyControlFactory.destroy();
    joltModule.destroy(_zeroGravity);
    joltModule.destroy(joltInterface);
    joltModule.destroy(contactsService.listener);
    joltModule.destroy(BP_LAYER_NON_MOVING);
    joltModule.destroy(BP_LAYER_MOVING);
  }

  async function findBestShape(
    geometry: BodyGeometry
  ): Promise<PhysicsShapeType> {
    return bodyControlFactory.findBestShape(geometry);
  }

  ////////////////////////////////////////////
  ////////////////////////////////////////////
  // delegates.afterObjectBodyRemoved.on((object, bodyCtrl) => {
  //   stopPhysicsLoop();
  //   console.debug("💥 Body removed", object.name);
  //   console.debug("💥 Current state", {
  //     animationFrameId,
  //     dynamicBodiesCount,
  //   });
  //   console.debug("💥 Current objects", {
  //     objectBodiesDictionary: [...objectBodiesDictionary.keys()],
  //   });
  // });
  ////////////////////////////////////////////
  ////////////////////////////////////////////

  //Used to avoid sending the same shape multiple times
  const bodyShapesSent = new Set<string>();
  delegates.afterObjectBodyRemoved.on((object, bodyCtrl) => {
    const bodyIDString = "body_" + bodyCtrl.getBodyID().GetIndex();
    bodyShapesSent.delete(bodyIDString);
  });

  //Detect changes on body, in order to update the debug mesh when needed
  delegates.bodyChanged.on((object, bodyId) => {
    if (bodyId instanceof joltModule.BodyID) {
      const bodyIDString = "body_" + bodyId.GetIndex();

      bodyShapesSent.delete(bodyIDString);
    }
    if (bodyId instanceof joltModule.CharacterID) {
      const bodyIDString = "character_" + bodyId.GetValue();
      bodyShapesSent.delete(bodyIDString);
    }
  });

  //Get all bodies and shapes for debug mesh rendering
  const getAllBodies = (getAllShapes: boolean = false) => {
    const joltBodies = [] as JoltBody[];
    const bodyIDs = new joltModule.BodyIDVector();
    physicsSystem.GetBodies(bodyIDs);
    const sentShapes = new Set<string>();

    const bodyLockInterface = physicsSystem.GetBodyLockInterface();

    for (let i = 0; i < bodyIDs.size(); i++) {
      const bodyID = bodyIDs.at(i);
      const bodyIDString = "body_" + bodyID.GetIndex();
      const body = bodyLockInterface.TryGetBody(bodyID);
      const includeShape = getAllShapes || !bodyShapesSent.has(bodyIDString);
      const joltBody = bodyToJoltBody(body, joltModule, includeShape);
      joltBodies.push(joltBody);
      if (includeShape) {
        sentShapes.add(bodyIDString);
      }
    }

    //Add character capsules
    for (const characterControl of characterControls) {
      const character = characterControl[1].getCharacter();
      if (!character || !characterControl[1].isEnabled) {
        continue;
      }
      const characterBody = character.body;
      const bodyID = characterBody.GetID();
      const bodyIDString = "character_" + bodyID.GetValue();
      let joltShape: JoltShape | undefined = undefined;
      const includeShape = getAllShapes || !bodyShapesSent.has(bodyIDString);
      if (includeShape) {
        joltShape = buildJoltShape(characterBody.GetShape(), joltModule);
        if (joltShape.type === "capsule") {
          joltShape.offset = {
            x: 0,
            y: joltShape.height / 2 + joltShape.radius,
            z: 0,
          };
        }
      }
      const position = {
        x: characterBody.GetPosition().GetX(),
        y: characterBody.GetPosition().GetY(),
        z: characterBody.GetPosition().GetZ(),
      };
      const rotation = {
        x: characterBody.GetRotation().GetX(),
        y: characterBody.GetRotation().GetY(),
        z: characterBody.GetRotation().GetZ(),
        w: characterBody.GetRotation().GetW(),
      };
      const joltBody: JoltBody = {
        id: characterBody.GetID().GetValue(),
        transform: {
          id: characterBody.GetID().GetValue(),
          position,
          rotation,
          scale: { x: 1, y: 1, z: 1 },
        },
        motionType: "kinematic",
        shape: joltShape,
        type: "character",
      };
      joltBodies.push(joltBody);
      if (includeShape) {
        sentShapes.add(bodyIDString);
      }
    }

    sentShapes.forEach((shape) => {
      bodyShapesSent.add(shape);
    });
    return joltBodies;
  };

  const getAndClearDirtyBodies = () => {
    const dirtyBodiesArray = [...dirtyBodies];
    dirtyBodies.clear();
    return dirtyBodiesArray;
  };

  const getCharacters = () => {
    const characters = Array.from(characterControls.values()).map(
      (characterControl) => {
        return characterControl.toJSON();
      }
    );
    return characters;
  };

  const joltInternals: JoltInternals = {
    joltModule,
    joltInterface,
    physicsSystem,
    bodyInterface,
    getFreeMemory,
  } as JoltInternals;

  const createCharacterPhysics = (
    characterSettings: Partial<CharacterPhysicsSettings>
  ) => {
    const control = new CharacterControl(characterSettings, joltInternals);
    characterControls.set(control.getBodyID(), control);
    return control;
  };

  const unregisterCharacterFromPhysics = (controlId: number) => {
    const control = characterControls.get(controlId);
    if (!control) return;

    control.dispose();
    characterControls.delete(controlId);
  };

  //We can change this to vector if we want gravity that is not just in the y direction
  const gravity = physicsSystem.GetGravity().GetY();
  const setGravity = (gravity: number) => {
    physicsSystem.SetGravity(new joltModule.Vec3(0, gravity, 0));
  };
  const getGravity = () => {
    return physicsSystem.GetGravity().GetY();
  };

  return {
    delegates,
    getAllBodies,
    createCharacterPhysics,
    unregisterCharacterFromPhysics,
    startPhysicsLoop,
    stopPhysicsLoop,
    isPhysicsLoopRunning: () => runningPhysicsLoop,

    getBodyControlById: (id: string) => objectBodiesDictionary.get(id),
    getObjectBodyControlPairs: () => [...objectBodiesDictionaryEntries],

    registerRagdollForPhysics,
    registerObjectForPhysics,
    unregisterObjectFromPhysics,
    unregisterRagdollFromPhysics,
    destroy,
    findBestShape,

    gravity,
    setGravity,
    getGravity,

    setGravityEnabled,
    getStaticBodiesCount,
    getDynamicBodiesCount,

    memoryStats: {
      freeMemoryAfterJoltModuleLoad,
      freeMemoryBeforeSettingsCreated,
    },

    getLastStepInfo(): PhysicsStepInfo {
      const contacts = contactsService.getAndClearContacts();
      const contactsAdded = contacts.contactsAdded;
      const contactsRemoved = contacts.contactsRemoved;
      return {
        stepNumber: lastStepNumber,
        bodies: getAndClearDirtyBodies().map(([_, body]) => body),
        characters: getCharacters().filter((character) => character !== null),
        contactsAdded: contactsAdded.map((contact) => ({
          body1Id: contact.body1.GetIndex(),
          body2Id: contact.body2.GetIndex(),
        })),
        contactsRemoved: contactsRemoved.map((contact) => ({
          body1Id: contact.body1.GetIndex(),
          body2Id: contact.body2.GetIndex(),
        })),
        dynamicBodiesCount, //This doesn't necessarily need to be updated here
        staticBodiesCount, //This doesn't necessarily need to be updated here
      };
    },

    physicsStep,
  };
}

export type JoltPhysicsService = Awaited<
  ReturnType<typeof createJoltPhysicsService>
>;
