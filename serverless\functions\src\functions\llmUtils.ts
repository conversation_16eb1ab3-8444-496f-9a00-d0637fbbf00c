import { createAnthropic } from "@ai-sdk/anthropic";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { createGroq } from "@ai-sdk/groq";
import { createOpenAI } from "@ai-sdk/openai";
import { CompoundModelTaskService, UserId } from "@nilo/firebase-schema";
import { withTracing } from "@posthog/ai";
import { LanguageModelUsage, LanguageModelV1 } from "ai";
import { posthog } from "../posthog";
import { secretKeys } from "../constants/secretKeys";

// Service Configuration
const serviceConfigs = {
  google: createGoogleGenerativeAI({
    apiKey: secretKeys.googleApiKey,
  }),
  openai: createOpenAI({
    apiKey: secretKeys.openaiApiKey,
    baseURL: "https://api.openai.com/v1",
  }),
  anthropic: createAnthropic({
    apiKey: secretKeys.anthropicApiKey,
  }),
  groq: createGroq({
    apiKey: secretKeys.groqApiKey,
  }),
  cerebras: createOpenAI({
    apiKey: secretKeys.cerebrasApiKey,
    baseURL: "https://api.cerebras.ai/v1",
  }),
  togetherai: createOpenAI({
    apiKey: secretKeys.togetheraiApiKey,
    baseURL: "https://api.together.xyz/v1",
  }),
};

export type LLMServiceProvider = keyof typeof serviceConfigs;

type LLMServiceProviderFactoryFunction<P extends LLMServiceProvider> =
  (typeof serviceConfigs)[P];

// extracts string literals from a union type
type ExtractLiterals<T> = T extends string
  ? string extends T
    ? never
    : T
  : never;

type AcceptedExtraModels = {
  google: "gemini-2.5-flash" | "gemini-2.5-pro";
  openai: "gpt-4.1" | "gpt-4o" | "o4-mini" | "gpt-5" | "gpt-5-mini";
  anthropic: never;
  groq:
    | "meta-llama/llama-4-maverick-17b-128e-instruct"
    | "moonshotai/kimi-k2-instruct";
  cerebras: "llama-4-scout-17b-16e-instruct" | "llama-3.3-70b";
  togetherai: "moonshotai/Kimi-K2-Instruct" | "deepseek-ai/DeepSeek-V3";
};

export type LLMServiceProviderModel<P extends LLMServiceProvider> =
  | ExtractLiterals<Parameters<LLMServiceProviderFactoryFunction<P>>[0]>
  | AcceptedExtraModels[P];

export type LLMServiceProviderSettings<P extends LLMServiceProvider> =
  Parameters<LLMServiceProviderFactoryFunction<P>>[1];

type TracedLLMModel = LanguageModelV1 & {
  calculateCostUSD: (usage: LanguageModelUsage) => number;
};

export function getTracedLLMModel<
  P extends LLMServiceProvider & keyof typeof llmCostUSDPerMillionTokens,
>({
  provider,
  model,
  settings = {},
  userId,
}: {
  provider: P;
  model: LLMServiceProviderModel<P>;
  settings?: LLMServiceProviderSettings<P>;
  userId: UserId;
}): TracedLLMModel {
  const providerFactoryFunction = serviceConfigs[provider](model, settings);
  const tracedModel = withTracing(providerFactoryFunction, posthog, {
    posthogDistinctId: userId,
  }) as TracedLLMModel;
  const costPerMillionTokens = llmCostUSDPerMillionTokens[provider][model];
  if (!costPerMillionTokens) {
    throw new Error(
      `Cost per million tokens not found for ${provider} ${model}`
    );
  }
  tracedModel.calculateCostUSD = (usage: LanguageModelUsage) => {
    return calculateCostUSD(costPerMillionTokens, usage);
  };
  return tracedModel;
}

export type LLMCostDollarsPerMillionTokens = {
  [K in keyof LanguageModelUsage]?: number;
};

type CompoundModelTaskServiceModel<
  P extends CompoundModelTaskService["provider"],
> = Extract<CompoundModelTaskService, { provider: P }>["model"];

// good source of pricing data: https://www.helicone.ai/llm-cost (not always correct - togetherai deepseek was off by x1000)
// TODO: we should consider grabbing these data from their source: https://github.com/Helicone/helicone/tree/main/packages/cost
export const llmCostUSDPerMillionTokens: {
  [P in LLMServiceProvider]: {
    [M in
      | LLMServiceProviderModel<P>
      | CompoundModelTaskServiceModel<P>]?: LLMCostDollarsPerMillionTokens;
  };
} = {
  // from https://platform.openai.com/docs/pricing
  openai: {
    "o4-mini": {
      promptTokens: 4,
      completionTokens: 16,
    },
    "gpt-4.1": {
      promptTokens: 2,
      completionTokens: 8,
    },
    "gpt-4o": {
      promptTokens: 2.5,
      completionTokens: 10,
    },
    "gpt-5": {
      promptTokens: 10,
      completionTokens: 30,
    },
    "gpt-5-mini": {
      promptTokens: 0.25,
      completionTokens: 2,
      // cachedPromptTokens: 0.025,
    },
  },
  // from https://www.anthropic.com/pricing#api
  anthropic: {
    "claude-4-sonnet-20250514": {
      promptTokens: 3,
      completionTokens: 15,
    },
    "claude-4-opus-20250514": {
      promptTokens: 15,
      completionTokens: 75,
    },
    "claude-3-5-haiku-20241022": {
      promptTokens: 0.8,
      completionTokens: 4,
    },
  },
  // from https://ai.google.dev/gemini-api/docs/pricing
  google: {
    "gemini-2.5-flash-preview-04-17": {
      promptTokens: 0.3,
      completionTokens: 2.5,
    },
    "gemini-2.5-flash": {
      promptTokens: 0.3,
      completionTokens: 2.5,
    },
    "gemini-2.5-pro": {
      // note: this is for <= 200k tokens
      promptTokens: 1.25,
      completionTokens: 10,
    },
  },
  groq: {
    // from https://console.groq.com/docs/model/meta-llama/llama-4-maverick-17b-128e-instruct
    "meta-llama/llama-4-maverick-17b-128e-instruct": {
      promptTokens: 0.2,
      completionTokens: 0.8,
    },
    "moonshotai/kimi-k2-instruct": {
      promptTokens: 1,
      completionTokens: 3,
    },
    "meta-llama/llama-4-scout-17b-16e-instruct": {
      promptTokens: 0.11,
      completionTokens: 0.34,
    },
    "gemma2-9b-it": {
      promptTokens: 0.2,
      completionTokens: 0.2,
    },
    // from https://console.groq.com/docs/model/deepseek-r1-distill-llama-70b
    "deepseek-r1-distill-llama-70b": {
      promptTokens: 0.75,
      completionTokens: 0.99,
    },
    // from https://console.groq.com/docs/model/deepseek-r1-distill-qwen-32b
    "deepseek-r1-distill-qwen-32b": {
      promptTokens: 0.69,
      completionTokens: 0.69,
    },
  },
  cerebras: {
    // from https://inference-docs.cerebras.ai/models/llama-33-70b
    "llama-3.3-70b": {
      promptTokens: 0.85,
      completionTokens: 1.2,
    },
    // from https://inference-docs.cerebras.ai/models/llama-4-scout
    "llama-4-scout-17b-16e-instruct": {
      promptTokens: 0.65,
      completionTokens: 0.85,
    },
  },
  // from https://www.together.ai/pricing
  togetherai: {
    "deepseek-ai/DeepSeek-V3": {
      promptTokens: 1.25,
      completionTokens: 1.25,
    },
    "moonshotai/Kimi-K2-Instruct": {
      promptTokens: 1,
      completionTokens: 3,
    },
  },
};

export function calculateCostUSD(
  costPerMillionTokens: LLMCostDollarsPerMillionTokens,
  usage: LanguageModelUsage
) {
  return (
    Object.entries(costPerMillionTokens) as [keyof LanguageModelUsage, number][]
  ).reduce((cost, [usageKey, costPerMillionTokens]) => {
    return cost + (usage[usageKey] * costPerMillionTokens) / 1000000;
  }, 0);
}
