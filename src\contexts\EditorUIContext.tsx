import {
  create<PERSON>ontext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { urlParams } from "@/utils/urlParams";
import { KeyboardMappingIDs } from "@/config/keyboardMapping";
import { Client } from "@/core/client";

/**
 * EditorUIContext provides access to UI panel states and controls throughout the component tree.
 *
 * Usage:
 *
 * 1. Wrap your component tree with EditorUIProvider:
 *    ```tsx
 *    <EditorUIProvider>
 *      <YourComponents />
 *    </EditorUIProvider>
 *    ```
 *
 * 2. Access UI state in any child component:
 *    ```tsx
 *    const { isEnvironmentSettingsOpen, isDevSettingsOpen, isLegacyWorldSettingsOpen } = useEditorUIState();
 *    ```
 *
 * 3. Access UI actions in any child component:
 *    ```tsx
 *    const { toggleEnvironmentSettingsPanel, toggleKeyboardShortcuts, toggleFeedback } = useEditorUIActions();
 *    // Toggle: toggleEnvironmentSettingsPanel()
 *    // Open: toggleEnvironmentSettingsPanel(true)
 *    // Close: toggleEnvironmentSettingsPanel(false)
 *    ```
 */

export enum LeftPanelType {
  EnvironmentSettings = "env",
  DevSettings = "dev",
  LegacyWorldSettings = "legacy",
}

//// Only useful for debugging
const DEFAULT_LEFT_PANEL = urlParams.openPanel as LeftPanelType | null;

interface EditorUIState {
  currentLeftPanel: LeftPanelType | null;
  isEnvironmentSettingsOpen: boolean;
  isDevSettingsOpen: boolean;
  isLegacyWorldSettingsOpen: boolean;
  isKeyboardShortcutsOpen: boolean;
  isFeedbackOpen: boolean;
}

interface EditorUIActions {
  toggleEnvironmentSettingsPanel: (open?: boolean) => void;
  toggleDevSettingsPanel: (open?: boolean) => void;
  toggleLegacyWorldSettingsPanel: (open?: boolean) => void;
  setCurrentLeftPanel: (panel: LeftPanelType | null) => void;
  toggleLeftPanel: (panel: LeftPanelType) => void;
  toggleKeyboardShortcuts: (open?: boolean) => void;
  toggleFeedback: (open?: boolean) => void;
}

interface EditorUIContextValue {
  state: EditorUIState;
  actions: EditorUIActions;
}

const EditorUIContext = createContext<EditorUIContextValue | null>(null);

export function EditorUIProvider({ children }: { children: ReactNode }) {
  const { currentLeftPanel, setCurrentLeftPanel, toggleCurrentLeftPanel } =
    useCurrentPanelState();
  const [isKeyboardShortcutsOpen, setIsKeyboardShortcutsOpen] = useState(false);
  const [isFeedbackOpen, setIsFeedbackOpen] = useState(false);

  const state: EditorUIState = useMemo(
    () => ({
      currentLeftPanel,
      isEnvironmentSettingsOpen:
        currentLeftPanel === LeftPanelType.EnvironmentSettings,
      isDevSettingsOpen: currentLeftPanel === LeftPanelType.DevSettings,
      isLegacyWorldSettingsOpen:
        currentLeftPanel === LeftPanelType.LegacyWorldSettings,
      isKeyboardShortcutsOpen,
      isFeedbackOpen,
    }),
    [currentLeftPanel, isKeyboardShortcutsOpen, isFeedbackOpen]
  );

  const toggleEnvironmentSettingsPanel = useCallback(
    (open?: boolean) => {
      const isCurrentlyOpen =
        currentLeftPanel === LeftPanelType.EnvironmentSettings;

      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isCurrentlyOpen) {
        return;
      }

      if (open === true) {
        setCurrentLeftPanel(LeftPanelType.EnvironmentSettings);
      } else if (open === false) {
        setCurrentLeftPanel(null);
      } else {
        // No parameter provided, toggle
        toggleCurrentLeftPanel(LeftPanelType.EnvironmentSettings);
      }
    },
    [currentLeftPanel, setCurrentLeftPanel, toggleCurrentLeftPanel]
  );

  const toggleDevSettingsPanel = useCallback(
    (open?: boolean) => {
      const isCurrentlyOpen = currentLeftPanel === LeftPanelType.DevSettings;

      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isCurrentlyOpen) {
        return;
      }

      if (open === true) {
        setCurrentLeftPanel(LeftPanelType.DevSettings);
      } else if (open === false) {
        setCurrentLeftPanel(null);
      } else {
        // No parameter provided, toggle
        toggleCurrentLeftPanel(LeftPanelType.DevSettings);
      }
    },
    [currentLeftPanel, setCurrentLeftPanel, toggleCurrentLeftPanel]
  );

  const toggleLegacyWorldSettingsPanel = useCallback(
    (open?: boolean) => {
      const isCurrentlyOpen =
        currentLeftPanel === LeftPanelType.LegacyWorldSettings;

      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isCurrentlyOpen) {
        return;
      }

      if (open === true) {
        setCurrentLeftPanel(LeftPanelType.LegacyWorldSettings);
      } else if (open === false) {
        setCurrentLeftPanel(null);
      } else {
        // No parameter provided, toggle
        toggleCurrentLeftPanel(LeftPanelType.LegacyWorldSettings);
      }
    },
    [currentLeftPanel, setCurrentLeftPanel, toggleCurrentLeftPanel]
  );

  const toggleKeyboardShortcuts = useCallback(
    (open?: boolean) => {
      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isKeyboardShortcutsOpen) {
        return;
      }

      if (open === true) {
        setIsKeyboardShortcutsOpen(true);
      } else if (open === false) {
        setIsKeyboardShortcutsOpen(false);
      } else {
        // No parameter provided, toggle
        setIsKeyboardShortcutsOpen((prev) => !prev);
      }
    },
    [isKeyboardShortcutsOpen]
  );

  const toggleFeedback = useCallback(
    (open?: boolean) => {
      // If open parameter is provided and matches current state, do nothing
      if (open !== undefined && open === isFeedbackOpen) {
        return;
      }

      if (open === true) {
        setIsFeedbackOpen(true);
      } else if (open === false) {
        setIsFeedbackOpen(false);
      } else {
        // No parameter provided, toggle
        setIsFeedbackOpen((prev) => !prev);
      }
    },
    [isFeedbackOpen]
  );

  // Keyboard shortcuts
  useKeyboardShortcuts({
    toggleEnvironmentSettingsPanel,
    toggleDevSettingsPanel,
    toggleLegacyWorldSettingsPanel,
    toggleKeyboardShortcuts,
    closeLeftPanel: () => setCurrentLeftPanel(null),
  });

  const actions: EditorUIActions = useMemo(
    () => ({
      toggleEnvironmentSettingsPanel,
      toggleDevSettingsPanel,
      toggleLegacyWorldSettingsPanel,
      setCurrentLeftPanel: setCurrentLeftPanel,
      toggleLeftPanel: toggleCurrentLeftPanel,
      toggleKeyboardShortcuts,
      toggleFeedback,
    }),
    [
      toggleEnvironmentSettingsPanel,
      toggleDevSettingsPanel,
      toggleLegacyWorldSettingsPanel,
      setCurrentLeftPanel,
      toggleCurrentLeftPanel,
      toggleKeyboardShortcuts,
      toggleFeedback,
    ]
  );

  const value: EditorUIContextValue = useMemo(
    () => ({ state, actions }),
    [state, actions]
  );

  return (
    <EditorUIContext.Provider value={value}>
      {children}
    </EditorUIContext.Provider>
  );
}

function useCurrentPanelState() {
  const [currentLeftPanel, setCurrentLeftPanel] =
    useState<LeftPanelType | null>(DEFAULT_LEFT_PANEL);

  return {
    currentLeftPanel,

    setCurrentLeftPanel: useCallback((key: LeftPanelType | null) => {
      setCurrentLeftPanel(key);
    }, []),

    toggleCurrentLeftPanel: useCallback((key: LeftPanelType) => {
      setCurrentLeftPanel((prev) => (prev === key ? null : key));
    }, []),
  };
}

function useKeyboardShortcuts({
  toggleEnvironmentSettingsPanel,
  toggleDevSettingsPanel,
  toggleLegacyWorldSettingsPanel,
  closeLeftPanel,
  toggleKeyboardShortcuts,
}: {
  toggleEnvironmentSettingsPanel: () => void;
  toggleDevSettingsPanel: () => void;
  toggleLegacyWorldSettingsPanel: () => void;
  closeLeftPanel: () => void;
  toggleKeyboardShortcuts: () => void;
}) {
  useEffect(() => {
    // Register keyboard shortcuts declaratively
    const cleanupFunctions = [
      Client.keyboard.registerAction(
        KeyboardMappingIDs.GLOBAL_DISMISS,
        closeLeftPanel
      ),
      Client.keyboard.registerAction(
        KeyboardMappingIDs.OPEN_ENVIRONMENT_SETTINGS,
        toggleEnvironmentSettingsPanel
      ),
      Client.keyboard.registerAction(
        KeyboardMappingIDs.OPEN_DEV_SETTINGS,
        toggleDevSettingsPanel
      ),
      Client.keyboard.registerAction(
        KeyboardMappingIDs.OPEN_LEGACY_WORLD_SETTINGS,
        toggleLegacyWorldSettingsPanel
      ),
      Client.keyboard.registerAction(
        KeyboardMappingIDs.OPEN_KEYBOARD_SHORTCUTS,
        toggleKeyboardShortcuts
      ),
    ];

    // Cleanup all registrations when component unmounts
    return () => {
      cleanupFunctions.forEach((cleanup) => cleanup());
    };
  }, [
    toggleEnvironmentSettingsPanel,
    toggleDevSettingsPanel,
    toggleLegacyWorldSettingsPanel,
    closeLeftPanel,
    toggleKeyboardShortcuts,
  ]);
}

export function useEditorUIState(): EditorUIState {
  const context = useContext(EditorUIContext);
  if (!context) {
    throw new Error("useEditorUIState must be used within a EditorUIProvider");
  }
  return context.state;
}

export function useEditorUIActions(): EditorUIActions {
  const context = useContext(EditorUIContext);
  if (!context) {
    throw new Error(
      "useEditorUIActions must be used within a EditorUIProvider"
    );
  }
  return context.actions;
}
