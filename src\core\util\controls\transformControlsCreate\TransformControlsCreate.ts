import { Matrix4, Object3D, Quaternion, Vector3 } from "three";
import { raycasterGizmoCreate } from "../../RaycastUtils";
import TransformControls, {
  GIZMOSPACE,
  onDragStartPropType,
  OnDragStartType,
} from "../TransformControls";
import { DuplicateControls } from "../DuplicateControls";
import { ControlPointIndex } from "../duplicateControls/DuplicateControlsControlPointsCollection";
import { isMobile } from "../../UserAgent";
import { checkNull } from "../../Check";
import { Client } from "@/core/client";
import { globalDelegates } from "@/core/client/globalDelegates";
import { runCommands } from "@/core/command";
import SetEntityPositionCommand from "@/core/command/commands/setEntityPosition";
import SetEntityOrientationCommand from "@/core/command/commands/setEntityOrientation";
import SetEntityScaleCommand from "@/core/command/commands/setEntityScale";
import { beginTransaction, Transaction } from "@/core/command/transaction";

export interface TransformControlsCreateOptions {
  translateAllowed?: boolean;
  translateXAllowed?: boolean;
  translateYAllowed?: boolean;
  translateZAllowed?: boolean;
  rotateAllowed?: boolean;
  rotateXAllowed?: boolean;
  rotateYAllowed?: boolean;
  rotateZAllowed?: boolean;
  scaleAllowed?: boolean;
  updateEntitiesOnDraggingChanged?: boolean;
  isMobileDevice?: boolean;
}

let dragStartData: onDragStartPropType = {
  component: "Arrow",
  axis: 0,
  origin: new Vector3(),
  directions: [new Vector3()],
};
let transformMoveTransaction: Transaction | null = null;
let dragStartSpace = GIZMOSPACE.LOCAL;
let dragStartMatrixWorld: Matrix4[] = [];

export function isTransformControlsActive(): boolean {
  return transformMoveTransaction !== null;
}

const wp0 = new Vector3();
let selected: Object3D[] = [];

const wp = new Vector3();
const wq = new Quaternion();
const ws = new Vector3();
const lp = new Vector3();
const lq = new Quaternion();
const ls = new Vector3();
const ap = new Vector3();
const aq = new Quaternion();
const as = new Vector3();
const dwp = new Vector3();
const dwq = new Quaternion();
const dws = new Vector3();
const dlp = new Vector3();
const dlq = new Quaternion();
const dls = new Vector3();
const wpp = new Vector3();
const wpq = new Quaternion();
const wps = new Vector3();
const m4 = new Matrix4();

const entityPosition = new Vector3();
const entityOrientation = new Quaternion();
const entityScale = new Vector3();

export function transformControlsCreate(
  options?: TransformControlsCreateOptions,
  duplicateControls?: DuplicateControls,
  i?: ControlPointIndex
) {
  const isMobileDevice: boolean = isMobile();
  const updateEntitiesOnDraggingChanged: boolean =
    options?.updateEntitiesOnDraggingChanged ?? true;

  const camera = Client.userEntity.getCamera();
  const scene = Client.scene;
  const domElement = Client.renderer?.domElement;
  if (!(camera && scene && domElement)) {
    throw new Error("Missing camera, scene, or domElement");
  }

  let dragged = false;

  const onDragStart: OnDragStartType = (data, space) => {
    console.debug("onDragStart");
    dragged = false;
    Client.room.history.pause();
    globalDelegates.startInteractingWithTransformControls.invoke(selected);
    const cameraControls = Client.userEntity.getCameraControls();
    if (!cameraControls) return;

    cameraControls.disableControls();
    dragStartData = data;
    dragStartSpace = space;

    if (updateEntitiesOnDraggingChanged) {
      const selectedEntities = Client.userEntity.getSelectedEntities();
      checkNull(
        transformMoveTransaction,
        "Attempt to start another drag while an existing drag is active"
      );
      transformMoveTransaction = beginTransaction("transform-move");
      for (let i = 0; i < selectedEntities.length; i += 1) {
        Client.markEntityAsDirty(selectedEntities[i], false, false);

        selectedEntities[i].setDragging(true);

        selectedEntities[i].getSimulatedPosition(entityPosition);
        selectedEntities[i].getScale(entityScale);
        selectedEntities[i].getSimulatedOrientation(entityOrientation);

        console.debug(
          "🛫 Starting transform drag",
          entityPosition,
          entityOrientation,
          entityScale
        );

        const id = selectedEntities[i].id;
        runCommands([
          new SetEntityPositionCommand(id, entityPosition),
          new SetEntityOrientationCommand(id, entityOrientation),
          new SetEntityScaleCommand(id, entityScale),
        ]);
      }
      selected = selectedEntities
        .map((e) => e.getRootNode())
        .filter((root) => Boolean(root)) as Object3D[];

      dragStartMatrixWorld = selected.map((obj) => obj.matrix.clone());
      wp0.copy(
        selected.length > 0
          ? selected[selected.length - 1].position
          : new Vector3()
      );
    } else {
      if (duplicateControls && i !== undefined) {
        duplicateControls.markControlPointAsDragged(i);
        wp0.copy(data.origin);
      }
    }
  };

  const onDrag = (_l: Matrix4, _dl: Matrix4, _w: Matrix4, _dw: Matrix4) => {
    dragged = true;
    if (updateEntitiesOnDraggingChanged) {
      const objects: Object3D[] | undefined = controls.getAttachedObjects();
      if (!objects || !objects.length) return;

      // We assume that we have a multiselect feature, not update first selected entity for now
      const entities = Client.userEntity.getSelectedEntities();
      if (entities.length == 0) return;

      Client.room.batch(() => {
        objects.forEach((object, index: number) => {
          if (object) {
            const entity = entities[index];
            if (entity && !entity.getLocked()) {
              entity.setPosition(object.position, false);
              entity.setOrientation(object.quaternion, false);
              entity.setScale(object.scale, false);
              Client.markEntityAsDirty(entity);
            }
          }
        });
      });

      const { component } = dragStartData;
      _w.decompose(wp, wq, ws);
      _l.decompose(lp, lq, ls);
      _dw.decompose(dwp, dwq, dws);
      _dl.decompose(dlp, dlq, dls);

      dragStartMatrixWorld[selected.length - 1].decompose(ap, aq, as);

      selected.forEach((part, index) => {
        dragStartMatrixWorld[index].decompose(wpp, wpq, wps);
        m4.compose(ap, aq, new Vector3(1, 1, 1));

        // dragStartMatrix[index].decompose(lpp, lpq, lps);

        switch (component) {
          case "Arrow":
            if (dragStartSpace === GIZMOSPACE.GLOBAL) {
              part.position.copy(wpp.clone().add(dwp));
            } else {
              part.position.copy(
                wpp
                  .clone()
                  .applyMatrix4(m4.clone().invert())
                  .add(dlp)
                  .applyMatrix4(m4)
              );
            }
            break;
          case "Slider":
            part.position.copy(wpp.clone().add(dwp));
            break;
          case "AxisRotator":
            if (dragStartSpace === GIZMOSPACE.GLOBAL) {
              part.quaternion.copy(wpq.clone().premultiply(dwq));
              part.position.copy(wpp).sub(wp0).applyQuaternion(wq).add(wp0);
            } else {
              part.quaternion.copy(
                wq.clone().multiply(aq.clone().invert()).multiply(wpq)
              );
              part.position.copy(
                wpp
                  .clone()
                  .applyMatrix4(m4.clone().invert())
                  .applyQuaternion(dwq)
                  .applyMatrix4(m4)
              );
            }
            break;
          case "Scale":
            part.scale.copy(wps.clone().multiply(dws));
            break;
        }
      });
    } else {
      if (duplicateControls && i !== undefined) {
        duplicateControls.updateBound();
      }
    }
  };

  const onDragEnd = () => {
    globalDelegates.stopInteractingWithTransformControls.invoke(selected);

    const cameraControls = Client.userEntity.getCameraControls();
    if (!cameraControls) return;

    cameraControls.enableControlsIfNotFirstPerson();

    if (updateEntitiesOnDraggingChanged) {
      const selectedEntities = Client.userEntity.getSelectedEntities();
      for (let i = 0; i < selectedEntities.length; i += 1) {
        selectedEntities[i].setDragging(false);
        //Update dragging state in live entities
        if (dragged) {
          Client.markEntityAsDirty(selectedEntities[i], false, false);

          selectedEntities[i].getPosition(entityPosition);
          selectedEntities[i].getScale(entityScale);
          selectedEntities[i].getOrientation(entityOrientation);

          console.debug(
            "🛬 Ending transform drag",
            entityPosition,
            entityOrientation,
            entityScale
          );
          const id = selectedEntities[i].id;
          runCommands([
            new SetEntityPositionCommand(id, entityPosition.clone()),
            new SetEntityOrientationCommand(id, entityOrientation.clone()),
            new SetEntityScaleCommand(id, entityScale.clone()),
          ]);
        } else {
          transformMoveTransaction?.abort();
        }
      }

      transformMoveTransaction?.end();
      transformMoveTransaction = null;
    } else {
      if (duplicateControls && i !== undefined) {
        duplicateControls.markControlPointAsDragged(null);
        duplicateControls.updateBound();
      }
    }
    Client.room.history.resume();
  };
  const controls = new TransformControls(
    camera,
    domElement,
    raycasterGizmoCreate(),
    scene,
    true,
    options ? { ...options, duplicateControls, i, isMobileDevice } : undefined,
    onDragStart,
    onDrag,
    onDragEnd
  );

  return controls;
}
