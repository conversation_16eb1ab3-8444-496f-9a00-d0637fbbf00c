import EventEmitter from "eventemitter3";
import { LiveblocksEntity } from "./NetworkEntity";
import { Client } from "@/core/client";
import {
  AnimationData,
  EntityType,
  SharedEntityData,
} from "@/liveblocks.config";
import { makeUnique<PERSON>ey } from "@nilo/ecs";

interface Events {
  // Character animation has been added or removed
  "character-motion-update": (motion: AnimationData) => void;
}

/**
 * Entity for storing shared data
 */
export class SharedEntity extends LiveblocksEntity {
  // entity type
  public override readonly type = "SharedEntity";
  public static readonly ID = "shared-entity";

  public readonly events = new EventEmitter<Events>();

  private _privateMotions: AnimationData[] = [];
  private _existingMotions: Set<string> = new Set();

  public static override readonly UNIQUE_ID = makeUniqueKey("SharedEntity");

  constructor() {
    super(true, SharedEntity.ID, false); // non persistent
    Client.animationStorage.initStorage();
  }

  public addCharacterMotion(motion: AnimationData) {
    if (this._privateMotions.find((a) => a.id === motion.id)) {
      return;
    }

    const generatingId = motion.name + "-generating";

    if (motion.isGenerating && this._existingMotions.has(generatingId)) {
      //Remove is generating animation.
      //This is required because we only get the Id when it's generated
      this._privateMotions = this._privateMotions.filter(
        (a) => a.id !== generatingId
      );
      this._removeCharacterMotionFromStorage(generatingId);
    }

    this._addCharacterMotionToStorage(motion);
  }

  private _addCharacterMotionToStorage(motion: AnimationData) {
    Client.animationStorage.addAnimation(motion);
    this._existingMotions.add(motion.id);
    this._privateMotions.push(motion);
    this.events.emit("character-motion-update", motion);
  }

  private _removeCharacterMotionFromStorage(motionId: string) {
    Client.animationStorage.removeAnimation(motionId);
    this._existingMotions.delete(motionId);
    this._privateMotions = this._privateMotions.filter(
      (motion) => motion.id !== motionId
    );
    //this.events.emit("character-motion-update");
  }

  public removeCharacterMotion(motionId: string) {
    this._removeCharacterMotionFromStorage(motionId);
  }

  public getCharacterMotions(): AnimationData[] {
    return this._privateMotions;
  }

  public override toJSON(): SharedEntityData {
    return {
      ...super.toJSON(),
      type: EntityType.SharedEntity,
      characterMotions: this._privateMotions,
    };
  }

  public override fromJSON(json: SharedEntityData) {
    const previousMotions = [...this._privateMotions];
    super.fromJSON(json);
    const newMotions = json.characterMotions;
    const added = newMotions.filter(
      (motion) => !previousMotions.includes(motion)
    );
    const removed = previousMotions.filter(
      (motion) => !newMotions.includes(motion)
    );

    for (const motion of added) {
      this._addCharacterMotionToStorage(motion);
    }
    for (const motion of removed) {
      this._removeCharacterMotionFromStorage(motion.id);
    }
  }
}
