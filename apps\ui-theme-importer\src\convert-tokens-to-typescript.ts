import fs from "node:fs";
import path from "node:path";
import prettier from "prettier";

interface FigmaTokens {
  [sectionName: string]: Record<string, unknown>;
}

interface FullTokens {
  tokens: FigmaTokens;
}

interface TypeScriptConfig {
  stripSuffixes?: string[];
}

interface Config {
  typescript?: TypeScriptConfig;
}

/**
 * Convert Figma variables to TypeScript object with camelCase properties
 * @param {string} inputPath - Path to figma-plugin-export.json
 * @param {string} outputPath - Path to output TypeScript file
 */
export async function figmaVariablesToTypeScript(
  inputPath: string,
  outputPath: string,
  config: Config = {}
): Promise<void> {
  assertExists(inputPath, "Input tokens file not found.");

  const raw = fs.readFileSync(inputPath, "utf-8");
  const tokens: FigmaTokens = JSON.parse(raw);

  // Pass the full tokens object for reference resolution
  const fullTokens: FullTokens = { tokens };

  // Use provided config or defaults
  const tsConfig = config.typescript || {};
  const stripSuffixes = tsConfig.stripSuffixes || [];

  const tsContent = [
    "/**",
    " * ⚠️  WARNING: This file is auto-generated from Figma tokens",
    " *",
    " * DO NOT EDIT THIS FILE MANUALLY!",
    " * Any manual changes will be overwritten the next time the tokens are imported.",
    " *",
    " * To modify these values:",
    " * 1. Update the Figma design tokens",
    " * 2. Re-run the import script: pnpm write-files",
    " *",
    ` * Source: ${path.relative(process.cwd(), inputPath)}`,
    " * Generator: convert-tokens-to-typescript.ts",
    " */",
    "",
    "export const figmaTokens = {",
    ...buildGenericTokensObject(tokens, fullTokens, stripSuffixes),
    "} as const;",
    "",
    "export type FigmaTokens = typeof figmaTokens;",
    "",
  ].join("\n");

  // Format the TypeScript content with Prettier
  const formattedContent = await formatWithPrettier(tsContent, outputPath);

  ensureDir(path.dirname(outputPath));
  fs.writeFileSync(outputPath, formattedContent, "utf-8");
  console.debug(
    `Wrote TypeScript tokens to ${path.relative(process.cwd(), outputPath)}`
  );
}

/**
 * Build a generic tokens object structure from any Figma tokens format
 */
function buildGenericTokensObject(
  tokens: FigmaTokens,
  fullTokens: FullTokens,
  stripSuffixes: string[]
): string[] {
  const lines: string[] = [];

  for (const [sectionName, sectionData] of Object.entries(tokens)) {
    if (!sectionData || typeof sectionData !== "object") continue;

    const camelSectionName = toCamelCase(sectionName, stripSuffixes);
    lines.push(`  ${camelSectionName}: {`);

    const sectionLines = buildSectionObject(
      sectionData,
      fullTokens,
      stripSuffixes
    );
    lines.push(...sectionLines.map((line) => `    ${line}`));

    lines.push("  },");
  }

  return lines;
}

/**
 * Build a generic section object structure
 */
function buildSectionObject(
  sectionData: Record<string, unknown>,
  fullTokens: FullTokens,
  stripSuffixes: string[]
): string[] {
  const lines: string[] = [];

  for (const [key, value] of Object.entries(sectionData)) {
    if (!value || typeof value !== "object") continue;

    const camelKey = toCamelCase(key, stripSuffixes);
    const processedValue = processTokenValue(value, fullTokens, stripSuffixes);

    if (processedValue !== null) {
      if (typeof processedValue === "string" && processedValue.includes("\n")) {
        // Multi-line object
        lines.push(`${camelKey}: {`);
        lines.push(processedValue);
        lines.push("},");
      } else {
        lines.push(`${camelKey}: ${processedValue},`);
      }
    }
  }

  return lines;
}

/**
 * Process a token value and return its TypeScript representation
 */
function processTokenValue(
  tokenData: unknown,
  fullTokens: FullTokens,
  stripSuffixes: string[]
): string | number | null {
  // Handle different token structures
  if (tokenData && typeof tokenData === "object" && "value" in tokenData) {
    // Simple token with value property
    const value = resolveSemanticRef(
      (tokenData as { value: unknown }).value,
      fullTokens
    );
    return formatValue(value, (tokenData as { type?: string }).type);
  } else if (tokenData && typeof tokenData === "object") {
    // Nested object - build recursively
    const nestedLines: string[] = [];
    for (const [nestedKey, nestedValue] of Object.entries(
      tokenData as Record<string, unknown>
    )) {
      if (!nestedValue || typeof nestedValue !== "object") continue;

      const camelNestedKey = toCamelCase(nestedKey, stripSuffixes);
      const processedNestedValue = processTokenValue(
        nestedValue,
        fullTokens,
        stripSuffixes
      );

      if (processedNestedValue !== null) {
        if (
          typeof processedNestedValue === "string" &&
          processedNestedValue.includes("\n")
        ) {
          // Multi-line object
          nestedLines.push(`${camelNestedKey}: {`);
          nestedLines.push(processedNestedValue);
          nestedLines.push("},");
        } else {
          nestedLines.push(`${camelNestedKey}: ${processedNestedValue},`);
        }
      }
    }

    if (nestedLines.length > 0) {
      return nestedLines.join("\n    ");
    }
  }

  return null;
}

/**
 * Format a value based on its type for TypeScript output
 */
function formatValue(value: unknown, type?: string): string | number {
  if (value == null) return "null";

  // Handle different value types
  if (type === "color") {
    return `"${normalizeHexColor(value)}"`;
  } else if (type === "fontWeights" || type === "float") {
    const numValue = parseFloat(String(value));
    return isNaN(numValue) ? `"${value}"` : numValue;
  } else if (type === "string") {
    // Check if it's a font family that needs fallbacks
    if (
      typeof value === "string" &&
      !value.includes("{") &&
      !value.includes("var(")
    ) {
      return `"${appendFallbackFonts(value)}"`;
    }
    return `"${value}"`;
  } else {
    // Default: try to parse as number, fallback to string
    const numValue = parseFloat(String(value));
    return isNaN(numValue) ? `"${value}"` : numValue;
  }
}

/**
 * Helpers
 */

function assertExists(p: string, message: string): void {
  if (!fs.existsSync(p)) {
    console.error(message, p);
    process.exit(1);
  }
}

function ensureDir(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

/**
 * Strip "ff" suffix from 8-character hex colors when they represent 100% opacity
 * Example: #0b0034ff -> #0b0034
 */
function normalizeHexColor(v: unknown): string | number | null | undefined {
  if (v == null) return v;
  const s = String(v).trim().toLowerCase();
  if (s === "") return s;

  // Check if it's an 8-character hex color ending with "ff"
  if (/^#[0-9a-fA-F]{8}$/.test(s) && s.toLowerCase().endsWith("ff")) {
    return s.slice(0, -2); // Remove last 2 characters
  }

  return s;
}

/**
 * Resolve a {section.key.subkey} style reference into the actual primitive value.
 * This resolves semantic tokens that reference primitive tokens into their final values.
 * Example:
 *   "{color.primary.500}" -> "#328b2c"
 *   "{typography.size.11}" -> 72
 *   "{spacing.4}" -> 12
 */
function resolveSemanticRef(value: unknown, fullTokens: FullTokens): unknown {
  if (typeof value !== "string") return value;

  return value.replace(/\{([^}]+)\}/g, (_, pathStr: string) => {
    const parts = pathStr.split(".");
    if (parts.length < 2) return value;

    // Try to find the referenced value in any section
    let current: unknown = null;

    // First try the exact section name
    const sectionName = parts[0];
    const section = fullTokens?.tokens?.[sectionName];

    if (section) {
      // Navigate through the nested structure
      current = section;
      for (
        let i = 1;
        i < parts.length && current && typeof current === "object";
        i++
      ) {
        if (current && typeof current === "object" && parts[i] in current) {
          current = (current as Record<string, unknown>)[parts[i]];
        } else {
          current = null;
          break;
        }
      }
    }

    // If not found, search all sections for the path
    if (!current) {
      for (const [_sectionName, sectionData] of Object.entries(
        fullTokens?.tokens || {}
      )) {
        if (!sectionData || typeof sectionData !== "object") continue;

        // Try to navigate through this section with the path
        let tempCurrent: unknown = sectionData;
        let pathFound = true;

        for (
          let i = 0;
          i < parts.length && tempCurrent && typeof tempCurrent === "object";
          i++
        ) {
          if (
            tempCurrent &&
            typeof tempCurrent === "object" &&
            parts[i] in tempCurrent
          ) {
            tempCurrent = (tempCurrent as Record<string, unknown>)[parts[i]];
          } else {
            pathFound = false;
            break;
          }
        }

        // If we found a complete path with a value property, use it
        if (
          pathFound &&
          tempCurrent &&
          typeof tempCurrent === "object" &&
          "value" in tempCurrent
        ) {
          current = tempCurrent;
          break;
        }
      }
    }

    // If we found a value property, return it
    if (current && typeof current === "object" && "value" in current) {
      return String((current as { value: unknown }).value);
    }

    return value;
  });
}

/**
 * Append generic fallback fonts to a font family value
 */
function appendFallbackFonts(fontValue: unknown): string | number {
  if (!fontValue || typeof fontValue !== "string") return String(fontValue);

  // Check if it already has fallbacks (contains comma)
  if (fontValue.includes(",")) return fontValue;

  // Don't add fallbacks if it's a reference (contains {path} or var())
  if (fontValue.includes("{") || fontValue.includes("var(")) return fontValue;

  // Determine appropriate fallbacks based on font name
  const font = fontValue.trim();
  const lowerFont = font.toLowerCase();

  if (lowerFont.includes("display") || lowerFont.includes("fliper")) {
    // Display/decorative fonts
    return `${font}, ui-serif, serif`;
  } else if (lowerFont.includes("mono") || lowerFont.includes("code")) {
    // Monospace fonts
    return `${font}, ui-monospace, monospace`;
  } else {
    // Default to sans-serif for most fonts (Inter, etc.)
    return `${font}, ui-sans-serif, system-ui, sans-serif`;
  }
}

/**
 * Convert kebab-case or snake_case to camelCase, with optional suffix stripping
 * Examples:
 *   "line-height" -> "lineHeight"
 *   "corner-radius" -> "cornerRadius"
 *   "Title-1" -> "title1"
 *   "padding-xtra-small" -> "paddingXtraSmall"
 *   "primitives-nilo-uikit" -> "primitives" (if stripSuffixes includes "-nilo-uikit")
 */
function toCamelCase(str: unknown, stripSuffixes: string[] = []): string {
  if (!str || typeof str !== "string") return String(str);

  let processedStr = str;

  // Strip configured suffixes first
  for (const suffix of stripSuffixes) {
    if (processedStr.endsWith(suffix)) {
      processedStr = processedStr.slice(0, -suffix.length);
      break;
    }
  }

  // Then convert to camelCase
  return processedStr
    .toLowerCase()
    .replace(/[-_]([a-z0-9])/g, (_, char: string) => char.toUpperCase());
}

/**
 * Format TypeScript content with Prettier
 */
async function formatWithPrettier(
  content: string,
  filePath: string
): Promise<string> {
  try {
    // Use project's Prettier config if available
    const options = await prettier.resolveConfig(filePath, {
      editorconfig: true,
    });

    // Always ensure we have a parser
    const prettierOptions = { ...options, parser: "typescript" };

    return prettier.format(content, prettierOptions);
  } catch (error) {
    console.warn(
      "⚠️  Prettier formatting failed, using unformatted content:",
      (error as Error).message
    );
    return content;
  }
}
