// config.ts
import path from "node:path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Common configuration for ui-theme-importer scripts
 */
export const config = {
  // Default settings
  prefix: "nilo-",

  // Semantic variable replacements
  semanticVariableReplacements: {
    "bg-": "fill-",
  },

  // TypeScript generation settings
  typescript: {
    // Strip these suffixes from section names when converting to camelCase
    stripSuffixes: ["-nilo-uikit"],
  },

  // Paths
  tokensPath: path.resolve(__dirname, "../tokens/figma-plugin-export.json"),
  outputCssPath: path.resolve(
    __dirname,
    "../../../src/styles/tailwind-theme-from-figma.css"
  ),
  outputTsPath: path.resolve(
    __dirname,
    "../../../src/components/ui-nilo/figma-tokens.ts"
  ),

  // Script paths for documentation
  scriptPaths: {
    main: "apps/ui-theme-importer/src/main.ts",
    figmaToTailwind: "apps/ui-theme-importer/src/convert-tokens-to-tailwind.ts",
    figmaToTypeScript:
      "apps/ui-theme-importer/src/convert-tokens-to-typescript.ts",
    annotate: "apps/ui-theme-importer/src/annotate-tailwind-theme-vars.ts",
    resolve: "apps/ui-theme-importer/src/resolve-css-variables.ts",
    clean: "apps/ui-theme-importer/src/clean-tokens-json.ts",
  },
};
