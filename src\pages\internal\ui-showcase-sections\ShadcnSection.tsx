import React, { useState } from "react";
import {
  Plus,
  Trash2,
  Settings,
  Download,
  Upload,
  Save,
  Edit,
  Eye,
  Heart,
  ArrowRight,
  Home,
  User,
  FileText,
  Bell,
  Search,
  Mail,
  Calendar,
  Star,
  Clock,
  Users,
} from "lucide-react";
import toast from "react-hot-toast";

import { AdvancedSlider } from "@/components/ui-nilo/AdvancedSlider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { NavigationLink } from "@/components/ui-nilo/NavigationLink";
import { Avatar } from "@/components/common/Avatar";
import { ModalDialog } from "@/components/ui-nilo/ModalDialog";
import { TexturePicker } from "@/components/ui-nilo/TexturePicker";
import { ColorPicker } from "@/components/ui-nilo/ColorPicker";
import { Kbd } from "@/components/ui-nilo/Kbd";
import { BasePanel } from "@/components/modals/BasePanel";
import { PanelCollapsible } from "@/components/ui-nilo/PanelCollapsible";
import { ConfirmationModal } from "@/components/ui-nilo/ConfirmationModal";

export default function ShadcnSection() {
  return (
    <div className="w-full max-w-6xl mx-auto space-y-8 relative">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <PopoverSection />
        <TabsSection />
        <ButtonShowcase />
        <SelectDropdownMenuSection />
        <ToggleGroupSection />
        <InputTextareaSection />
        <SwitchSection />
        <NiloComponentsSection />
        <SliderSection />
        <NavigationLinkSection />
      </div>
    </div>
  );
}

const PopoverSection = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isScrollableModalOpen, setIsScrollableModalOpen] = useState(false);
  const [isBasePanelOpen, setIsBasePanelOpen] = useState(false);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);

  const handleConfirmAction = async () => {
    toast.success("Confirmation action completed!");
  };

  const handleCancelAction = () => {
    toast.error("Confirmation cancelled");
  };

  const renderShortContent = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="modal-input">Enter some text</Label>
        <Input id="modal-input" placeholder="Type something here..." />
      </div>
      <div className="space-y-2">
        <Label htmlFor="modal-textarea">Description</Label>
        <Textarea
          id="modal-textarea"
          placeholder="Add a description..."
          rows={3}
        />
      </div>
      <div className="flex items-center space-x-2">
        <Switch id="modal-switch" />
        <Label htmlFor="modal-switch">Enable notifications</Label>
      </div>
    </div>
  );

  const renderFormContent = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="panel-input">Panel Input</Label>
          <Input id="panel-input" placeholder="Enter some settings..." />
        </div>
        <div className="space-y-2">
          <Label htmlFor="panel-textarea">Description</Label>
          <Textarea
            id="panel-textarea"
            placeholder="Add a description..."
            rows={3}
          />
        </div>
        <div className="flex items-center space-x-2">
          <Switch id="panel-switch" />
          <Label htmlFor="panel-switch">Enable feature</Label>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <Label className="text-sm font-medium">Quick Actions</Label>
        <div className="grid grid-cols-2 gap-2">
          <Button variant="outline" size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Advanced
          </Button>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <Label className="text-sm font-medium">Panel State</Label>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">Panel Open</span>
            <Badge variant="secondary">{isBasePanelOpen ? "Yes" : "No"}</Badge>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLongContent = () => (
    <div className="flex flex-col h-full gap-0">
      <div className="flex-1 min-h-0 overflow-auto space-y-6 scrollbar-thin-nilo -mr-3 pt-4">
        {Array.from({ length: 8 }, (_, sectionIndex) => (
          <div key={sectionIndex} className="space-y-4">
            <div className="border-b border-border pb-2">
              <h3 className="text-lg font-semibold">
                Section {sectionIndex + 1}: Component Category
              </h3>
              <p className="text-sm text-muted-foreground">
                This section covers various components and their
                implementations.
              </p>
            </div>

            <div className="space-y-3">
              {Array.from({ length: 6 }, (_, itemIndex) => (
                <div
                  key={itemIndex}
                  className="p-4 border rounded-lg bg-muted/20"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium">Component {itemIndex + 1}</h4>
                    <Badge variant="secondary">
                      v{Math.floor(Math.random() * 5) + 1}.
                      {Math.floor(Math.random() * 10)}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    This is a detailed description of the component
                    functionality, usage patterns, and configuration options. It
                    includes examples of how to implement the component in your
                    application.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">React</Badge>
                    <Badge variant="outline">TypeScript</Badge>
                    <Badge variant="outline">Accessible</Badge>
                    <Badge variant="outline">Customizable</Badge>
                  </div>
                  <div className="mt-3 space-y-2">
                    <div className="flex items-center gap-2">
                      <Switch id={`switch-${sectionIndex}-${itemIndex}`} />
                      <Label
                        htmlFor={`switch-${sectionIndex}-${itemIndex}`}
                        className="text-sm"
                      >
                        Enable advanced features
                      </Label>
                    </div>
                    <Input
                      placeholder={`Enter configuration for component ${itemIndex + 1}...`}
                      className="h-8"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end space-x-2 pt-4 border-t">
        <Button
          variant="secondary"
          onClick={() => setIsScrollableModalOpen(false)}
        >
          Cancel
        </Button>
        <Button onClick={() => setIsScrollableModalOpen(false)}>
          Save Configuration
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <Card>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Modal Dialogs</Label>
              <div className="flex flex-wrap gap-3">
                <Button onClick={() => setIsModalOpen(true)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Open Example Modal Dialog
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => setIsScrollableModalOpen(true)}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Open Scrollable Modal
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsBasePanelOpen(true)}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Open Base Panel
                </Button>
                <Button
                  variant="destructive"
                  onClick={() => setIsConfirmationModalOpen(true)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Open Confirmation Modal
                </Button>
              </div>
            </div>
          </div>

          <ModalDialog
            open={isModalOpen}
            onOpenChange={setIsModalOpen}
            title="Example Modal Dialog"
            description="This is a demonstration of the ModalDialog component with customizable content and footer actions."
          >
            {renderShortContent()}
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="secondary" onClick={() => setIsModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setIsModalOpen(false)}>
                Save Changes
              </Button>
            </div>
          </ModalDialog>

          <ModalDialog
            open={isScrollableModalOpen}
            onOpenChange={setIsScrollableModalOpen}
            title="Component Documentation"
            description="Comprehensive guide to all available UI components and their usage patterns."
            className="max-w-6xl max-h-[calc(100vh-2rem)] h-[600px]"
          >
            {renderLongContent()}
          </ModalDialog>

          {/* ConfirmationModal Example */}
          <ConfirmationModal
            open={isConfirmationModalOpen}
            onOpenChange={setIsConfirmationModalOpen}
            title="Delete Project"
            description="Are you sure you want to delete this project? This action cannot be undone and will permanently remove all project data, including models, textures, and settings."
            confirmText="Delete Project"
            cancelText="Keep Project"
            onConfirm={handleConfirmAction}
            onCancel={handleCancelAction}
            variant="destructive"
          />
        </CardContent>
      </Card>

      {/* BasePanel Example - rendered OUTSIDE the card to avoid stacking context issues */}
      {isBasePanelOpen && (
        <div className="absolute inset-0 pointer-events-none z-50">
          <div className="absolute top-4 left-4 animate-slide-in-from-left">
            <BasePanel
              title="Settings Panel"
              onClose={() => setIsBasePanelOpen(false)}
              className="w-96 max-h-[calc(100vh-2rem)]"
            >
              <div className="space-y-4">
                <PanelCollapsible title="Form Settings" defaultOpen={true}>
                  {renderFormContent()}
                </PanelCollapsible>

                <PanelCollapsible title="Documentation" defaultOpen={false}>
                  {renderLongContent()}
                </PanelCollapsible>
              </div>
            </BasePanel>
          </div>
        </div>
      )}
    </>
  );
};

const NiloComponentsSection = () => {
  const [environmentTexture, setEnvironmentTexture] = useState("");
  const [primaryColor, setPrimaryColor] = useState("#3b82f6");

  return (
    <Card>
      <CardContent className="space-y-6">
        <div className="space-y-6">
          {/* Texture Picker Section */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Texture Picker</Label>
            <div className="space-y-1">
              <TexturePicker
                id="environment-texture"
                value={environmentTexture}
                onChange={setEnvironmentTexture}
                roomId="demo-room"
                type="environment"
                defaultText="Default Environment"
              />

              {environmentTexture && (
                <Badge variant="secondary">
                  Environment: {environmentTexture}
                </Badge>
              )}
            </div>
          </div>

          <Separator />

          {/* Color Picker Section */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Color Picker</Label>
            <div className="space-y-1">
              <ColorPicker
                id="primary-color"
                placeholder="Enter primary color"
                defaultColor="#3b82f6"
                value={primaryColor}
                onChange={setPrimaryColor}
              />
              <Badge
                variant="outline"
                style={{ backgroundColor: primaryColor, color: "white" }}
              >
                Primary: {primaryColor}
              </Badge>
            </div>
          </div>

          <Separator />

          {/* Keyboard Shortcuts Section */}
          <div className="space-y-3 text-nilo-icon-secondary">
            <Label className="text-sm  font-medium">Keyboard Shortcuts</Label>
            <div className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Save Project</span>
                  <div className="flex items-center gap-1">
                    <Kbd>Ctrl</Kbd>
                    <span className="text-xs text-muted-foreground">+</span>
                    <Kbd>S</Kbd>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Redo Action</span>
                  <div className="flex items-center gap-1">
                    <Kbd>Ctrl</Kbd>
                    <span className="text-xs text-muted-foreground">+</span>
                    <Kbd>Shift</Kbd>
                    <span className="text-xs text-muted-foreground">+</span>
                    <Kbd>Z</Kbd>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Toggle Grid</span>
                  <Kbd>G</Kbd>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ButtonShowcase = () => {
  const [selectedVariant, setSelectedVariant] = useState<
    "default" | "secondary" | "tertiary" | "destructive" | "ghost" | "link"
  >("default");
  const [selectedSize, setSelectedSize] = useState<
    "sm" | "default" | "lg" | "icon"
  >("default");
  const [isRounded, setIsRounded] = useState(false);
  const [isDisabled, setIsDisabled] = useState(false);

  const buttonVariants = [
    "default",
    "secondary",
    "tertiary",
    "destructive",
    "ghost",
    // "link",
  ] as const;

  return (
    <Card>
      <CardContent className="space-y-8">
        <div className="space-y-4">
          <div className="flex flex-wrap gap-3">
            {buttonVariants.map((variant) => (
              <Button
                key={variant}
                variant={variant}
                size="sm"
                onClick={() => setSelectedVariant(variant)}
              >
                {variant.charAt(0).toUpperCase() + variant.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex flex-wrap gap-3">
            {buttonVariants.map((variant) => (
              <Button
                key={`disabled-${variant}`}
                variant={variant}
                size="sm"
                disabled
              >
                {variant.charAt(0).toUpperCase() + variant.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        <Separator />

        <div className="flex flex-row flex-wrap gap-6">
          <div className="space-y-3">
            <Label className="text-sm font-medium">Size</Label>
            <ToggleGroup
              type="single"
              value={selectedSize}
              onValueChange={(value: "sm" | "default" | "lg" | "icon") =>
                value && setSelectedSize(value)
              }
            >
              <ToggleGroupItem value="sm" size="sm">
                Small
              </ToggleGroupItem>
              <ToggleGroupItem value="default" size="sm">
                Default
              </ToggleGroupItem>
              <ToggleGroupItem value="lg" size="sm">
                Large
              </ToggleGroupItem>
              {/* <ToggleGroupItem value="icon" size="sm">
                Icon
              </ToggleGroupItem> */}
            </ToggleGroup>
          </div>

          <div className="space-y-3">
            <Label className="text-sm font-medium">Rounding</Label>
            <div className="flex items-center space-x-2">
              <Switch
                id="rounded"
                checked={isRounded}
                onCheckedChange={setIsRounded}
              />
              <Label htmlFor="rounded" className="text-sm">
                Rounded
              </Label>
            </div>
          </div>

          <div className="space-y-3">
            <Label className="text-sm font-medium">State</Label>
            <div className="flex items-center space-x-2">
              <Switch
                id="disabled"
                checked={isDisabled}
                onCheckedChange={setIsDisabled}
              />
              <Label htmlFor="disabled" className="text-sm">
                Disabled
              </Label>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <Label className="text-sm font-medium">Current Selection</Label>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>
              Variant: <Badge variant="secondary">{selectedVariant}</Badge>
            </span>
            <span>
              Size: <Badge variant="secondary">{selectedSize}</Badge>
            </span>
            <span>
              Rounding:{" "}
              <Badge variant="secondary">
                {isRounded ? "Rounded" : "Default"}
              </Badge>
            </span>
            <span>
              State:{" "}
              <Badge variant="secondary">
                {isDisabled ? "Disabled" : "Enabled"}
              </Badge>
            </span>
          </div>
        </div>

        <Separator />

        <div className="space-y-6">
          <div className="space-y-3">
            <div className="flex flex-wrap items-center gap-4">
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                Button
              </Button>
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                Save Changes
              </Button>
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                Delete Account
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex flex-wrap items-center gap-4">
              <Button
                variant={selectedVariant}
                size="icon"
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Plus className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedVariant}
                size="icon"
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedVariant}
                size="icon"
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedVariant}
                size="icon"
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Heart className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex flex-wrap items-center gap-4">
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Plus className="h-4 w-4" />
                Add Item
              </Button>
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Download className="h-4 w-4" />
                Download
              </Button>
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Save className="h-4 w-4" />
                Save Project
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex flex-wrap items-center gap-4">
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                Continue
                <ArrowRight className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                Upload
                <Upload className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                Next
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex flex-wrap items-center gap-4">
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Edit className="h-4 w-4" />
                Edit Profile
                <ArrowRight className="h-4 w-4" />
              </Button>
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <Eye className="h-4 w-4" />
                View Details
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex flex-wrap items-center gap-4">
              <Button
                variant={selectedVariant}
                size={selectedSize}
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Loading...
              </Button>
              <Button
                variant={selectedVariant}
                size="icon"
                disabled={isDisabled}
                className={isRounded ? "rounded-full" : ""}
              >
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const SwitchSection = () => {
  const [switchValue, setSwitchValue] = useState(false);
  return (
    <Card>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="gravity"
                checked={switchValue}
                onCheckedChange={setSwitchValue}
              />
              <Label htmlFor="gravity">Gravity</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="collisions" defaultChecked />
              <Label htmlFor="collisions">Object Collisions</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="shadows" disabled />
              <Label htmlFor="shadows">Shadows (Coming Soon!)</Label>
            </div>
          </div>
          <p className="text-sm text-muted-foreground">
            Gravity:{" "}
            <Badge variant="secondary">{switchValue ? "ON" : "OFF"}</Badge>
          </p>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label>Auto-save</Label>
                  <Switch defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label>Notifications</Label>
                  <Switch />
                </div>
                <div className="flex items-center justify-between">
                  <Label>Dark mode</Label>
                  <Switch />
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label>Premium feature</Label>
                  <Switch disabled />
                </div>
                <div className="flex items-center justify-between">
                  <Label>Beta access</Label>
                  <Switch disabled defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label>Advanced settings</Label>
                  <Switch disabled />
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Switch id="experimental" />
              <div className="space-y-1">
                <Label htmlFor="experimental">Experimental Features</Label>
                <p className="text-sm text-muted-foreground">
                  Enable cutting-edge features that may be unstable
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Switch id="analytics" defaultChecked />
              <div className="space-y-1">
                <Label htmlFor="analytics">Usage Analytics</Label>
                <p className="text-sm text-muted-foreground">
                  Help us improve by sharing anonymous usage data
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const InputTextareaSection = () => {
  const [inputValue, setInputValue] = useState("");
  const [textareaValue, setTextareaValue] = useState("");

  return (
    <Card>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="default-input">Object Name</Label>
              <Input
                id="default-input"
                placeholder="Enter object name..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="disabled-input">Disabled Input</Label>
              <Input
                id="disabled-input"
                placeholder="This field is locked"
                disabled
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="error-input">Error Input</Label>
              <Input
                id="error-input"
                placeholder="Name already exists!"
                // className="border-red-500 focus-visible:ring-red-500"
                aria-invalid={true}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="success-input">Success Input</Label>
              <Input
                id="success-input"
                placeholder="Object saved successfully!"
                // className="border-green-500 focus-visible:ring-green-500"
              />
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="space-y-2 pb-2">
            <Label htmlFor="textarea">Tell us about your creation</Label>
            <Textarea
              id="textarea"
              placeholder="Describe your amazing 3D object..."
              value={textareaValue}
              onChange={(e) => setTextareaValue(e.target.value)}
              rows={4}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const ToggleGroupSection = () => {
  const [toggleValue, setToggleValue] = useState("bold");

  return (
    <Card>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <ToggleGroup
            type="single"
            value={toggleValue}
            onValueChange={setToggleValue}
          >
            <ToggleGroupItem value="bold" aria-label="Toggle bold">
              <span className="font-bold">B</span>
            </ToggleGroupItem>
            <ToggleGroupItem value="italic" aria-label="Toggle italic">
              <span className="italic">I</span>
            </ToggleGroupItem>
            <ToggleGroupItem value="underline" aria-label="Toggle underline">
              <span className="underline">U</span>
            </ToggleGroupItem>
          </ToggleGroup>
          <p className="text-sm text-muted-foreground">
            Selected: <Badge variant="secondary">{toggleValue || "none"}</Badge>
          </p>
        </div>

        <Separator />

        <div className="space-y-4">
          <ToggleGroup type="multiple" aria-label="Text formatting">
            <ToggleGroupItem value="bold" aria-label="Toggle bold">
              <span className="font-bold">Bold</span>
            </ToggleGroupItem>
            <ToggleGroupItem value="italic" aria-label="Toggle italic">
              <span className="italic">Italic</span>
            </ToggleGroupItem>
            <ToggleGroupItem value="underline" aria-label="Toggle underline">
              <span className="underline truncate max-w-[80px]">Underline</span>
            </ToggleGroupItem>
          </ToggleGroup>
          <p className="text-sm text-muted-foreground">
            Note: Long text is truncated with ellipsis to maintain button
            consistency
          </p>
        </div>

        <Separator />

        <div className="space-y-4">
          <ToggleGroup type="multiple" aria-label="Fixed width formatting">
            <ToggleGroupItem
              value="bold"
              aria-label="Toggle bold"
              className="w-20"
            >
              <span className="font-bold">B</span>
            </ToggleGroupItem>
            <ToggleGroupItem
              value="italic"
              aria-label="Toggle italic"
              className="w-20"
            >
              <span className="italic">I</span>
            </ToggleGroupItem>
            <ToggleGroupItem
              value="underline"
              aria-label="Toggle underline"
              className="w-20"
            >
              <span className="underline">U</span>
            </ToggleGroupItem>
          </ToggleGroup>
          <p className="text-sm text-muted-foreground">
            Fixed width buttons ensure consistent sizing regardless of content
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

const TabsSection = () => {
  const [activeTab, setActiveTab] = useState("objects");
  const [objectType, setObjectType] = useState("");
  const [materialType, setMaterialType] = useState("");
  const [lightIntensity, setLightIntensity] = useState([75]);
  const [shadowsEnabled, setShadowsEnabled] = useState(false);
  const [worldName, setWorldName] = useState("");
  const [autoSave, setAutoSave] = useState(true);

  return (
    <Card>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="objects">Objects</TabsTrigger>
              <TabsTrigger value="materials">Materials</TabsTrigger>
              <TabsTrigger value="lights">Lights</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="objects" className="space-y-4">
              <div className="space-y-2">
                <Label>Add New Object</Label>
                <Select value={objectType} onValueChange={setObjectType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose object type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cube">📦 Cube</SelectItem>
                    <SelectItem value="sphere">⚪ Sphere</SelectItem>
                    <SelectItem value="cylinder">🔵 Cylinder</SelectItem>
                    <SelectItem value="pyramid">🔺 Pyramid</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="w-full">Add to World</Button>
                {objectType && (
                  <Badge variant="secondary">Selected: {objectType}</Badge>
                )}
              </div>
            </TabsContent>
            <TabsContent value="materials" className="space-y-4">
              <div className="space-y-2">
                <Label>Material Type</Label>
                <ToggleGroup
                  type="single"
                  value={materialType}
                  onValueChange={(value) => value && setMaterialType(value)}
                  aria-label="Material selection"
                >
                  <ToggleGroupItem value="metal">🔩 Metal</ToggleGroupItem>
                  <ToggleGroupItem value="wood">🪵 Wood</ToggleGroupItem>
                  <ToggleGroupItem value="glass">🪟 Glass</ToggleGroupItem>
                  <ToggleGroupItem value="plastic">🧱 Plastic</ToggleGroupItem>
                </ToggleGroup>
                {materialType && (
                  <Badge variant="secondary">Material: {materialType}</Badge>
                )}
              </div>
            </TabsContent>
            <TabsContent value="lights" className="space-y-4">
              <div className="space-y-2">
                <Label>Light Intensity: {lightIntensity[0]}%</Label>
                <Slider
                  value={lightIntensity}
                  onValueChange={setLightIntensity}
                  max={100}
                  step={1}
                  className="w-full"
                />
                <div className="flex items-center space-x-2">
                  <Switch
                    id="shadows"
                    checked={shadowsEnabled}
                    onCheckedChange={setShadowsEnabled}
                  />
                  <Label htmlFor="shadows">Enable Shadows</Label>
                </div>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary">
                    Intensity: {lightIntensity[0]}%
                  </Badge>
                  {shadowsEnabled && <Badge variant="outline">Shadows</Badge>}
                </div>
              </div>
            </TabsContent>
            <TabsContent value="settings" className="space-y-4">
              <div className="space-y-2">
                <Label>World Name</Label>
                <Input
                  placeholder="My Amazing World"
                  value={worldName}
                  onChange={(e) => setWorldName(e.target.value)}
                />
                <div className="flex items-center space-x-2">
                  <Switch
                    id="auto-save"
                    checked={autoSave}
                    onCheckedChange={setAutoSave}
                  />
                  <Label htmlFor="auto-save">Auto-save</Label>
                </div>
                <div className="flex flex-wrap gap-1">
                  {worldName && (
                    <Badge variant="secondary">Name: {worldName}</Badge>
                  )}
                  {autoSave && <Badge variant="outline">Auto-save</Badge>}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
};

const SliderSection = () => {
  const [sliderValue, setSliderValue] = useState([50]);
  const [steppedSliderValue, setSteppedSliderValue] = useState([50]);
  const [tickedSliderValue, setTickedSliderValue] = useState([50]);
  const [rangeSliderValue, setRangeSliderValue] = useState([25, 75]);

  return (
    <Card>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-6">
            <div className="space-y-2">
              <Label>Continuous Slider: {sliderValue[0]}%</Label>
              <Slider
                value={sliderValue}
                onValueChange={setSliderValue}
                max={100}
                step={1}
                className="w-full my-4"
              />
            </div>
            <div className="space-y-2">
              <Label>
                Stepped Slider (25% increments): {steppedSliderValue[0]}%
              </Label>
              <Slider
                value={steppedSliderValue}
                onValueChange={setSteppedSliderValue}
                max={100}
                step={25}
                className="w-full my-4"
              />
            </div>
            <div className="space-y-2">
              <Label>
                Stepped Slider (10% increments, ticks): {tickedSliderValue[0]}%
              </Label>
              <Slider
                value={tickedSliderValue}
                onValueChange={setTickedSliderValue}
                max={100}
                step={10}
                ticks={true}
                className="w-full my-4"
              />
            </div>
            <div className="space-y-2">
              <Label>
                Range Slider: {rangeSliderValue[0]}% - {rangeSliderValue[1]}%
              </Label>
              <Slider
                value={rangeSliderValue}
                onValueChange={setRangeSliderValue}
                max={100}
                step={0.01}
                className="w-full my-4"
              />
            </div>
          </div>
        </div>

        <Separator />

        <AdvancedSliderDemo />
      </CardContent>
    </Card>
  );
};

const SelectDropdownMenuSection = () => {
  const [selectValue, setSelectValue] = useState("");
  const [dropdownValue, setDropdownValue] = useState("");
  const [materialValue, setMaterialValue] = useState("");
  const [sizeValue, setSizeValue] = useState("");
  const [animationValue, setAnimationValue] = useState("");
  const [viewOptions, setViewOptions] = useState({
    showGrid: true,
    showCoordinates: false,
    showAxes: true,
  });
  const [objectProperties, setObjectProperties] = useState({
    visible: true,
    interactive: false,
    glowing: true,
  });
  const [effects, setEffects] = useState({
    rainEffect: false,
    snowEffect: true,
    windEffect: false,
  });

  return (
    <Card>
      <CardContent className="space-y-8">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Label>Object Type</Label>
              <Select value={selectValue} onValueChange={setSelectValue}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose object type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cube">📦 Cube</SelectItem>
                  <SelectItem value="sphere">⚪ Sphere</SelectItem>
                  <SelectItem value="cylinder">🔵 Cylinder</SelectItem>
                  <SelectItem value="pyramid">🔺 Pyramid</SelectItem>
                  <SelectItem value="torus">🍩 Torus</SelectItem>
                </SelectContent>
              </Select>
              <Badge variant="secondary">
                Selected: {selectValue || "none"}
              </Badge>
            </div>

            <div className="space-y-4">
              <Label>Material</Label>
              <Select value={materialValue} onValueChange={setMaterialValue}>
                <SelectTrigger>
                  <SelectValue placeholder="Select material" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="metal">🔩 Metal</SelectItem>
                  <SelectItem value="wood">🪵 Wood</SelectItem>
                  <SelectItem value="glass">🪟 Glass</SelectItem>
                  <SelectItem value="plastic">🧱 Plastic</SelectItem>
                  <SelectItem value="stone">🪨 Stone</SelectItem>
                </SelectContent>
              </Select>
              <Badge variant="secondary">
                Material: {materialValue || "none"}
              </Badge>
            </div>

            <div className="space-y-4">
              <Label>Size Preset</Label>
              <Select value={sizeValue} onValueChange={setSizeValue}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tiny">Tiny (0.5x)</SelectItem>
                  <SelectItem value="small">Small (1x)</SelectItem>
                  <SelectItem value="medium">Medium (2x)</SelectItem>
                  <SelectItem value="large">Large (5x)</SelectItem>
                  <SelectItem value="huge">Huge (10x)</SelectItem>
                </SelectContent>
              </Select>
              <Badge variant="secondary">Size: {sizeValue || "none"}</Badge>
            </div>

            <div className="space-y-4">
              <Label>Animation Type</Label>
              <Select value={animationValue} onValueChange={setAnimationValue}>
                <SelectTrigger>
                  <SelectValue placeholder="Select animation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rotate">🔄 Rotate</SelectItem>
                  <SelectItem value="bounce">⚡ Bounce</SelectItem>
                  <SelectItem value="float">🦋 Float</SelectItem>
                  <SelectItem value="pulse">💓 Pulse</SelectItem>
                  <SelectItem value="none">❌ None</SelectItem>
                </SelectContent>
              </Select>
              <Badge variant="secondary">
                Animation: {animationValue || "none"}
              </Badge>
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Label>World Actions</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    Actions Menu
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuLabel>World Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>💾 Save World</DropdownMenuItem>
                  <DropdownMenuItem>📤 Export World</DropdownMenuItem>
                  <DropdownMenuItem>🔗 Share World</DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>🔄 Reset World</DropdownMenuItem>
                  <DropdownMenuItem className="text-danger-danger-500">
                    🗑️ Delete World
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="space-y-4">
              <Label>View Options</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    View Settings
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuCheckboxItem
                    checked={viewOptions.showGrid}
                    onCheckedChange={(checked) =>
                      setViewOptions((prev) => ({ ...prev, showGrid: checked }))
                    }
                  >
                    📐 Show Grid
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={viewOptions.showCoordinates}
                    onCheckedChange={(checked) =>
                      setViewOptions((prev) => ({
                        ...prev,
                        showCoordinates: checked,
                      }))
                    }
                  >
                    📍 Show Coordinates
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={viewOptions.showAxes}
                    onCheckedChange={(checked) =>
                      setViewOptions((prev) => ({ ...prev, showAxes: checked }))
                    }
                  >
                    🎯 Show Axes
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={dropdownValue}
                    onValueChange={setDropdownValue}
                  >
                    <DropdownMenuLabel>Camera View</DropdownMenuLabel>
                    <DropdownMenuRadioItem value="perspective">
                      👁️ Perspective
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="orthographic">
                      📐 Orthographic
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="top-down">
                      ⬇️ Top Down
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>
              <div className="space-y-2">
                <Badge variant="secondary">
                  Camera: {dropdownValue || "none"}
                </Badge>
                <div className="flex flex-wrap gap-1">
                  {viewOptions.showGrid && (
                    <Badge variant="outline">Grid</Badge>
                  )}
                  {viewOptions.showCoordinates && (
                    <Badge variant="outline">Coordinates</Badge>
                  )}
                  {viewOptions.showAxes && (
                    <Badge variant="outline">Axes</Badge>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <Label>Object Properties</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    Properties
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuCheckboxItem
                    checked={objectProperties.visible}
                    onCheckedChange={(checked) =>
                      setObjectProperties((prev) => ({
                        ...prev,
                        visible: checked,
                      }))
                    }
                  >
                    🎭 Visible
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={objectProperties.interactive}
                    onCheckedChange={(checked) =>
                      setObjectProperties((prev) => ({
                        ...prev,
                        interactive: checked,
                      }))
                    }
                  >
                    🎯 Interactive
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={objectProperties.glowing}
                    onCheckedChange={(checked) =>
                      setObjectProperties((prev) => ({
                        ...prev,
                        glowing: checked,
                      }))
                    }
                  >
                    🌟 Glowing
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>📏 Edit Size</DropdownMenuItem>
                  <DropdownMenuItem>🎨 Change Color</DropdownMenuItem>
                  <DropdownMenuItem>📝 Rename</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <div className="flex flex-wrap gap-1">
                {objectProperties.visible && (
                  <Badge variant="outline">Visible</Badge>
                )}
                {objectProperties.interactive && (
                  <Badge variant="outline">Interactive</Badge>
                )}
                {objectProperties.glowing && (
                  <Badge variant="outline">Glowing</Badge>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <Label>Effects Menu</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-start">
                    Effects
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuCheckboxItem
                    checked={effects.rainEffect}
                    onCheckedChange={(checked) =>
                      setEffects((prev) => ({ ...prev, rainEffect: checked }))
                    }
                  >
                    🌧️ Rain Effect
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={effects.snowEffect}
                    onCheckedChange={(checked) =>
                      setEffects((prev) => ({ ...prev, snowEffect: checked }))
                    }
                  >
                    ❄️ Snow Effect
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuCheckboxItem
                    checked={effects.windEffect}
                    onCheckedChange={(checked) =>
                      setEffects((prev) => ({ ...prev, windEffect: checked }))
                    }
                  >
                    💨 Wind Effect
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>✨ Add Particles</DropdownMenuItem>
                  <DropdownMenuItem>💡 Add Light</DropdownMenuItem>
                  <DropdownMenuItem>🔊 Add Sound</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <div className="flex flex-wrap gap-1">
                {effects.rainEffect && <Badge variant="outline">Rain</Badge>}
                {effects.snowEffect && <Badge variant="outline">Snow</Badge>}
                {effects.windEffect && <Badge variant="outline">Wind</Badge>}
              </div>
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Label>Disabled Select</Label>
              <Select disabled>
                <SelectTrigger>
                  <SelectValue placeholder="This is disabled" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="option1">Option 1</SelectItem>
                  <SelectItem value="option2">Option 2</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-4">
              <Label>Disabled Dropdown</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    disabled
                    className="w-full justify-start"
                  >
                    Disabled Menu
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuItem disabled>Disabled Action</DropdownMenuItem>
                  <DropdownMenuItem>Enabled Action</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const NavigationLinkSection = () => {
  return (
    <Card>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Navigation Links</h3>

          {/* Basic Navigation Links with Icons */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Basic Navigation Links
            </Label>
            <div className="space-y-2">
              <NavigationLink icon={<Home className="h-4 w-4" />}>
                Home
              </NavigationLink>
              <NavigationLink icon={<User className="h-4 w-4" />}>
                Profile
              </NavigationLink>
              <NavigationLink icon={<FileText className="h-4 w-4" />}>
                Documentation
              </NavigationLink>
              <NavigationLink icon={<Bell className="h-4 w-4" />}>
                Notifications
              </NavigationLink>
            </div>
          </div>

          <Separator />

          {/* Navigation Links with Helper Text */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">With Helper Text</Label>
            <div className="space-y-2">
              <NavigationLink
                icon={<Search className="h-4 w-4" />}
                helperText="Search across all content"
              >
                Search
              </NavigationLink>
              <NavigationLink
                icon={<Mail className="h-4 w-4" />}
                helperText="3 new messages"
              >
                Messages
              </NavigationLink>
              <NavigationLink
                icon={<Calendar className="h-4 w-4" />}
                helperText="Next event in 2 hours"
              >
                Calendar
              </NavigationLink>
            </div>
          </div>

          <Separator />

          {/* Navigation Links with Avatars */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">With Avatars</Label>
            <div className="space-y-2">
              <NavigationLink
                icon={
                  <Avatar
                    src="https://api.dicebear.com/9.x/shapes/svg?seed=John"
                    alt="John Doe"
                    className="w-8 h-8 rounded-full"
                  />
                }
                helperText="Online now"
              >
                John Doe
              </NavigationLink>
              <NavigationLink
                icon={
                  <Avatar
                    src="https://api.dicebear.com/9.x/shapes/svg?seed=Jane"
                    alt="Jane Smith"
                    className="w-8 h-8 rounded-full"
                  />
                }
                helperText="Last seen 2 hours ago"
              >
                Jane Smith
              </NavigationLink>
              <NavigationLink
                icon={
                  <Avatar
                    src="https://api.dicebear.com/9.x/shapes/svg?seed=Bob"
                    alt="Bob Johnson"
                    className="w-8 h-8 rounded-full"
                  />
                }
                helperText="Typing..."
              >
                Bob Johnson
              </NavigationLink>
            </div>
          </div>

          <Separator />

          {/* Navigation Links with Different States */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Different States</Label>
            <div className="space-y-2">
              <NavigationLink
                icon={<Star className="h-4 w-4" />}
                className="bg-yellow-500/10 text-yellow-600 hover:bg-yellow-500/20"
              >
                Favorites
              </NavigationLink>
              <NavigationLink
                icon={<Clock className="h-4 w-4" />}
                className="bg-blue-500/10 text-blue-600 hover:bg-blue-500/20"
              >
                Recent
              </NavigationLink>
              <NavigationLink
                icon={<Users className="h-4 w-4" />}
                className="bg-green-500/10 text-green-600 hover:bg-green-500/20"
              >
                Team
              </NavigationLink>
            </div>
          </div>

          <Separator />

          {/* Disabled Navigation Links */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Disabled States</Label>
            <div className="space-y-2">
              <NavigationLink icon={<Settings className="h-4 w-4" />} disabled>
                Settings (Coming Soon)
              </NavigationLink>
              <NavigationLink
                icon={<Download className="h-4 w-4" />}
                disabled
                helperText="Feature not available"
              >
                Downloads
              </NavigationLink>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

function AdvancedSliderDemo() {
  const [xValue, setXValue] = React.useState([99]);
  const [yValue, setYValue] = React.useState([75]);
  const [zValue, setZValue] = React.useState([50]);

  return (
    <div className="w-full max-w-2xl mx-auto p-nilo-6 space-y-nilo-6 bg-nilo-fill-tertiary rounded-nilo-lg">
      {/* X Slider */}
      <AdvancedSlider
        label="X"
        value={xValue}
        onValueChange={setXValue}
        min={0}
        max={100}
        step={10}
        ticks={true}
        showTickValues={true}
        showNumericInput={true}
      />

      {/* Y Slider */}
      <AdvancedSlider
        label="Y"
        value={yValue}
        onValueChange={setYValue}
        min={0}
        max={100}
        step={5}
        ticks={true}
        showTickValues={true}
        showNumericInput={true}
      />

      {/* Z Slider */}
      <AdvancedSlider
        label="Z"
        value={zValue}
        onValueChange={setZValue}
        min={0}
        max={100}
        step={10}
        ticks={true}
        showTickValues={true}
        showNumericInput={true}
      />

      {/* Custom range slider */}
      <AdvancedSlider
        label="Custom Range"
        defaultValue={[25, 75]}
        min={0}
        max={200}
        step={25}
        ticks={true}
        showTickValues={true}
        showNumericInput={true}
      />

      {/* Minimal slider */}
      <AdvancedSlider
        label="Minimal"
        defaultValue={[60]}
        min={0}
        max={100}
        step={1}
        ticks={false}
        showTickValues={false}
        showNumericInput={false}
      />
    </div>
  );
}
