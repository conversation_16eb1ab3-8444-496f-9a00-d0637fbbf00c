// using a bunch of european regions, mostly from tier 1 pricing
// for reference: https://firebase.google.com/docs/functions/locations and https://cloud.google.com/run/pricing?hl=en#tiers
const FUNCTIONS_REGIONS = [
  "europe-north1" as const,
  "europe-central2" as const,
  "europe-southwest1" as const,
  "europe-west1" as const,
  "europe-west4" as const,
  "europe-west8" as const,
  "europe-west9" as const,
];

type FunctionsRegion = (typeof FUNCTIONS_REGIONS)[number];

type DatabaseLocation = "eur3" | FunctionsRegion;

/**
 * Sanitizes the isolated environment name:
 * - only lowercase letters, numbers and dashes are allowed
 * - length is limited to 32 characters
 * - has to start with a letter
 * - has to end with a letter or a number
 * @param name - The name to sanitize.
 * @returns The sanitized name.
 */
export function sanitizeIsolatedEnvironmentName(name: string) {
  // make sure it is lowercase
  name = name.toLowerCase();
  // replace all non-alphanumeric characters with a dash
  name = name.replace(/[^a-z0-9]+/g, "-");
  // remove invalid leading and trailing characters
  name = name.replace(/^[^a-z]+|-+$/g, "");
  if (name.length > 32) {
    // make sure it is at most 32 characters
    name = name.slice(0, 32);
    // remove trailing dashes after trimming
    name = name.replace(/-+$/g, "");
  }
  if (name.length < 4) {
    throw new Error("Isolated environment name is too short");
  }
  return name;
}

export type IsolatedEnvironmentComponentNames = {
  name: string;
  bucket: string;
  database: string | undefined;
  databaseLocation: DatabaseLocation;
  functions: string;
  functionsRegion: FunctionsRegion;
  fleet: string;
};

export function getComponentNames(
  name?: string,
  useHumanReadableDatabaseName: boolean = false
): IsolatedEnvironmentComponentNames {
  const sanitized = sanitizeIsolatedEnvironmentName(name ?? "main");
  if (sanitized === "main" && name !== undefined && name !== "main") {
    throw new Error(
      "Isolated environment name is not 'main' but it is sanitized to 'main'"
    );
  }
  const functionsRegion = getFunctionsRegion(sanitized);
  return {
    name: sanitized,
    bucket:
      sanitized === "main"
        ? "nilo-technologies.firebasestorage.app"
        : `nilo-technologies-${sanitized}`,
    database:
      sanitized === "main"
        ? useHumanReadableDatabaseName
          ? "(default)"
          : undefined
        : sanitized,
    databaseLocation:
      sanitized === "main" || sanitized === "prod" ? "eur3" : functionsRegion,
    // function group name behave weirdly if it contains dashes, so we remove them
    functions: sanitized.replace(/-/g, ""),
    functionsRegion,
    fleet: `${sanitized}-fleet`,
  };
}

export function getComponentNamesFromDatabaseName(
  databaseName: string | undefined | null
): IsolatedEnvironmentComponentNames {
  if (
    databaseName === undefined ||
    databaseName === null ||
    databaseName === "(default)"
  ) {
    return getComponentNames("main");
  }
  return getComponentNames(databaseName);
}

export function getStorageBucketFromDatabaseName(
  databaseName: string | undefined | null
) {
  return getComponentNamesFromDatabaseName(databaseName).bucket;
}

export function isIsolatedEnvironmentStorageBucket(bucket: string): boolean {
  return (
    bucket.startsWith("nilo-technologies-") ||
    bucket === "nilo-technologies.firebasestorage.app"
  );
}

export function getFunctionsRegion(name: string): FunctionsRegion {
  return FUNCTIONS_REGIONS[
    hashStringTo32BitInteger(name) % FUNCTIONS_REGIONS.length
  ];
}

/**
 * Hashes a string to a 32-bit integer.
 * @returns A 32-bit integer.
 */
function hashStringTo32BitInteger(name: string): number {
  // cryptographic hash functions are unavailable in the browser
  // all we need is determinism, so we'll use a simple, non-cryptographic hash function
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    const char = name.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash |= 0; // convert to 32-bit signed integer
  }

  return Math.abs(hash);
}
