import {
  FieldValue,
  QueryDocumentSnapshot,
  UpdateData,
} from "firebase-admin/firestore";
import {
  DbCollections,
  DbCompoundModelTask,
  PrimitiveShapeSpec,
  CompoundModelTaskStatus,
} from "@nilo/firebase-schema";
import { onDocumentCreated } from "../utils";
import { Logger } from "../../logger";
import { captureEvent } from "../../posthog";
import { generateCompoundModelWithLLM } from "./primitiveModelGenerator";
import { CompoundModelContext, CompoundModelListener } from "./types";

class Listener implements CompoundModelListener {
  public finalUpdate: UpdateData<DbCompoundModelTask> | null = null;
  public costUsd: number | undefined = undefined;

  constructor(
    private taskDocumentSnapshot: QueryDocumentSnapshot<
      DbCompoundModelTask,
      DbCompoundModelTask
    >
  ) {}

  async onStarted(): Promise<void> {
    const update: UpdateData<DbCompoundModelTask> = {
      status: "streaming",
      requestedAt: FieldValue.serverTimestamp(),
      shapes: [], // Initialize empty array for arrayUnion operations
      completedShapes: 0, // Initialize counter for increment operations
    };
    await this.taskDocumentSnapshot.ref.update(update);
  }

  async onShapeGenerated(shape: PrimitiveShapeSpec): Promise<void> {
    // Use arrayUnion to append shape without read-modify-write cycle
    // This eliminates the O(n²) performance bottleneck!
    const update: UpdateData<DbCompoundModelTask> = {
      shapes: FieldValue.arrayUnion(shape),
      completedShapes: FieldValue.increment(1),
    };
    await this.taskDocumentSnapshot.ref.update(update);
  }

  async onProgress(progress: {
    status: CompoundModelTaskStatus;
    progress: number;
    completedShapes: number;
    totalShapes: number;
  }): Promise<void> {
    const update: UpdateData<DbCompoundModelTask> = {
      status: progress.status,
      progress: progress.progress,
      completedShapes: progress.completedShapes,
      totalShapes: progress.totalShapes,
    };
    await this.taskDocumentSnapshot.ref.update(update);
  }

  async onFinished(
    result:
      | {
          status: "success";
          modelTitle?: string;
          modelDescription?: string;
          generationTimeMs?: number;
          service?: { provider: string; model: string };
          costUsd?: number;
        }
      | {
          status: "failed" | "cancelled";
          message: string;
          suggestion: string;
          generationTimeMs?: number;
          service?: { provider: string; model: string };
        }
  ): Promise<void> {
    const baseUpdate: UpdateData<DbCompoundModelTask> = {
      status: result.status,
      finishedAt: FieldValue.serverTimestamp(),
      progress: result.status === "success" ? 100 : undefined,
      generationTimeMs: result.generationTimeMs,
    };

    if (result.status === "success") {
      this.finalUpdate = {
        ...baseUpdate,
        modelTitle: result.modelTitle,
        modelDescription: result.modelDescription,
      };
      this.costUsd = result.costUsd;
    } else {
      this.finalUpdate = {
        ...baseUpdate,
        message: result.message,
        suggestion: result.suggestion,
      };
    }
  }
}

export const onCompoundModelRequest = onDocumentCreated(
  `${DbCollections.compoundModelTasks}/{id}`,
  async (event) => {
    const functionStartedAtMs = performance.now();
    const taskId = event.params.id;
    const logger = Logger.create({ taskId });
    const taskDocumentSnapshot = event.data as
      | QueryDocumentSnapshot<DbCompoundModelTask, DbCompoundModelTask>
      | undefined;

    if (!taskDocumentSnapshot) {
      logger.warn("❌ No task found", taskId);
      return;
    }

    if (taskDocumentSnapshot.data()?.createdAt === undefined) {
      logger.debug("🚀 Task createdAt is undefined - setting now", taskId);
      await taskDocumentSnapshot.ref.update({
        createdAt: FieldValue.serverTimestamp(),
      });
    }

    const {
      success: taskParseSuccess,
      data: task,
      error: taskParseError,
    } = DbCompoundModelTask.safeParse(taskDocumentSnapshot.data());

    if (!taskParseSuccess) {
      logger.error("❌ Invalid task data", taskId, taskParseError);

      // Extract meaningful error message from Zod validation error
      let errorMessage = "Invalid request data";
      let suggestion = "Please check your input parameters and try again";

      if (taskParseError?.issues && Array.isArray(taskParseError.issues)) {
        const issue = taskParseError.issues[0]; // Get first validation issue
        if (issue) {
          const fieldPath = issue.path.join(".");
          errorMessage = `Invalid ${fieldPath}: ${issue.message}`;

          // Provide specific suggestions based on the validation error
          if (issue.code === "invalid_enum_value") {
            const options = issue.options
              ? issue.options.join(", ")
              : "valid options";
            suggestion = `Please use one of these valid options: ${options}`;
          } else if (issue.code === "invalid_type") {
            suggestion = `Expected ${issue.expected}, but received ${issue.received}`;
          }
        }
      }

      // Update document to failed status with validation error details
      await taskDocumentSnapshot.ref.update({
        status: "failed",
        message: errorMessage,
        suggestion: suggestion,
        finishedAt: FieldValue.serverTimestamp(),
      } as UpdateData<DbCompoundModelTask>);

      return;
    }

    logger.debug("🚀 New Compound Model Request", taskId, task);

    let finalUpdate: UpdateData<DbCompoundModelTask> = {
      status: "failed",
      message: "Internal error processing request",
      suggestion: "Try again later",
    };
    let costUsd: number | undefined = undefined;

    try {
      const context: CompoundModelContext = {
        task,
        functionStartedAtMs,
        logger,
      };
      const listener = new Listener(taskDocumentSnapshot);

      await listener.onStarted();
      await generateCompoundModelWithLLM(context, listener);

      if (listener.finalUpdate) {
        finalUpdate = listener.finalUpdate;
      }
      costUsd = listener.costUsd;
    } catch (error) {
      logger.error("❌ Error processing shape generation request", error);
      finalUpdate = {
        status: "failed",
        message: "Error processing request",
        suggestion: "Try again later",
        finishedAt: FieldValue.serverTimestamp(),
      };
    }

    await taskDocumentSnapshot.ref.update(finalUpdate);

    // emit metrics event
    let errorMessage = undefined;
    if (
      finalUpdate.status === "failed" &&
      typeof finalUpdate.message === "string"
    ) {
      errorMessage = finalUpdate.message;
    }
    captureEvent(task.userId, "ai_generation", {
      generation_type: "text_to_compound_model",
      world_id: task.worldId,
      generation_duration_ms: performance.now() - functionStartedAtMs,
      success: finalUpdate.status === "success",
      error_type: errorMessage,
      cost_usd: costUsd,
      model_provider: task.taskBody.service?.provider ?? "llm",
      model_name: task.taskBody.service?.model ?? "llm",
    });

    logger.debug("🚀 Shape generation task finished", { finalUpdate });
  }
);
