import {
  EyeIcon as EyeIconOutline,
  HeartIcon as HeartIconOutline,
  PlusCircleIcon as PlusCircleIconOutline,
  StarIcon as StarIconOutline,
} from "@heroicons/react/24/outline";
import {
  BoltIcon as BoltIconSolid,
  FireIcon as FireIconSolid,
  HeartIcon as HeartIconSolid,
  StarIcon as StarIconSolid,
  UserIcon as UserIconSolid,
} from "@heroicons/react/24/solid";
import type { ComponentType, MouseEvent } from "react";
import { useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { Link } from "react-router-dom";

import { WorldThumbnailMoreMenu } from "./WorldThumbnailMoreMenu";
import {
  getDefaultWorldThumbnailOptions,
  WorldThumbnailOptions,
} from "./WorldThumbnailOptions";
import { WorldThumbnailSkeleton } from "./WorldThumbnailSkeleton";
import { Avatar } from "@/components/common/Avatar";
import { auth } from "@/config/firebase";
import {
  useStreamDocument,
  useStreamDocumentById,
} from "@/hooks/firebaseHooks";
import { useUserProfileData } from "@/hooks/useUserProfileData";
import { useWorldOnlineCount } from "@/hooks/useWorldOnlineCount";
import { useWorldPreviewScreenshotSD } from "@/hooks/useWorldPreviewScreenshotSD";
import { useWorldSessionCount } from "@/hooks/useWorldSessionCount";
import { setWorldLiked, setWorldSaved } from "@/platform/actions/world-actions";
import { useNiloPlatformNavigate } from "@/platform/hook/useNiloPlatformNavigate";
import {
  worldsCollection,
  worldScoresCollection,
  worldUserRelationDoc,
} from "@/utils/firestoreCollections";

const configuration = {
  popularEyecatchViewsThreshold: 999,
  onlineEyecatchThreshold: 1,
  onlyAllowSavingMyWorlds: true,
};

export const WorldThumbnail = ({
  worldId,
  options: userOptions,
}: {
  worldId: string;
  options?: Partial<WorldThumbnailOptions>;
}) => {
  const [user] = useAuthState(auth);
  const myUserId = user?.uid;

  if (!myUserId) {
    return <WorldThumbnailSkeleton />;
  }

  return (
    <WorldThumbnailAuthenticated
      worldId={worldId}
      myUserId={myUserId}
      options={userOptions}
    />
  );
};

const WorldThumbnailAuthenticated = ({
  worldId,
  myUserId,
  options: userOptions,
}: {
  worldId: string;
  myUserId: string;
  options?: Partial<WorldThumbnailOptions>;
}) => {
  const {
    navigateToWorld,
    navigateToCreateRemixedWorld: navigateToRemixWorld,
  } = useNiloPlatformNavigate();

  const [isModalOpen, setIsModalOpen] = useState(false);

  const options = {
    ...getDefaultWorldThumbnailOptions(),
    ...userOptions,
  };

  // Use the custom hook for preview image
  const { imgUrl, loading: imgLoading } = useWorldPreviewScreenshotSD(worldId);

  const [imgLoaded, setImgLoaded] = useState(false);

  const handleThumbnailClick = (midClick: boolean) => {
    if (midClick) {
      console.debug(
        "For now middle-mouse click does the same as left-mouse click"
      );
    }

    if (options.thumbnailClickAction === "infoModal") {
      setIsModalOpen(true);
    } else if (options.thumbnailClickAction === "visit") {
      navigateToWorld(worldId);
    } else if (options.thumbnailClickAction === "remix") {
      navigateToRemixWorld(worldId, { inNewTab: true });
    } else {
      throw new Error(
        `Invalid thumbnail click action: ${options.thumbnailClickAction}`
      );
    }
  };

  return (
    <>
      <div
        className={`group relative ${options.thumbnailAspectRatio} rounded-2xl overflow-hidden hover:shadow-lg transition-shadow duration-200`}
      >
        <button
          onClick={() => {
            handleThumbnailClick(false);
          }}
          onMouseDown={(e) => {
            if (e.button === 1) {
              handleThumbnailClick(true);
            }
          }}
          className="absolute inset-0 w-full h-full cursor-pointer"
        >
          {imgUrl ? (
            <img
              src={imgUrl}
              alt={"World Thumbnail"}
              onLoad={() => setImgLoaded(true)}
              className={`w-full h-full object-cover transition-opacity duration-700 ${imgLoaded ? "opacity-100" : "opacity-20"} group-hover:brightness-120 group-hover:saturate-150 ${imgLoading ? "opacity-50" : ""}`}
            />
          ) : (
            <div className="w-full h-full bg-zinc-900 animate-pulse" />
          )}

          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
        </button>

        <WorldThumbnailOverlay
          worldId={worldId}
          myUserId={myUserId}
          options={options}
        />
      </div>

      {isModalOpen && <div>Info modal not implemented yet</div>}

      {/* <WorldInfoModal
        worldId={worldId}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      /> */}
    </>
  );
};

const WorldThumbnailOverlay = ({
  worldId,
  myUserId,
  options,
}: {
  worldId: string;
  myUserId: string;
  options: WorldThumbnailOptions;
}) => {
  const worldData = useStreamDocumentById(
    worldsCollection,
    worldId
  ).value?.data();
  const scoreData = useStreamDocumentById(
    worldScoresCollection,
    worldId
  ).value?.data();

  const isLikedByMeDoc = useStreamDocument(
    worldUserRelationDoc({ kind: "like", worldId, userId: myUserId })
  ).value;
  const isLikedByMe = !!isLikedByMeDoc?.exists();

  const isSavedByMeDoc = useStreamDocument(
    worldUserRelationDoc({ kind: "save", worldId, userId: myUserId })
  ).value;
  const isSavedByMe = !!isSavedByMeDoc?.exists();

  // Get owner profile data
  const { data: ownerProfile } = useUserProfileData(worldData?.ownerId);

  const { count: sessionCount = 0, loading: sessionCountLoading } =
    useWorldSessionCount(worldId);
  const { count: onlineCount = 0, loading: onlineCountLoading } =
    useWorldOnlineCount(worldId);

  // Dummy/fallback data for missing backend info
  // These can be replaced with real data when available
  const dummyRemixes = Math.floor(Math.random() * 10); // 0-9

  const handleLikeClick = async (e: MouseEvent) => {
    //// Make sure thumb-click doesn't also trigger
    e.preventDefault();
    e.stopPropagation();

    try {
      await setWorldLiked(worldId, myUserId, !isLikedByMe);
    } catch (error) {
      console.error("💔 Error toggling like:", error);
    }
  };

  const handleSaveClick = async (e: MouseEvent) => {
    //// Make sure thumb-click doesn't also trigger
    e.preventDefault();
    e.stopPropagation();

    try {
      await setWorldSaved(worldId, myUserId, !isSavedByMe);
    } catch (error) {
      console.error("💔 Error toggling save:", error);
    }
  };

  if (!worldData || !scoreData) {
    return null;
  }

  const showFeaturedEyecatch =
    options.renderFeaturedEyecatch && worldData.isFeatured;

  const showViewsEyecatch =
    options.renderViewsEyecatch &&
    !worldData.isFeatured &&
    sessionCount >= configuration.popularEyecatchViewsThreshold;

  const showOnlineEyecatch =
    options.renderOnlineEyecatch &&
    onlineCount >= configuration.onlineEyecatchThreshold;

  const canSave =
    !configuration.onlyAllowSavingMyWorlds || worldData.ownerId === myUserId;

  return (
    <>
      <div className="absolute top-2 left-2 flex items-center space-x-2 z-10">
        {showFeaturedEyecatch && (
          <LabelEyeCatch
            icon={BoltIconSolid}
            label="Featured"
            color="text-white"
            // bgColor="bg-nilo-green"
            bgColor="bg-gradient-to-r from-purple-600 to-pink-600"
          />
        )}
        {showViewsEyecatch && (
          <LabelEyeCatch
            icon={FireIconSolid}
            label="Popular!"
            color="text-black"
            bgColor="bg-yellow-300"
          />
        )}
        {showOnlineEyecatch && (
          <OnlineEyeCatch count={onlineCount} loading={onlineCountLoading} />
        )}
      </div>

      <div className="absolute inset-0 flex flex-row h-full w-full p-4 pointer-events-none">
        <div className="flex flex-col justify-end flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            {options.renderCreatorAboveTitle && worldData.ownerId && (
              <Link
                to={`/user/${worldData.ownerId}`}
                className="pointer-events-auto hover:opacity-80 transition-opacity flex items-center space-x-2"
                onClick={(e) => e.stopPropagation()}
              >
                <Avatar
                  src={ownerProfile?.avatarUrl}
                  alt={ownerProfile?.displayName ?? worldData.ownerId}
                  className="w-4 h-4 rounded-full"
                />
                <span className="text-white/80 text-xs">
                  {ownerProfile?.displayName ??
                    ownerProfile?.username ??
                    worldData.ownerId.substring(0, 8) + "..."}
                </span>
              </Link>
            )}
          </div>
          <div className="flex items-center">
            {options.renderCreatorByTitle && worldData.ownerId && (
              <Link
                to={`/user/${worldData.ownerId}`}
                className="pointer-events-auto hover:opacity-80 transition-opacity mr-2"
                onClick={(e) => e.stopPropagation()}
              >
                <Avatar
                  src={ownerProfile?.avatarUrl}
                  alt={ownerProfile?.displayName ?? worldData.ownerId}
                  className="w-4 h-4 rounded-full"
                />
              </Link>
            )}
            <h2 className="text-white font-semibold text-lg truncate drop-shadow mr-4">
              {worldData.displayName ?? worldData.name}
            </h2>
          </div>
        </div>
        <div
          className={`flex ${options.horizontalStatsLayout ? "flex-row items-end space-x-3" : "flex-col justify-end"} items-end gap-3`}
        >
          {options.renderCreatorStat && worldData.ownerId && (
            <Link
              to={`/user/${worldData.ownerId}`}
              className="pointer-events-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <Stat
                imgUrl={ownerProfile?.avatarUrl}
                label={
                  ownerProfile?.displayName ??
                  ownerProfile?.username ??
                  worldData.ownerId.substring(0, 8) + "..."
                }
              />
            </Link>
          )}
          {options.renderOnlineStat && (
            <Stat
              icon={UserIconSolid}
              label={onlineCountLoading ? "..." : onlineCount}
              hidden={
                (onlineCount ?? 0) < configuration.onlineEyecatchThreshold &&
                !onlineCountLoading
              }
              iconColor="text-green-400"
              labelColor="text-green-300"
            />
          )}
          {options.renderViewsStat && (
            <Stat
              icon={EyeIconOutline}
              label={
                sessionCountLoading
                  ? "..."
                  : sessionCount == null
                    ? "•"
                    : sessionCount
              }
              hidden={
                (sessionCount ?? 0) <
                  configuration.popularEyecatchViewsThreshold &&
                !sessionCountLoading
              }
            />
          )}
          {options.renderLikesStat && (
            <Stat
              icon={isLikedByMe ? HeartIconSolid : HeartIconOutline}
              label={scoreData.likes < 1 ? "" : scoreData.likes}
              onTap={handleLikeClick}
              iconColor={isLikedByMe ? "text-red-500" : "text-white"}
            />
          )}
          {options.renderSavesStat && canSave && (
            <Stat
              icon={isSavedByMe ? StarIconSolid : StarIconOutline}
              onTap={handleSaveClick}
              label={isSavedByMe ? "Saved" : ""}
              iconColor={isSavedByMe ? "text-yellow-400" : "text-white"}
            />
          )}
          {options.renderRemixesStat && (
            <Stat
              icon={PlusCircleIconOutline}
              label={dummyRemixes < 1 ? "-" : dummyRemixes}
              onTap={() => alert("this is where you'd remix the world")}
            />
          )}
        </div>
      </div>

      {myUserId && (
        <div className="absolute top-2 right-2 z-10 opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-opacity pointer-events-auto md:pointer-events-none md:group-hover:pointer-events-auto">
          <WorldThumbnailMoreMenu
            userId={myUserId}
            worldId={worldId}
            isOwner={worldData?.ownerId === myUserId}
            worldData={worldData}
            ownerProfile={ownerProfile}
          />
        </div>
      )}
    </>
  );
};

const OnlineEyeCatch = ({
  count,
  loading,
}: {
  count: number | undefined;
  loading?: boolean;
}) => {
  if (loading) return null;
  return (
    <div className="flex items-center space-x-2 bg-black/60 rounded-full px-2 py-1">
      <span className="w-3 h-3 rounded-full bg-green-400 animate-pulse block" />
      <span className="text-green-300 text-xs font-semibold drop-shadow">
        {count} online
      </span>
    </div>
  );
};

const LabelEyeCatch = ({
  icon: Icon,
  label,
  color = "text-white",
  bgColor = "bg-black/60",
}: {
  icon: ComponentType<{ className?: string }>;
  label: string;
  color?: string;
  bgColor?: string;
}) => (
  <div
    className={`flex items-center space-x-2 ${bgColor} rounded-full px-2 py-1`}
  >
    <Icon className={`${color} w-3 h-3`} />
    <span className={`${color} text-xs font-bold drop-shadow`}>{label}</span>
  </div>
);

const Stat = ({
  icon: Icon,
  imgUrl,
  label,
  hidden,
  iconColor = "text-white",
  labelColor = "text-white",
  onTap,
}: {
  icon?: ComponentType<{ className?: string }>;
  imgUrl?: string;
  label?: string | number;
  hidden?: boolean;
  iconColor?: string;
  labelColor?: string;
  onTap?: (e: MouseEvent) => void;
}) => {
  if (hidden) return null;

  const content = (
    <div
      className={`
        flex flex-col items-center w-5 pointer-events-auto
        hover:opacity-80 transition-opacity
        ${onTap ? "cursor-pointer" : ""}`}
    >
      {Icon !== undefined && (
        <Icon className={`${iconColor} w-5 h-5 drop-shadow mx-auto`} />
      )}
      {imgUrl !== undefined && (
        <Avatar
          src={imgUrl}
          alt={imgUrl}
          className="w-5 h-5 rounded-full drop-shadow mx-auto"
        />
      )}
      {label !== undefined && (
        <span
          className={`${labelColor} text-xs font-bold drop-shadow mt-1 truncate max-w-[60px]`}
        >
          {label === 0 ? "0" : label === null ? "\u00A0" : label}
        </span>
      )}
    </div>
  );

  if (onTap) {
    return (
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onTap(e);
        }}
      >
        {content}
      </button>
    );
  }

  return content;
};
