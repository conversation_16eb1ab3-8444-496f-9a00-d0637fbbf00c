import { test, expect } from "@playwright/test";
import { enterAndLogin } from "./utils";

test("create a new world", async ({ page }) => {
  // Handle authentication flow
  await enterAndLogin(page);

  // Now we can safely look for the Create New World button
  await page.getByRole("button", { name: "Create New World" }).click();

  // Make sure we ended up on the world page, which the URL should be
  // play/<world-id>
  await expect(page).toHaveURL(/.*\/play\/.*/, { timeout: 20000 });

  // Wait for the world data to load and the Publish button to be visible
  // new world is private by default, so Publish button should appear
  await expect(page.getByRole("button", { name: "Publish" })).toBeVisible({
    timeout: 10000,
  });

  // Publish the world should make the Unpublish button visible
  await page.getByRole("button", { name: "Publish" }).click();
  await page.getByRole("button", { name: "Publish World" }).click();
  await expect(page.getByRole("button", { name: "Unpublish" })).toBeVisible({
    timeout: 10000,
  });

  // Go back to the home page and make sure the world is visible
  // Click the logo button to open the dropdown menu
  await page.getByRole("button", { name: "Home (logo)" }).click();

  // Wait for the dropdown menu to be visible before clicking on the link
  const exploreItem = page.getByRole("menuitem", { name: "Explore Worlds" });
  await expect(exploreItem).toBeVisible();

  // Click on the Explore Worlds link in the dropdown menu
  await exploreItem.click();

  // Once we click on Explore Worlds, we should be redirected to / page (which shows explore content)
  await expect(page).toHaveURL("/", { timeout: 20000 });

  console.info("✅ New world creation test completed successfully");
});
