import { Object3D, ConeGeometry, Mesh, MeshBasicMaterial } from "three";
import { nanoid } from "nanoid/non-secure";
import {
  setObjectNotRaycastable,
  setObjectRaycastable,
} from "../util/RaycastUtils";
import {
  DEFAULT_PARTICLE_SETTINGS,
  ParticleSystem,
  ParticleSystemSettings,
} from "../particles/ParticleSystem";
import { Client } from "../client/Client";
import { LiveblocksEntity } from "./NetworkEntity";
import { EntityType, ParticleEntityData } from "@/liveblocks.config";
import { EntityId, makeUniqueKey } from "@nilo/ecs";

export class ParticleEntity extends LiveblocksEntity {
  public override readonly type = "ParticleEntity";
  protected override rootNode: Object3D;
  private _gizmoObject: Object3D;

  public static override readonly UNIQUE_ID = makeUniqueKey("ParticleEntity");

  private _particleSystem: ParticleSystem;

  private _settings: ParticleSystemSettings;

  public get settings() {
    return {
      ...this._settings,
    };
  }

  constructor(
    id: EntityId | null = null,
    settings: ParticleSystemSettings = {
      ...DEFAULT_PARTICLE_SETTINGS,
      visible: true,
      lifetime: { min: 1, max: 3 },
      count: 64,
      scale: { min: 0.1, max: 0.3 },
      region: {
        x: { min: -1, max: 1 },
        y: { min: -1, max: 1 },
        z: { min: -1, max: 1 },
      },
    }
  ) {
    const isNew = id === null;
    super(true, id, true);
    this._settings = settings;
    this.rootNode = new Object3D();
    this.setRootNode(this.rootNode);

    // Initialize particles
    this._particleSystem = new ParticleSystem(this.settings);

    // Create points system
    this.rootNode.add(this._particleSystem);
    this._gizmoObject = new Object3D();
    this.initGizmo();

    if (isNew) {
      this.showGizmo();
    } else {
      this.hideGizmo();
    }
  }

  getMesh() {
    return this.particleSystem;
  }

  setSelected(_value: boolean) {}

  setHovered(_value: boolean) {}

  public initGizmo() {
    //Create cone
    if (this._gizmoObject.children.length >= 1) {
      return;
    }
    const geometry = new ConeGeometry(0.25, 0.75, 10);
    const material = new MeshBasicMaterial({ color: 0x00cccc, opacity: 1.0 });
    const cone = new Mesh(geometry, material);
    cone.rotateZ(-Math.PI / 2);
    this._gizmoObject.add(cone);
    this.rootNode.add(this._gizmoObject);
    setObjectRaycastable(cone);
    this._gizmoObject.name = this.id + "_gizmo";
  }

  public showGizmo() {
    this._gizmoObject.visible = true;
    setObjectRaycastable(this._gizmoObject.children[0]);
  }

  public hideGizmo() {
    this._gizmoObject.visible = false;
    setObjectNotRaycastable(this._gizmoObject.children[0]);
  }

  public override update(time: number, deltaTime: number) {
    super.update(time, deltaTime);
    this._particleSystem.update(deltaTime);
  }

  public override toJSON(): ParticleEntityData {
    return {
      ...super.toJSON(),
      type: EntityType.ParticleEntity,
      settings: this.settings,
    };
  }

  public override fromJSON(json: ParticleEntityData) {
    if (json.id === "") {
      console.error("🚨 ParticleEntity: No id provided from json data");
      json = {
        ...json,
        id: nanoid(),
      };
    }
    super.fromJSON(json);

    if (json.options) {
      //backwards compatibility
      this._settings = json.options as ParticleSystemSettings;
    }

    if (json.settings) {
      this._settings = json.settings;
    }
    this._particleSystem.updateSettings(this.settings);
  }

  public getSettings() {
    return this._settings;
  }

  public updateSettings(settings: Partial<ParticleSystemSettings>) {
    this._settings = { ...this._settings, ...settings };
    this._particleSystem.updateSettings(settings);
    if (Client.world.isAlive(this.id)) {
      Client.markEntityAsDirty(this);
    }
  }

  public get particleSystem() {
    return this._particleSystem;
  }

  public override getRootNode() {
    return this.rootNode;
  }
}
