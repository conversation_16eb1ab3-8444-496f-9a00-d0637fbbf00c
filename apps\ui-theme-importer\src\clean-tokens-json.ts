// clean-tokens-json.ts
import fs from "node:fs";

/**
 * Clean up Figma tokens JSON by removing scope and $extensions properties
 * @param {string} inputPath - Path to input figma-variables.json (will be overwritten)
 */
export function cleanTokensJson(inputPath: string): void {
  assertExists(inputPath, "Input tokens file not found.");

  const raw = fs.readFileSync(inputPath, "utf-8");
  const tokens = JSON.parse(raw);

  // Clean the tokens recursively
  const cleanedTokens = cleanTokenObject(tokens);

  // Write cleaned tokens back to the same file
  fs.writeFileSync(inputPath, JSON.stringify(cleanedTokens, null, 2), "utf-8");
  console.debug(`🧹 Cleaned tokens JSON written to ${inputPath}`);
}

/**
 * Recursively clean a token object by removing scope and $extensions properties
 */
function cleanTokenObject(obj: unknown): unknown {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(cleanTokenObject);
  }

  const cleaned: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(obj as Record<string, unknown>)) {
    // Skip scope and $extensions properties
    if (key === "scope" || key === "$extensions") {
      continue;
    }

    // Recursively clean nested objects
    if (value !== null && typeof value === "object") {
      cleaned[key] = cleanTokenObject(value);
    } else {
      cleaned[key] = value;
    }
  }

  return cleaned;
}

/**
 * Helper functions
 */

function assertExists(p: string, message: string): void {
  if (!fs.existsSync(p)) {
    console.error(message, p);
    process.exit(1);
  }
}
