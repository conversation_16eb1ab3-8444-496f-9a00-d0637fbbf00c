import { describe, test, expect } from "@jest/globals";
import { Struct, f32, text } from "@evenstar/byteform";
import { NetworkComponent } from "../src/index";

describe("NetworkComponent", () => {
  interface TestData {
    value: number;
    name: string;
  }

  const testSchema = new Struct({
    value: f32,
    name: text,
  });

  describe("constructor", () => {
    test("creates NetworkComponent with schema", () => {
      const toData = (data: TestData) => data;
      const fromData = (data: TestData) => data;

      const component = new NetworkComponent(
        "test-component",
        toData,
        fromData,
        testSchema
      );

      expect(component.meta.name).toBe("test-component");
      expect(component.toData).toBe(toData);
      expect(component.fromData).toBe(fromData);
      expect(component.schema).toBe(testSchema);
    });
  });

  describe("toData and fromData methods", () => {
    test("round-trip conversion preserves data", () => {
      const originalData = { value: 42, name: "test-object" };

      const toData = (data: TestData) => ({
        value: data.value,
        name: data.name,
      });
      const fromData = (data: TestData) => ({
        value: data.value,
        name: data.name,
      });

      const component = new NetworkComponent("roundtrip", toData, fromData);

      const serialized = component.toData(originalData);
      const deserialized = component.fromData(serialized);

      expect(deserialized).toStrictEqual(originalData);
    });
  });

  describe("SelfData helper method", () => {
    test("identity transformations preserve data exactly", () => {
      const component = NetworkComponent.SelfData<TestData>("identity");
      const testData: TestData = { value: 3.2, name: "test name" };

      const serialized = component.toData(testData);
      expect(serialized).toBe(testData); // Should be the exact same reference

      const deserialized = component.fromData(serialized);
      expect(deserialized).toBe(serialized); // Should be the exact same reference
    });

    test("handles complex data structures", () => {
      interface ComplexData {
        id: number;
        metadata: {
          name: string;
          tags: string[];
        };
        active: boolean;
      }

      const component = NetworkComponent.SelfData<ComplexData>("complex");
      const testData: ComplexData = {
        id: 123,
        metadata: {
          name: "test-entity",
          tags: ["important", "networked"],
        },
        active: true,
      };

      const serialized = component.toData(testData);
      const deserialized = component.fromData(serialized);

      expect(deserialized).toBe(testData); // Identity transformation
    });
  });
});
