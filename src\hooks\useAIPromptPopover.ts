import { useState, useEffect, useCallback, useRef } from "react";
import { KeyboardMappingIDs } from "@/config/keyboardMapping";
import { Client } from "@/core/client";

interface CursorPosition {
  x: number;
  y: number;
}

export function useAIPromptPopover() {
  const [isOpen, setIsOpen] = useState(false);
  const [cursorPosition, setCursorPosition] = useState<CursorPosition | null>(
    null
  );
  const containerRef = useRef<HTMLElement | null>(null);
  const lastCursorPositionRef = useRef<CursorPosition | null>(null);

  // Track mouse position - but only when not over popover UI elements
  const handleMouseMove = useCallback(
    (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Don't update cursor position if mouse is over UI elements
      const isOverUIElement =
        target.closest('[role="dialog"]') ||
        target.closest("[data-radix-portal]") ||
        target.closest(".ui-popover") ||
        target.tagName === "SELECT" ||
        target.tagName === "OPTION" ||
        target.tagName === "BUTTON" ||
        target.closest("button") ||
        target.closest("select");

      if (!isOverUIElement) {
        // Store cursor position in ref to avoid re-renders
        lastCursorPositionRef.current = { x: event.clientX, y: event.clientY };

        // Only update state if popover is open (for real-time positioning)
        if (isOpen) {
          setCursorPosition(lastCursorPositionRef.current);
        }
      }
    },
    [isOpen]
  );

  // Handle keyboard shortcut
  const handleKeyDown = useCallback(() => {
    // Only trigger if we're in the canvas container or it has focus
    const activeElement = document.activeElement as HTMLElement;

    // More robust canvas detection - check for ThreeCanvas container class or tabindex
    const isCanvasContainer = (element: HTMLElement) => {
      return (
        element.classList.contains("_container_1pwv7_1") ||
        element.getAttribute("tabindex") === "-1" ||
        element.closest('[class*="container"]') !== null
      );
    };

    const isInCanvas =
      containerRef.current?.contains(activeElement) ||
      activeElement === containerRef.current ||
      document.activeElement === containerRef.current ||
      isCanvasContainer(activeElement);

    if (isInCanvas) {
      setIsOpen(true);
    }
  }, []);

  useEffect(() => {
    // Add keyboard shortcut listener
    const cleanup = Client.keyboard.registerAction(
      KeyboardMappingIDs.OPEN_AI_PROMPT_POPOVER,
      handleKeyDown
    );

    // Add mouse move listener
    document.addEventListener("mousemove", handleMouseMove);

    return () => {
      cleanup();
      document.removeEventListener("mousemove", handleMouseMove);
    };
  }, [handleKeyDown, handleMouseMove]);

  const openPopover = useCallback((position?: CursorPosition) => {
    if (position) {
      setCursorPosition(position);
    } else if (lastCursorPositionRef.current) {
      // Use the last stored cursor position if no specific position provided
      setCursorPosition(lastCursorPositionRef.current);
    }
    setIsOpen(true);
  }, []);

  const closePopover = useCallback(() => {
    setIsOpen(false);
  }, []);

  return {
    isOpen,
    cursorPosition,
    containerRef,
    openPopover,
    closePopover,
    setIsOpen,
  };
}
