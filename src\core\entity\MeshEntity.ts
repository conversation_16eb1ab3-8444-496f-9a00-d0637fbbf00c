import {
  Object3D,
  <PERSON>sh,
  Box3,
  Vector3,
  Color,
  Group,
  SkinnedMesh,
  Vector2,
  Quaternion,
  Euler,
  BoxGeometry,
  LineSegments,
  BufferGeometry,
  LineDashedMaterial,
  CircleGeometry,
  MeshBasicMaterial,
  DoubleSide,
} from "three";
import toast from "react-hot-toast";
import { SkeletonUtils } from "three/examples/jsm/Addons";
import { addDoc, serverTimestamp } from "firebase/firestore";
import isEqual from "fast-deep-equal/es6";
import { BoxLineObject } from "../util/BoxLineObject";
import {
  LAYERS,
  setObjectLayer,
  setObjectNotRaycastable,
  setObjectRaycastable,
} from "../util/RaycastUtils";
import { EntityHierarchyController } from "../util/hierarchy/EntityHierarchyController";
import { mergeObjectsDeep, simpleDeepClone } from "../util/JsObjectUtils";
import { EntityAnimator, PlayableAnimation } from "../animator/EntityAnimator";
import TransformControls from "../util/controls/TransformControls";
import { isMobile } from "../util/UserAgent";
import { ImportedModel } from "../util/import";
import { ModelGenerator } from "../util/modelGeneration/ModelGenerator";
import {
  CHARACTER_BEHAVIOUR_ID,
  CharacterController,
  CharacterControllerComponent,
  CharacterJSON,
} from "../game-systems/components/character/Character";
import {
  AnimationComponent,
  AnimatorComponent,
  TriggerComponent,
  HealthComponent,
  TriggerAreaDamageComponent,
  RequestRespawnComponent,
  RespawnPointTag,
  RagdollComponent,
} from "../components";
import { assertNotNull, assertTrue } from "../util/Assert";
import { EntitySelectionMode } from "../util/EntitySelectionMode";
import { runCommands } from "../command";
import SetEntityPositionCommand from "../command/commands/setEntityPosition";
import { ModelDataComponent } from "../ecs/behaviors/mesh";
import { PhysicsUserDataComponent } from "../ecs/behaviors/physics";
import { EntityMaterialController } from "./common/EntityMaterialController";
import {
  dereferenceForDetailAndUrl,
  FindOrCreateModelByUrlAndDetailOptions,
} from "./meshEntity/MeshEntityGeometryDetailLoader";
import { EntityDetailController } from "./common/EntityDetailController";
import {
  assignUrlToMesh,
  LoadedMeshData,
  meshEntityModelLoad,
  MeshEntityModelLoadOptions,
  setLoadedModelByUrl,
} from "./meshEntity/MeshEntityModelLoader";
import {
  LoadModelProperties,
  loadModelPropertiesEquals,
  LoadModelPropertiesPending,
} from "./meshEntity/LoadModelProperties";
import { physicsSetDragging } from "./common/physicsSetDragging";
import { getOriginOffset } from "./common/getEntityOriginOffset";
import { localColor, UserEntity } from "./UserEntity";
import { dashedMaterial, vDown } from "./PrimitiveEntity";
import { LiveblocksEntity } from "./NetworkEntity";
import { EntityOriginTypes, GameEntity } from "./Entity";
import {
  AnimationData,
  EntityType,
  ImagePromptData,
  MeshEntityData,
  ModelData,
} from "@/liveblocks.config";
import { Client } from "@/core/client";
import { EmissiveFresnelShader } from "@/core/shader";
import { globalDelegates } from "@/core/client/globalDelegates";
import { PhysicsUserData } from "@/types";
import { mergeMeshes } from "@/utils/mesh/MeshesMerge";
import { setPhysicsStaticFlag } from "@/core/util/setPhysicsStaticFlag";
// import { DelayedTasksController } from "@/utils/DelayedTasksController";
import { createDelegateFunction } from "@nilo/utilities";
import { urlParams } from "@/utils/urlParams";
import { EntityId, makeUniqueKey } from "@nilo/ecs";
import { ModelGenerationProviders } from "@nilo/firebase-schema";
import { assetsCollection } from "@/utils/firestoreCollections";
import { auth } from "@/config/firebase";
import { removePhysicsFromEntity } from "@/physics/PhysicsSimulation";
import { addPhysicsBodyInfoToEntity } from "@/physics/helpers/addPhysicsBodyInfoToEntity";
import { ModelGenerationTaskError } from "@/utils/api-tasks/runModelGenerationTask";
import { calculatePhysicsBodyOffsetMatrix } from "@/physics/helpers/calculatePhysicsBodyOffsetMatrix";
/**
 * Generative / Imported mesh entity
 * TODO: split into client and server parts when we switch to c/s (post proto)
 * TODO: note we'll probably only have a single entity class once component system implemented
 */

export const GENERATION_BOX_NAME = "GENERATION_BOX";
export const GENERATION_OUTLINE_NAME = "GENERATION_OUTLINE";

const box = new Box3();
const boxSize = new Vector3();
const pos = new Vector3();
const vLeft = new Vector3(1, 0, 0);
export async function initializeModelMesh(mesh: Mesh) {
  const morphTargetDictionary = Object.keys(mesh.morphTargetDictionary || {});

  let isMorphValid = true;
  for (const key of morphTargetDictionary) {
    /**
     * Morph key should be something like: position, normal, color, etc.
     * But in broken rooms, sometimes keys like "0, 1, 2" can appear.
     * Maybe related to the mergeMeshes function.
     * TODO: Investigate and fix the issue to support broken morph targets.
     */
    if (!isNaN(+key)) {
      isMorphValid = false;
      break;
    }
  }

  if (!isMorphValid) {
    const morphAttributes = Object.keys(mesh.geometry.morphAttributes);

    for (const key of morphAttributes) {
      delete mesh.geometry.morphAttributes[key];
    }

    mesh.morphTargetInfluences = undefined;
    mesh.morphTargetDictionary = undefined;
    mesh.updateMorphTargets();
  }

  // Optional: modify mesh position and scale here
  //mesh.scale.set(1, 1, 1);
  mesh.position.x = 0;
  mesh.position.y = 0; //0.5;

  setObjectRaycastable(mesh);
  mesh.renderOrder = 1;

  mesh.castShadow = true;
  mesh.receiveShadow = true;

  // Calculate the scale to make geometry fit the bbox mesh
  box.setFromObject(mesh, true);
  box.getSize(boxSize);

  //wait for one frame to take load of main thread.
  await new Promise((resolve) => setTimeout(resolve, 1));

  if (urlParams.temporaryScale) {
    mesh.scale.set(1, 1, 1);
    const scaleFactor = 1 / Math.max(boxSize.x, boxSize.y, boxSize.z);
    mesh.scale.setScalar(scaleFactor);
  }

  mesh.geometry.computeBoundingBox();
  // mesh.geometry.center();

  //wait for one frame to take load of main thread.
  await new Promise((resolve) => setTimeout(resolve, 1));

  //mesh.geometry.center();
  // Move mesh to the ground
  const min = mesh.geometry.boundingBox!.min.clone();
  mesh.updateMatrixWorld(true); // Ensure matrixWorld is up to date
  min.applyMatrix4(mesh.matrix); // Apply the mesh's local matrix to the min point
  // mesh.position.y -= min.y; // Move the mesh down by the min point's y value
}

const modelGenerator = new ModelGenerator();

export const generateProgressSteps = [
  {
    message: "Preparing to generate...",
    name: "prepare-generate",
    progressEnd: 5,
    expectedTime: 5,
  },
  {
    name: "generating",
    progressEnd: 90,
    message: "Generating...",
    expectedTime: 30,
  },
  {
    name: "loading",
    progressEnd: 100,
    message: "Loading...",
    expectedTime: 10,
  },
];

export const rigProgressSteps = [
  {
    name: "rigging",
    progressEnd: 95,
    message: "Rigging...",
    expectedTime: 30,
  },
  {
    name: "finishing",
    progressEnd: 100,
    message: "Finishing...",
    expectedTime: 5,
  },
];

export class MeshEntity extends LiveblocksEntity {
  private originType = EntityOriginTypes.NativeOrigin;
  // private sizeInMb?: number;
  // private importedModel?: Group;
  // three.js local scenegraph
  private root: Object3D = new Object3D();
  private _currentMesh: Mesh = new Mesh();
  private dragMarker: LineSegments<BufferGeometry, LineDashedMaterial> | null =
    null;
  /** Indicates when model finished loading. Set by */
  public modelLoaded: boolean = false;
  private _loadModelPropertiesCompleted: LoadModelProperties | null = null;

  public static override readonly UNIQUE_ID = makeUniqueKey("MeshEntity");

  private boundingBoxMaterial: EmissiveFresnelShader | undefined;
  private taskId = "";
  private boundingBox = new Group();
  private size: number = 1.0;

  private _modelLoadingProgress: number = 0;

  private originalMeshTaskId = "";

  // entity type
  public override type = EntityType.MeshEntity as const;
  // entity name
  public name: string | undefined;
  // mesh detail level
  public readonly detailController = new EntityDetailController(this);

  // public _imagePrompt: string = "";
  // public _imageType: string = "";
  public _prompt: string = "";
  //this variable will store the id of the user who gave it this prompt
  private _modelData: ModelData | null = null;
  // mesh (bounding box) color
  public _color: number = 0xffffff;
  // children
  public readonly hierarchyController: EntityHierarchyController;

  public readonly materialController = new EntityMaterialController(this);

  public physicsUserData: PhysicsUserData | undefined;

  public readonly extraChildren: Object3D[] = [];

  public containsSkinnedMesh: boolean = false;

  public override readonly delegates = {
    ...(this as GameEntity).delegates,
    onModelLoaded: createDelegateFunction(),
    onDeleted: createDelegateFunction(),
  };
  private _jsonAnimation: AnimationData | undefined;

  // constructor
  constructor(id: EntityId | null = null) {
    super(true, id, true); // persistent
    this.boundingBox.position.set(0, 0.475, 0);
    this.setRootNode(this.root);
    this.root.add(this._currentMesh);
    this.root.name = "root_" + this.id;

    setObjectRaycastable(this._currentMesh);
    //@ts-expect-error we assign entity to Object3D
    this._currentMesh.entity = this;

    this.hierarchyController = new EntityHierarchyController(this);
  }

  public override onRoomJoin() {
    super.onRoomJoin();

    assertTrue(
      Client.world.isAlive(this.id),
      "onRoomJoin assumes entity is alive"
    );

    // If no model is stored, generate from the set prompt
    if (!this._modelData && this._prompt) {
      this.generateFromPrompt();
    }

    // NOTE: when liveblocks/client-server sync is capable of syncing components separately, rather than having to resort to this "mega data object" approach this won't be needed
    if (this._jsonAnimation) {
      Client.world.addComponent(
        this.id,
        AnimationComponent,
        new PlayableAnimation(this._jsonAnimation)
      );
    }
  }

  public getMesh() {
    return this._currentMesh;
  }

  setOriginType(type: EntityOriginTypes, _dirty = true) {
    if (this.containsSkinnedMesh) {
      return;
    }
    const prevOffset = this.getOffset();

    const prevPosition = new Vector3();
    this.getSimulatedPosition(prevPosition);
    this.updateOriginType(type);

    if (this.originType === type) return;
    this.originType = type;

    const newOffset = this.getOffset();

    if (this.isInWorld && _dirty) {
      this.setPosition(
        prevPosition.clone().add(prevOffset).sub(newOffset),
        false
      );
      Client.markEntityAsDirty(this, false);
    }

    const bodyControl = this.getPhysicsBodyControl();
    bodyControl?.updatePhysicsBodyOffsetMatrix(
      calculatePhysicsBodyOffsetMatrix(this).toArray()
    );
  }

  updateOriginType(type: EntityOriginTypes) {
    const mesh = this.getMesh();
    if (!mesh) return;
    mesh.position.copy(getOriginOffset(mesh, type));
    mesh.updateMatrix();
    mesh.updateMatrixWorld();
  }

  getOriginType() {
    return this.originType;
  }

  // Creates a new animator based on the provided mesh
  private createAnimator(skinnedMesh: SkinnedMesh): EntityAnimator {
    const skeleton = skinnedMesh.skeleton;
    const isTripo = skeleton.bones[0].name === "Root";

    //remove entity mesh because we will be adding another one in entity animator
    if (Client.physics) {
      removePhysicsFromEntity(this);
    } else {
      Client.events.once("physics-started", () => {
        removePhysicsFromEntity(this);
      });
    }

    const animator = new EntityAnimator(
      skinnedMesh,
      isTripo ? "tripo" : "mixamo"
    );

    if (isTripo) {
      // Small hack to match the convexHull with the
      // skeleton changes inside EntityAnimator for tripo rigs
      // With ECS this flow should be improved
      this.getMesh()?.rotateY(-Math.PI / 2);
    }

    const ragdoll = animator.getRagdoll();
    if (ragdoll) {
      this.data().addComponent(RagdollComponent, ragdoll);
    }

    this.delegates.onDeleted.once(() => {
      this.data().getComponent(AnimatorComponent)?.dispose();
      this.data().removeComponent(AnimatorComponent);
      this.containsSkinnedMesh = false;
    });

    return animator;
  }

  public getCharacterModel(): Object3D | undefined {
    return this.extraChildren[0];
  }

  private _isRigging: boolean = false;
  public isRigging() {
    return this._isRigging;
  }

  public setRigging(value: boolean) {
    this._isRigging = value;
  }

  // prompt used to generate mesh (if generator)

  public setPrompt(value: string, _dirty = true) {
    if (this._prompt !== value) {
      this._prompt = value;

      if (this.isInWorld && _dirty) Client.markEntityAsDirty(this, false);
    }
  }

  public getPrompt() {
    return this._prompt;
  }

  // id of the user who gave who set prompt

  public setTaskId(taskId: string, _dirty = true) {
    if (taskId && this.taskId !== taskId) {
      this.taskId = taskId;
      const onProgress = (progress: number, timeLeftMs: number) => {
        if (Client.getEntity(this.id) === this) {
          this._progress.setProgress({ progress, timeLeftMs });
        }
      };
      //TODO: Maybe need to merge trackGeneration and trackRig into track
      if (this.originalMeshTaskId) {
        const entityWasStatic = this.getPhysicsUserData()?.physics?.isStatic;
        const position = new Vector3();
        this.getPosition(position);
        this.setRigging(true);
        this._progress.setProgressSteps(rigProgressSteps);
        this._progress.startStep("rigging");
        this._progress.setIsGenerating(true);
        this.setOriginType(EntityOriginTypes.GeometryBottomCenter);
        (async () => {
          try {
            const modelData = await modelGenerator.trackGeneration({
              taskId,
              onProgress,
            });
            console.debug("🎉 Rigging model completed", modelData);
            if (!isEqual(modelData, this._modelData)) {
              this._progress.startStep("finishing");
              this.setModelData(modelData, true);

              this.delegates.onModelLoaded.once(() => {
                this.setOrientation(
                  new Quaternion().setFromEuler(new Euler(0, -Math.PI / 2, 0))
                );
                position.y -= this.getEntitySize().clone().y / 2; //Maintain position. RiggedMesh will have the pivot point at the bottom
                this.setPosition(position, true);
              });
              console.debug("🎉 Rigged model loaded");
            }
          } catch (err) {
            if (err instanceof ModelGenerationTaskError) {
              const message = `Error rigging model\n${err.message}\n${err.suggestion}`;
              console.warn(message);
              toast.error(message);
            } else {
              console.error("🔥 Error rigging model:", err);
            }
            this._progress.fail();
            if (!entityWasStatic) {
              setPhysicsStaticFlag(this, false);
            }
          } finally {
            if (Client.userEntity.getSelectedEntities().includes(this)) {
              //Reselect entity to update selection box
              Client.userEntity.selectEntities(
                [this],
                EntitySelectionMode.REPLACE,
                true
              );
            }
            this._progress.complete();
            this.setRigging(false);
          }
        })();
      } else {
        this._progress.setProgressSteps(generateProgressSteps);
        this._progress.startStep("generating");
        this._progress.setIsGenerating(true);
        (async () => {
          try {
            const modelData = await modelGenerator.trackGeneration({
              taskId,
              onProgress,
            });
            console.debug("🐛 Model generation completed", this.id, modelData);
            this._progress.startStep("loading");
            if (Client.getEntity(this.id) === this) {
              this.setModelData(modelData);
              Client.markEntityAsDirty(this, false, false); //Need to update always. this is required due to Authority
              this.delegates.onModelLoaded.once(() => {
                addPhysicsBodyInfoToEntity(this);
                Client.events.emit("entity-created", this);
              });
            }
          } catch (err) {
            console.warn("Error generating model", err);
            this.onModelGenerationFailed();
          } finally {
            this._progress.complete();
          }
        })();
      }
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
  }

  public setOriginalMeshTaskId(taskId: string, _dirty = true) {
    if (this.originalMeshTaskId !== taskId) {
      this.originalMeshTaskId = taskId;
      if (_dirty) Client.markEntityAsDirty(this, false);
    }
  }

  // modelData
  public setModelData(modelData: ModelData | null, _dirty = true) {
    if (modelData && !isEqual(this._modelData, modelData)) {
      this._modelData = modelData;
      if (_dirty) {
        Client.markEntityAsDirty(this, false);
      }

      this.data().addComponent(ModelDataComponent, modelData);
      if (_dirty) {
        this.delegates.onModelLoaded.once(() => {
          if (!this.getPhysicsUserData()?.isUserImported) {
            const box = new Box3().setFromObject(this.getMesh().clone());

            //TODO: this will be executed each time we update modelData (re-generate)
            // probably we need to check for initial generation only (this._modelData === null above)
            // or to have another repositioning logic for secondary re-generations

            // this is fixing position of a entity setting half size above the intersection (placement) point
            runCommands(
              new SetEntityPositionCommand(this.id, [
                this._position.x,
                this._position.y + (box.max.y - box.min.y) * 0.5,
                this._position.z,
              ]),
              false
            );
          }

          Client.userEntity.preventModelFalling(
            this.id,
            this._position,
            this._orientation
          );
        });
      }
    } else {
      this.modelLoaded = false;
    }
  }
  public getModelData() {
    return this._modelData;
  }

  public isRiggable(): boolean {
    const provider = this._modelData?.provider?.name;
    const rigged = this._modelData?.rigged;
    return (
      (provider === ModelGenerationProviders.Tripo ||
        provider === ModelGenerationProviders.Meshy) &&
      rigged === false
    );
  }

  public override addDragMarker(): void {
    this.dragMarker =
      this.dragMarker || new LineSegments(new BufferGeometry(), dashedMaterial);
    const circle = new Mesh(
      new CircleGeometry(0.2).rotateY(Math.PI / 2),
      new MeshBasicMaterial({
        color: 0xffffff,
        depthTest: false,
        side: DoubleSide,
        transparent: true,
        opacity: 0.15,
      })
    );
    circle.position.set(0, 2, 0);
    this.dragMarker.add(circle);

    this.dragMarker.visible = false;

    Client.scene.add(this.dragMarker);
    this.updateDragMarker();
  }

  public override updateDragMarker(): void {
    if (this.dragMarker) {
      this.getSimulatedPosition(pos);
      const [point, _angle, normal] = Client.raycastGround(
        [pos, vDown],
        [this]
      );

      if (point) {
        this.dragMarker.visible = true;
        this.dragMarker.geometry.setFromPoints([pos, point]);
        this.dragMarker.computeLineDistances();
        this.dragMarker.children[0].position.copy(point);
        this.dragMarker.children[0].quaternion.setFromUnitVectors(
          vLeft,
          normal || vLeft
        );
      } else {
        this.dragMarker.visible = false;
      }
    }
  }
  public override removeDragMarker(): void {
    if (this.dragMarker) {
      Client.scene.remove(this.dragMarker);
      this.dragMarker.geometry.dispose();
      this.dragMarker = null;
    }
  }

  public override setDragging(value: boolean, dirty = true) {
    if (this._dragging === value) {
      return;
    }

    super.setDragging(value, dirty);
    const physicsData = this.getPhysicsUserData();
    assertNotNull(physicsData, "Physics user data should not be null");

    physicsSetDragging(this, physicsData, value);
  }

  setSelected(value: boolean) {
    setObjectLayer(this.getMesh(), LAYERS.SELECTION, value);
    this.extraChildren.forEach((obj) => {
      obj.traverse((child) => {
        if ((child as Mesh).isMesh) {
          setObjectLayer(child, LAYERS.SELECTION, value);
        }
      });
    });
  }

  setHovered(value: boolean) {
    setObjectLayer(this.getMesh(), LAYERS.HOVER, value);
    this.extraChildren.forEach((obj) => {
      obj.traverse((child) => {
        if ((child as Mesh).isMesh) {
          setObjectLayer(child, LAYERS.HOVER, value);
        }
      });
    });
  }

  // mesh (bounding box) color
  public setColor(value: number, _dirty = true) {
    if (this._color !== value) {
      this._color = value;
      this.onColorChange();
      if (this.isInWorld && _dirty) {
        Client.markEntityAsDirty(this, false);
      }
    }
  }
  public getColor() {
    return this._color;
  }

  public generateModelFromImage({ imagePrompt, imageType }: ImagePromptData) {
    this.generateModel(imagePrompt, imageType);
  }

  //this function will be called when user presses enter on the prompt
  private generateFromPrompt() {
    if (!this._prompt) return;

    if (this.createdBy === Client.userEntity.id) {
      this.generateModel(this._prompt, "text");
    }
  }

  private async generateModel(input: string, type: string) {
    this.modelLoaded = false;
    this._progress.setProgressSteps(generateProgressSteps);

    this._progress.setProgress({
      progress: 0,
      message: "Preparing to generate model...",
    });
    if (type === "text") {
      console.debug("🐛 Generating model, from prompt: ", input);
    } else {
      console.debug("🐛 Generating model, from image: ");
    }
    this._progress.setIsGenerating(true);
    Client.markEntityAsDirty(this, false, false); //Need to update always. this is required due to Authority
    try {
      const taskId = await modelGenerator.generate({
        input,
        type,
        worldId: Client.worldInfo?.worldId,
      });
      if (!Client.world.isAlive(this.id)) {
        console.debug(
          "🐛 Entity is not alive, skipping model generation",
          this.id
        );
        return;
      }
      this.setTaskId(taskId);
    } catch (err) {
      console.error("Error generating model", err);
      this.onModelGenerationFailed();
    }
  }

  public async rig() {
    const entityWasStatic = this.getPhysicsUserData()?.physics?.isStatic;
    const modelData = this.getModelData();
    const taskId = this.taskId;
    if (modelData && taskId) {
      this.setRigging(true);
      const beforeSize = this.getEntitySize().clone();
      this.setOrientation(new Quaternion(), true, true);
      const afterSize = this.getEntitySize().clone();
      const yDiff = afterSize.y - beforeSize.y;
      if (yDiff > 0.01) {
        const position = new Vector3(0, 0, 0);
        this.getSimulatedPosition(position);
        position.y += yDiff / 2;
        this.setPosition(position, true);
      }
      setPhysicsStaticFlag(this, true);
      console.debug(`✨ Rigging mesh`, taskId);
      this.getProgress().setProgressSteps(rigProgressSteps);
      this.getProgress().startStep("rigging");
      this._progress.setIsGenerating(true);

      try {
        const riggingTaskId = await modelGenerator.rig({
          modelData,
          taskId,
          worldId: Client.worldInfo?.worldId,
        });
        this.setOriginalMeshTaskId(this.taskId);
        this.setTaskId(riggingTaskId);
      } catch (err) {
        this._progress.setIsGenerating(false);

        if (err instanceof ModelGenerationTaskError) {
          toast.error(`Error rigging model\n${err.message}\n${err.suggestion}`);
        } else {
          console.error("Error rigging model", err);
          toast.error("Error rigging model");
        }
        this.getProgress().fail();
        if (!entityWasStatic) {
          setPhysicsStaticFlag(this, false);
        }
      }
    } else {
      console.warn("Missing modelData property for entity", this.id);
    }
  }

  private onModelGenerationFailed = () => {
    this._progress.setIsGenerating(false);
    // this.removeBox();
    Client.removeEntity(this);

    // Here message and suggestion are coming from the last AI model so
    // probably will be incorrect to show them for the user
    toast.error("Error generating model");
  };

  private onColorChange() {
    // this.boundingBoxMaterial.color = new Color(this.color);
  }

  // TODO: temporary for tripo3d remove
  private removeChild(object: Object3D) {
    setObjectRaycastable(object);
    this.root.remove(object);
  }

  //#region JSON
  public override toJSON(): MeshEntityData {
    const currentAnimation =
      Client.world.getComponent(this.id, AnimationComponent) ?? undefined;
    const health =
      Client.world.getComponent(this.id, HealthComponent) ?? undefined;
    const dealDamage =
      Client.world.getComponent(this.id, TriggerAreaDamageComponent) ??
      undefined;
    const respawn =
      Client.world.getComponent(this.id, RequestRespawnComponent) ?? undefined;
    const respawnPoint =
      Client.world.getComponent(this.id, RespawnPointTag) ?? undefined;

    const data = this.data();
    const physicsUserData = data.getComponent(PhysicsUserDataComponent) ?? {};

    return {
      ...super.toJSON(),
      type: this.type,
      originType: this.getOriginType(),
      modelData: this._modelData,
      // imagePrompt: this._imagePrompt,
      // imageType: this._imageType,
      // importedModel: this.importedModel
      //   ? JSON.stringify(this.importedModel.toJSON())
      //   : undefined,
      // sizeInMb: this.sizeInMb,
      prompt: this._prompt,
      color: this._color,
      originalMeshTaskId: this.originalMeshTaskId,
      taskId: this.taskId,
      physicsUserData: physicsUserData,
      ...this.hierarchyController.toJSON(),
      ...this.detailController.toJSON(),
      ...this.materialController.toJSON(),
      animation: currentAnimation?.animationData,
      health: health,
      triggerAreaDamage: dealDamage,
      respawn: respawn,
      respawnPoint: respawnPoint,
    };
  }

  async loadFromUserData({ model, file_size_mb }: ImportedModel) {
    try {
      const expectedTime = file_size_mb ? file_size_mb / 2 : 20; //Assume 2MB/s
      this.getProgress().setProgressSteps([
        {
          message: "Uploading...",
          name: "uploading",
          progressEnd: 50,
          expectedTime: expectedTime,
        },
        {
          message: "Loading...",
          name: "loading",
          progressEnd: 100,
          expectedTime: expectedTime,
        },
      ]);
      const url = await Client.userEntity.importer.uploadModelToFirebase(
        model,
        (progress) => {
          this.getProgress().setProgress(progress);
        }
      );
      // add the uploaded model to assets (and the builder bag)
      const userId = auth.currentUser?.uid;
      if (userId) {
        await addDoc(assetsCollection, {
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          userId,
          type: "3d-model",
          status: "completed",
          isDeleted: false,
          rigged: false,
          url,
          name: this.root.name,
          provider: "UserUpload",
        });
      } else {
        console.error("Can't add uploaded model to assets, no user id found");
      }
      const mesh = this.getMesh();
      this.getProgress().startStep("loading");
      assignUrlToMesh(mesh, url);
      const loadOptions = this.getMeshEntityLoadOptions(url);
      const loadedModelData = await meshEntityModelLoad(loadOptions);
      if (loadedModelData) {
        setLoadedModelByUrl(url, loadedModelData);
      }

      // Ensure entity is still alive once our operation completes
      if (Client.world.isAlive(this.id)) {
        await this.parseImportedModel(url);
      }
    } catch (error) {
      console.error("Error importing model", error);
      toast.error("Error importing model");
      Client.removeEntity(this, true);
    }
  }

  public override fromJSON(json: MeshEntityData) {
    const data = this.data();
    // Add physicsUserData if not added earlier (such as constructing from Prefab)
    //
    // Ensure this is added before `super.fromJSON` as it may end up calling `setDragging` and trying to modify physicsData
    if (!data.hasComponent(PhysicsUserDataComponent)) {
      data.addComponent(PhysicsUserDataComponent, json.physicsUserData || {});
    }

    super.fromJSON(json);

    if (this.isInWorld) {
      // TODO: NILO-905 replace by system listening for component changes instead. Extract to completely separate component rather than component and setter in MeshEntity
      const oldPhysicsUserData = this.getPhysicsUserData() || {};
      if (
        !json.physicsUserData ||
        !isEqual(oldPhysicsUserData.physics, json.physicsUserData.physics)
      ) {
        this.setPhysicsUserData(json.physicsUserData || {}, false);
      }
    }
    if (json.modelData || json.firebaseUrl) {
      this._prompt = json.prompt;
    } else {
      this.setPrompt(json.prompt, false);
    }
    if (json.originType) this.setOriginType(json.originType, false);
    this.setOriginalMeshTaskId(json.originalMeshTaskId, false);
    this.setTaskId(json.taskId, false);

    if (json.modelData) {
      this.setModelData(json.modelData, false);
    } else if (json.firebaseUrl && json.firebaseUrl.length > 0) {
      // make it compatible with old data (for now)
      this.setModelData({ url: json.firebaseUrl }, false);
    }
    this.setColor(json.color, false);
    this.hierarchyController.fromJSON(json);
    this.detailController.fromJSON(json);
    this.materialController.fromJSON(json);

    const isAlive = Client.world.isAlive(this.id);
    this._jsonAnimation = undefined;
    if (isAlive) {
      if (json.animation) {
        Client.world.addComponent(
          this.id,
          AnimationComponent,
          // Note: no imported animation, we need to load it
          new PlayableAnimation(json.animation)
        );
      } else {
        Client.world.removeComponent(this.id, AnimationComponent);
      }

      // Handle health component
      if (json.health !== undefined) {
        Client.world.addComponent(this.id, HealthComponent, json.health);
      } else {
        Client.world.removeComponent(this.id, HealthComponent);
      }

      // Handle deal damage component
      if (json.triggerAreaDamage !== undefined) {
        Client.world.addComponent(
          this.id,
          TriggerAreaDamageComponent,
          json.triggerAreaDamage
        );
      } else {
        Client.world.removeComponent(this.id, TriggerAreaDamageComponent);
      }

      // Handle respawn component
      if (json.respawn !== undefined) {
        Client.world.addComponent(
          this.id,
          RequestRespawnComponent,
          json.respawn
        );
      } else {
        Client.world.removeComponent(this.id, RequestRespawnComponent);
      }

      // Handle respawn point tag
      if (json.respawnPoint !== undefined) {
        Client.world.addComponent(this.id, RespawnPointTag, json.respawnPoint);
      } else {
        Client.world.removeComponent(this.id, RespawnPointTag);
      }
    } else {
      // store for adding to the ECS entity later
      this._jsonAnimation = json.animation;
    }

    //TODO: here should be condition to differ generated MeshEntity from user model loaded
    this.generateBoundingBox();
  }

  getPhysicsUserData(): PhysicsUserData | null {
    return this.id
      ? Client.world.getComponent(this.id, PhysicsUserDataComponent)
      : null;
  }

  setPhysicsUserData(physicsUserData: PhysicsUserData, _dirty = true) {
    if (!physicsUserData) return;

    const oldPhysicsUserData = this.getPhysicsUserData() || {};

    const newPhysicsUserData = mergeObjectsDeep(
      oldPhysicsUserData,
      physicsUserData
    );

    const clonedData = simpleDeepClone(newPhysicsUserData);
    if (clonedData) {
      this._currentMesh.userData = {
        ...this._currentMesh.userData,
        ...clonedData,
      };
    }

    // Ensure component exists before setting; add if missing
    if (this.data().hasComponent(PhysicsUserDataComponent)) {
      this.data().setComponent(PhysicsUserDataComponent, clonedData);
    } else {
      this.data().addComponent(PhysicsUserDataComponent, clonedData);
    }

    if (clonedData.physics?.isSensor) {
      Client.world.addComponent(this.id, TriggerComponent, {});
    } else {
      Client.world.removeComponent(this.id, TriggerComponent);
    }

    this.getPhysicsBodyControl()?.updatePhysicsOptions(
      clonedData.physics || {}
    );

    // TODO:this is not enough, if a physics property changes we need to update the live entity
    // this.setDirty(true);
    if (this.isInWorld && _dirty) {
      Client.markEntityAsDirty(this, false);
    }
  }

  public updatePhysicsData() {
    const physicsData = this.getPhysicsUserData()?.physics;
    const bodyControl = this.getPhysicsBodyControl();
    if (physicsData && bodyControl) {
      bodyControl.updatePhysicsOptions(physicsData);
    }
  }

  public triggerSetup(active: boolean) {
    const physicsUserData = this.getPhysicsUserData() || {};
    const updatedPhysicsUserData: PhysicsUserData = {
      ...physicsUserData,
      physics: {
        ...physicsUserData.physics,
        isSensor: active,
        motionType: active ? "kinematic" : "dynamic",
        gravityFactor: active ? 0 : 1,
      },
    };
    this.setPhysicsUserData(updatedPhysicsUserData);

    this.getPhysicsBodyControl()?.updatePhysicsOptions({
      ...physicsUserData.physics,
      ...updatedPhysicsUserData.physics,
    });
  }

  // update the percentage of the model when loaded from firebase
  //@param {}
  //@return { percentage of model loaded }
  public updateProgress(event: ProgressEvent, isFirebaseCall: boolean): number {
    const total = event.total || 0;
    const loaded = event.loaded || 0;
    let percentage = total ? (loaded / total) * 100 : 0;

    if (isFirebaseCall && !this._progress.inProgress()) {
      // this.addBox();
    } else if (!isFirebaseCall) {
      const remainingProgress = 100 - this._modelLoadingProgress;
      percentage = percentage * 0.01 * remainingProgress;
      percentage += this._modelLoadingProgress;
    }

    return percentage;
  }

  //#region loadModel
  // loadmodelFromUri
  //@param { model url }
  //@return {} no return
  modelUrl(): string | null {
    if (process.env.USE_FIREBASE_EMULATOR === "true" && this._modelData?.url) {
      // if we are using firebase emulator, the url will be pointing to localhost
      // we need to fix the url to point to the correct one (the one that the client is using)
      const url = new URL(this._modelData.url);
      if (url.hostname === "localhost" || url.hostname === "127.0.0.1") {
        url.hostname = window.location.hostname;
        return url.toString();
      }
    }
    return this._modelData?.url || null;
  }
  private _loadModelPropertiesPending(): LoadModelPropertiesPending {
    return {
      modelUrl: this.modelUrl(),
      detail: this.detailController.detail(),
    };
  }
  // this conflicts with the modelLoaded property, to investigate
  isLoaded(): boolean {
    return (
      this._loadModelPropertiesCompleted != null &&
      loadModelPropertiesEquals(
        this._loadModelPropertiesCompleted,
        this._loadModelPropertiesPending()
      )
    );
  }
  override destroy() {
    super.destroy();

    if (this.getIsGenerating()) {
      this.removeBox();
      this.progress.abort();
    }

    if (this._loadModelPropertiesCompleted) {
      dereferenceForDetailAndUrl(
        this._currentMesh,
        this._loadModelPropertiesCompleted
      );
    }

    this.delegates.onDeleted.invoke();
  }

  public handleModelLoadedWithExtraChildren(loadedMeshData: LoadedMeshData) {
    //this.getMesh().material = invisibleMaterial;
    // this.getMesh().clear();
    this.getMesh().visible = false;
    //TODO:: cloning, probably not the most efficient way to do this
    // Did this to allow duplication of skinned meshes
    this.extraChildren.push(
      SkeletonUtils.clone(loadedMeshData.extraChildren[0])
    );
    for (const child of this.extraChildren) {
      this.root.add(child);
      //const mesh = this.getMesh();
      //const bbox = mesh.geometry.boundingBox!;
      //const offset = new Vector3(0, (bbox.max.y - bbox.min.y) * -0.0, 0);
      //child.position.copy(offset); // hacky fix to match skinned mesh and the mesh

      child.userData = {};
      let addedAnimator = Client.world.hasComponent(this.id, AnimatorComponent);
      if (!addedAnimator) {
        child.traverse((child) => {
          if (!(child instanceof SkinnedMesh)) return;
          child.updateMatrixWorld();
          child.computeBoundingBox();

          if (!addedAnimator) {
            const animator = this.createAnimator(child);
            addedAnimator = true;
            Client.world.addComponent(this.id, AnimatorComponent, animator);

            this.containsSkinnedMesh = true;
            setPhysicsStaticFlag(this, true);
            setObjectRaycastable(child);

            const assignCharacterListener = () => {
              const id = CHARACTER_BEHAVIOUR_ID;
              if (
                Client.world.hasComponent(this.id, CharacterControllerComponent)
              )
                return;

              this.state.listener.setDataAddedListener(id, async () => {
                const character = await CharacterController.createAsync(this);
                this.state.listener.setDataChangedListener(
                  id,
                  (data: unknown) => {
                    Client.world
                      .getComponent(this.id, CharacterControllerComponent)!
                      .fromJSON(this.data(), data as CharacterJSON);
                  }
                );
                Client.world.addComponents(this.id, [
                  [CharacterControllerComponent, character],
                ]);
                this.state.listener.setDataRemovedListener(id, () => {
                  character.dispose(this);
                });
              });
            };
            child.updateMatrixWorld();
            child.computeBoundingBox();

            if (Client.physics) {
              assignCharacterListener();
            } else {
              Client.events.once("physics-started", assignCharacterListener);
            }
          }
          if (child instanceof SkinnedMesh) {
            child.castShadow = true;
            child.receiveShadow = true;
          }
        });
      }
      this.getMesh().castShadow = false;
    }
  }

  //#endregion
  //#region misc

  // this function is used to add child
  //@param { 3D object }
  //@return {} no return
  private _addChild(object: Object3D) {
    setObjectRaycastable(object);
    this.root.add(object);
  }

  public setRootParent(parent: Object3D) {
    parent.add(this.root);
  }
  //#endregion

  // generation of bounding box and loader
  // this function is used to generate boundingBox in the editor.
  //@param {editor reference, id of tripo3d api}
  //@return {} no return
  private generateBoundingBox() {
    if (this.boundingBox.children.length > 0) {
      return;
    }
    const userEntity = Client.getEntity(
      this.createdBy as string
    ) as UserEntity | null;

    const color =
      this.createdBy === Client.userEntity.id
        ? localColor
        : userEntity
          ? userEntity.getColor()
          : 0xffffff;
    const geometry = new BoxGeometry(0.95, 0.95, 0.95);
    this.boundingBoxMaterial = new EmissiveFresnelShader({
      color: new Color(color),
      minOpacity: 0.25,
      maxOpacity: 0.6,
      power: 1.5,
    });

    const boxMesh = new Mesh(geometry, this.boundingBoxMaterial);
    boxMesh.name = GENERATION_BOX_NAME;
    boxMesh.renderOrder = 1;

    const generationBox = new Box3(
      new Vector3(-0.475, -0.475, -0.475),
      new Vector3(0.475, 0.475, 0.475)
    );

    const outline = new BoxLineObject(this, color);
    outline.material.opacity = 0.25;
    outline.setFromBox(generationBox);
    outline.setAnimated(true);
    outline.name = GENERATION_OUTLINE_NAME;

    this.boundingBox.name = "bbox|" + this.id;
    this.boundingBox.add(boxMesh);
    this.boundingBox.add(outline);

    setObjectNotRaycastable(boxMesh);
  }

  public addBox() {
    this.root.add(this.boundingBox);
  }

  public removeBox() {
    this.root.remove(this.boundingBox);
  }

  // Entity present check
  // Function to check if an entity is present in the scene
  //@param {reference of editor's scene, name of the entity}
  //@return {true of false }
  public isEntityPresent(entityName: string) {
    let entityFound = false;

    Client.scene.traverse(function (object: Object3D) {
      if (object.name === entityName) {
        entityFound = true;
      }
    });

    return entityFound;
  }
  //#endregion

  // // this function is used to show alert message
  // //@param {} no params
  // //@return {} no return
  // public showAlert() {
  //   //window.alert("there was an error in generating the model");
  // }

  // public async importDragModel(file: File) {
  //   console.warn("importDragModel");
  //   const modelImporter = new Importer();

  //   modelImporter.uploadModelToFirebase(file);

  //   const model = await modelImporter.loadModel(file);

  //   if (!model) {
  //     Client.removeEntity(this);
  //     return;
  //   }

  //   this.setImportModel(model);
  // }

  public async setImportModel(model: Group) {
    // Check the structure of tempModel before exporting
    this.modelLoaded = true;
    this._progress.setProgress({
      progress: 0,
    });
    this.containsSkinnedMesh = false;
    model.traverse((child) => {
      if (this.containsSkinnedMesh) return;
      if (child instanceof SkinnedMesh) {
        this.containsSkinnedMesh = true;
      }
    });

    if (model.children.length > 0) await mergeMeshes(this._currentMesh, model);
    await initializeModelMesh(this._currentMesh); // containsSkinnedMesh must be false, couldn't find a way to get around this in a better way

    this._currentMesh.userData = {
      ...this._currentMesh.userData,
      ...model.userData,
    };

    this.removeBox();

    if (!this.containsSkinnedMesh) {
      globalDelegates.addEntity.invoke(this);
    }
  }

  public override isEntityNearCamera() {
    const distance = this.getDistanceToCamera();
    const entitySize = this.getEntitySize();
    const maxDistance = Math.max(Math.max(...entitySize) * 7.5, 10);
    return distance < maxDistance;
  }

  public override worldToScreen(
    offset?: Vector3,
    showAtBottom: boolean = true
  ): Vector2 {
    const screenPosition = super.worldToScreen(offset).clone();
    if (!showAtBottom) {
      return screenPosition;
    }

    // Get position on the bottom of the bounding box
    const camera = Client.userEntity.getCamera();
    const bbox = this.getBoundingBox();
    if (!camera || !bbox) return screenPosition;

    const corners = [
      new Vector3(bbox.min.x, bbox.min.y, bbox.min.z),
      new Vector3(bbox.max.x, bbox.min.y, bbox.min.z),
      new Vector3(bbox.min.x, bbox.min.y, bbox.max.z),
      new Vector3(bbox.max.x, bbox.min.y, bbox.max.z),
    ];

    let lowestScreenPosition = screenPosition;
    for (const corner of corners) {
      const cornerScreenPosition = Client.worldToScreen(corner)!.clone();
      if (cornerScreenPosition.y > lowestScreenPosition.y) {
        lowestScreenPosition = cornerScreenPosition;
      }
    }

    screenPosition.y = lowestScreenPosition!.y;

    //Ensure position will be on the bottom of the gizmos(TransformControls) and bounding box
    const _isMobile = isMobile();
    const gizmoSize =
      (_isMobile ? 1.9 : 1.3) *
      TransformControls.GetGizmoScale(this._position, camera, false);

    const cameraDownVector = new Vector3(0, -1, 0);
    cameraDownVector.applyQuaternion(camera.quaternion);

    const gizmoLowerEdge = this._position
      .clone()
      .add(cameraDownVector.multiplyScalar(gizmoSize));
    const gizmoLowerEdgeScreenPosition = Client.worldToScreen(gizmoLowerEdge)!;
    if (gizmoLowerEdgeScreenPosition.y > screenPosition.y) {
      screenPosition.y = gizmoLowerEdgeScreenPosition.y;
    }

    return screenPosition;
  }

  public getBoundingBox(): Box3 | undefined {
    const boundingBox = this._currentMesh.geometry.boundingBox?.clone();
    if (boundingBox && boundingBox.min.x !== Infinity) {
      this._currentMesh.updateWorldMatrix(true, false);
      boundingBox?.applyMatrix4(this._currentMesh.matrixWorld);
      return boundingBox;
    }
    return undefined;
  }

  public getSize(): Vector3 {
    const result = this.getGeometrySize();
    const scale = new Vector3();
    this.getScale(scale);
    result.multiply(scale);
    return result;
  }

  override getOffset(): Vector3 {
    const physicsQuaternion = this._simulatedOrientation;
    return this.getMesh()
      .position.clone()
      .multiply(this._scale)
      .applyQuaternion(this.getMesh().quaternion)
      .applyQuaternion(
        physicsQuaternion
          ? new Quaternion(
              physicsQuaternion.x,
              physicsQuaternion.y,
              physicsQuaternion.z,
              physicsQuaternion.w
            )
          : this._orientation
      );
  }

  public getGeometrySize(): Vector3 {
    const result = new Vector3();
    const box = this.getGeometryBox();
    box.getSize(result);
    return result;
  }

  public getGeometryBox() {
    const mesh = this.getMesh();
    if (!mesh.geometry.boundingBox) mesh.geometry.computeBoundingBox();
    if (
      mesh.geometry.boundingBox &&
      isFinite(mesh.geometry.boundingBox.min.x) &&
      isFinite(mesh.geometry.boundingBox.min.y) &&
      isFinite(mesh.geometry.boundingBox.min.z) &&
      isFinite(mesh.geometry.boundingBox.max.x) &&
      isFinite(mesh.geometry.boundingBox.max.y) &&
      isFinite(mesh.geometry.boundingBox.max.z)
    ) {
      return mesh.geometry.boundingBox;
    }
    return new Box3(new Vector3(), new Vector3());
  }

  public getEntitySize(): Vector3 {
    const bbox = this.getBoundingBox();
    if (bbox) {
      return new Vector3(
        bbox.max.x - bbox.min.x,
        bbox.max.y - bbox.min.y,
        bbox.max.z - bbox.min.z
      );
    }
    return new Vector3(1, 1, 1);
  }

  public getGlobalSize() {
    const mesh = this.getMesh();
    if (!mesh) {
      return new Vector3();
    }
    const box = new Box3().setFromObject(mesh.clone());
    const size = box
      .getSize(new Vector3())
      .multiply(this._scale)
      .applyQuaternion(this._orientation);
    size.x = Math.abs(size.x);
    size.y = Math.abs(size.y);
    size.z = Math.abs(size.z);
    return size;
  }
  // this function is used to update the firebase url of locally imported model
  //@param { firebase url }
  //@return {} no return
  public async parseImportedModel(modelUrl: string) {
    assertTrue(Client.world.isAlive(this.id), "Entity should be alive");
    await this.setModelData({ url: modelUrl });
  }

  public override getPhysicsBodyControl() {
    if (!this.getMesh()) return null;
    return super.getPhysicsBodyControl();
  }

  public getMeshEntityLoadOptions(
    modelUrl: string
  ): MeshEntityModelLoadOptions {
    return { modelUrl, id: this.id };
  }

  public getFindOrCreateModelByUrlAndDetailOptions(
    modelUrl: string
  ): FindOrCreateModelByUrlAndDetailOptions {
    return {
      ...this.getMeshEntityLoadOptions(modelUrl),
      detail: this.detailController.detail(),
      mesh: this._currentMesh,
    } as FindOrCreateModelByUrlAndDetailOptions;
  }
}
