import { Vector3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ector<PERSON><PERSON><PERSON>, Quaternion<PERSON>ike } from "three";

import { GameEntity } from "../Entity";
import { Transformable } from "@/liveblocks.config";

const _p = new Vector3();
const _q = new Quaternion();

//TODO later :: Generalize this to support more types of interpolation
interface InterpolationState<T> {
  time: number;
  endTime: number;
  target: T;
  initial: T;
  type: "vector3" | "quaternion";
}

export class EntityReplication {
  private _entity: GameEntity;

  private _interpolationStates: {
    [key: string]: InterpolationState<Vector3 | Quaternion>;
  } = {};

  private _interpolationDuration: number = 0.1;

  constructor(entity: GameEntity) {
    this._entity = entity;
  }

  public disableInterpolation(type: "position" | "orientation") {
    delete this._interpolationStates[type];
  }

  public refreshPositionInterpolation(position: Vector3Like) {
    const interpolationState = this._interpolationStates[
      "position"
    ] as InterpolationState<Vector3>;
    this._entity.getSimulatedPosition(interpolationState.initial);
    interpolationState.time = 0;
    interpolationState.target.copy(position);
  }

  public refreshOrientationInterpolation(orientation: Quaternion) {
    const interpolationState = this._interpolationStates[
      "orientation"
    ] as InterpolationState<Quaternion>;
    this._entity.getOrientation(interpolationState.initial);
    interpolationState.time = 0;
    interpolationState.target.copy(orientation);
  }

  private _firstTime: boolean = true;

  public fromJSON(json: Transformable) {
    // yes, we use the _p and _q vectors here just to copy
    // them to themselves, but we keep this for performance reasons.
    // we can get rid of this once we have non-Liveblocks networking
    _p.set(json.positionX, json.positionY, json.positionZ);
    _q.set(
      json.orientationX,
      json.orientationY,
      json.orientationZ,
      json.orientationW
    );
    this.fromTransform(_p, _q);
  }

  public fromTransform(position: Vector3Like, orientation: QuaternionLike) {
    _p.set(position.x, position.y, position.z);
    _q.set(orientation.x, orientation.y, orientation.z, orientation.w);
    const entityPosition = new Vector3();
    const entityOrientation = new Quaternion();
    this._entity.getPosition(entityPosition);
    this._entity.getOrientation(entityOrientation);

    if (this._firstTime) {
      this._firstTime = false;
      // NOTE: entity is not yet spawned, so do not touch the ECS (for now)
      this._entity.setTransformProps(_p, _q);
      return;
    }

    if (!_p.equals(entityPosition)) {
      this._interpolationStates["position"] = {
        time: 0,
        endTime: this._interpolationDuration,
        target: _p.clone(),
        initial: entityPosition,
        type: "vector3",
      };
      this.refreshPositionInterpolation(_p);
    }

    if (!_q.equals(entityOrientation)) {
      this._interpolationStates["orientation"] = {
        time: 0,
        endTime: this._interpolationDuration,
        target: _q.clone(),
        initial: entityOrientation,
        type: "quaternion",
      };
      this.refreshOrientationInterpolation(_q);
    }
  }

  public update(deltaTime: number) {
    if (this._interpolationStates["position"]) {
      this.updatePositionInterpolation(deltaTime);
    }
    if (this._interpolationStates["orientation"]) {
      this.updateOrientationInterpolation(deltaTime);
    }
  }

  public updatePositionInterpolation(deltaTime: number) {
    const state = this._interpolationStates[
      "position"
    ] as InterpolationState<Vector3>;
    state.time += deltaTime;
    const t = Math.min(state.time / state.endTime, 1.0);
    _p.copy(state.initial).lerp(state.target, t);
    this._entity.setPosition(_p, false);

    if (t >= 1.0) {
      delete this._interpolationStates["position"];
    }
  }

  public updateOrientationInterpolation(deltaTime: number) {
    const state = this._interpolationStates[
      "orientation"
    ] as InterpolationState<Quaternion>;
    state.time += deltaTime;
    const t = Math.min(state.time / state.endTime, 1.0);
    _q.copy(state.initial).slerp(state.target, t);
    this._entity.setOrientation(_q, false, false);

    if (t >= 1.0) {
      delete this._interpolationStates["orientation"];
    }
  }

  public isInterpolating(type: "position" | "orientation") {
    return this._interpolationStates[type] !== undefined;
  }
}
