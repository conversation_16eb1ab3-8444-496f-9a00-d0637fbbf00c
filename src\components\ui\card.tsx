import * as React from "react";

import { cn } from "@/lib/utils";

function Card({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card"
      className={cn(
        "bg-nilo-fill-tertiary/90 backdrop-blur-sm text-nilo-text-primary flex flex-col gap-nilo-6 rounded-nilo-lg border border-nilo-border-tertiary py-nilo-6 shadow-[2px_4px_7px_0px_rgba(0,0,0,0.50),1px_1px_1px_0px_rgba(0,0,0,0.25)]",
        className
      )}
      {...props}
    />
  );
}

function CardHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-header"
      className={cn(
        "@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-nilo-1.5 px-nilo-padding-default has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-nilo-6",
        className
      )}
      {...props}
    />
  );
}

function CardTitle({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-title"
      className={cn(
        "nilo-size-5 leading-nilo-tight font-nilo-bold font-nilo-display text-nilo-text-secondary",
        className
      )}
      {...props}
    />
  );
}

function CardDescription({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-description"
      className={cn(
        "text-nilo-text-tertiary text-sm font-nilo-regular font-nilo-primary",
        className
      )}
      {...props}
    />
  );
}

function CardAction({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-action"
      className={cn(
        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
        className
      )}
      {...props}
    />
  );
}

function CardContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-content"
      className={cn(
        "px-nilo-padding-default scrollbar-thin-nilo overflow-auto",
        className
      )}
      {...props}
    />
  );
}

function CardFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-footer"
      className={cn(
        "flex items-center px-nilo-padding-default [.border-t]:pt-nilo-6",
        className
      )}
      {...props}
    />
  );
}

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardAction,
  CardDescription,
  CardContent,
};
