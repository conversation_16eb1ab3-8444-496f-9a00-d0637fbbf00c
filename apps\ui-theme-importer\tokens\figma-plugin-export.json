{"primitives-nilo-uikit": {"color": {"primary": {"100": {"value": "#001100FF", "type": "color"}, "200": {"value": "#002D00FF", "type": "color"}, "300": {"value": "#094A00FF", "type": "color"}, "400": {"value": "#1E6A06FF", "type": "color"}, "500": {"value": "#328B2CFF", "type": "color"}, "600": {"value": "#46AD4CFF", "type": "color"}, "700": {"value": "#59D16BFF", "type": "color"}, "800": {"value": "#58F287FF", "type": "color"}, "900": {"value": "#BFFFCCFF", "type": "color"}}, "accent": {"100": {"value": "#0B0034FF", "type": "color"}, "200": {"value": "#1A045FFF", "type": "color"}, "300": {"value": "#2A2086FF", "type": "color"}, "400": {"value": "#3B3AADFF", "type": "color"}, "500": {"value": "#4D55D6FF", "type": "color"}, "600": {"value": "#5865F2FF", "type": "color"}, "700": {"value": "#869BFFFF", "type": "color"}, "800": {"value": "#ACC2FFFF", "type": "color"}, "900": {"value": "#D8EAFFFF", "type": "color"}}, "neutral": {"100": {"value": "#08090CFF", "type": "color"}, "200": {"value": "#111318FF", "type": "color"}, "300": {"value": "#191C24FF", "type": "color"}, "400": {"value": "#232C34FF", "type": "color"}, "500": {"value": "#3D4852FF", "type": "color"}, "600": {"value": "#58626CFF", "type": "color"}, "700": {"value": "#79838DFF", "type": "color"}, "800": {"value": "#9DA6AFFF", "type": "color"}, "900": {"value": "#CAD1D8FF", "type": "color"}, "black": {"value": "#000000FF", "type": "color"}, "white": {"value": "#FFFFFFFF", "type": "color"}}, "warning": {"100": {"value": "#210000FF", "type": "color"}, "200": {"value": "#401200FF", "type": "color"}, "300": {"value": "#5E2F00FF", "type": "color"}, "400": {"value": "#7D4C00FF", "type": "color"}, "500": {"value": "#9C6C00FF", "type": "color"}, "600": {"value": "#BB8D1BFF", "type": "color"}, "700": {"value": "#DAB039FF", "type": "color"}, "800": {"value": "#F7DE52FF", "type": "color"}, "900": {"value": "#FBF0B3FF", "type": "color"}}, "danger": {"100": {"value": "#220018FF", "type": "color"}, "200": {"value": "#490025FF", "type": "color"}, "300": {"value": "#6F0033FF", "type": "color"}, "400": {"value": "#97073FFF", "type": "color"}, "500": {"value": "#BF2848FF", "type": "color"}, "600": {"value": "#ED4245FF", "type": "color"}, "700": {"value": "#FF6F6AFF", "type": "color"}, "800": {"value": "#FF9E97FF", "type": "color"}, "900": {"value": "#FFD4CCFF", "type": "color"}}}, "typography": {"font": {"display": {"value": "Fliper", "type": "string"}, "primary": {"value": "Inter", "type": "string"}}, "weight": {"light": {"value": "300", "type": "fontWeights"}, "regular": {"value": "400", "type": "fontWeights"}, "medium": {"value": "500", "type": "fontWeights"}, "semibold": {"value": "600", "type": "fontWeights"}, "bold": {"value": "700", "type": "fontWeights"}, "extrabold": {"value": "800", "type": "fontWeights"}}, "size": {"0": {"value": "10", "type": "float"}, "1": {"value": "12", "type": "float"}, "2": {"value": "14", "type": "float"}, "3": {"value": "16", "type": "float"}, "4": {"value": "18", "type": "float"}, "5": {"value": "20", "type": "float"}, "6": {"value": "24", "type": "float"}, "7": {"value": "30", "type": "float"}, "8": {"value": "36", "type": "float"}, "9": {"value": "48", "type": "float"}, "10": {"value": "60", "type": "float"}, "11": {"value": "72", "type": "float"}}, "line-height": {"normal": {"value": "1.2000000476837158", "type": "float"}, "tight": {"value": "1", "type": "float"}, "tighter": {"value": "0.8999999761581421", "type": "float"}, "tightest": {"value": "0.75", "type": "float"}}}, "spacing": {"0": {"value": "0", "type": "float"}, "1": {"value": "2", "type": "float"}, "2": {"value": "4", "type": "float"}, "3": {"value": "8", "type": "float"}, "4": {"value": "12", "type": "float"}, "5": {"value": "16", "type": "float"}, "6": {"value": "20", "type": "float"}, "7": {"value": "24", "type": "float"}, "8": {"value": "28", "type": "float"}, "9": {"value": "32", "type": "float"}, "10": {"value": "40", "type": "float"}, "11": {"value": "44", "type": "float"}, "12": {"value": "48", "type": "float"}, "13": {"value": "56", "type": "float"}}}, "component-nilo-uikit": {"typography": {"Title": {"Title-1": {"size": {"value": "{typography.size.11}", "type": "float"}, "line-height": {"value": "80", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.display}", "type": "string"}}, "Title-2": {"size": {"value": "{typography.size.10}", "type": "float"}, "line-height": {"value": "64", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.display}", "type": "string"}}, "Title-3": {"size": {"value": "{typography.size.9}", "type": "float"}, "line-height": {"value": "56", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.display}", "type": "string"}}}, "Header": {"H1": {"size": {"value": "{typography.size.7}", "type": "float"}, "line-height": {"value": "36", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.display}", "type": "string"}}, "H2": {"size": {"value": "{typography.size.6}", "type": "float"}, "line-height": {"value": "32", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.display}", "type": "string"}}, "H3": {"size": {"value": "{typography.size.5}", "type": "float"}, "line-height": {"value": "28", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.display}", "type": "string"}}, "H4": {"size": {"value": "{typography.size.3}", "type": "float"}, "line-height": {"value": "20", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}}, "Body": {"Body-1": {"size": {"value": "{typography.size.3}", "type": "float"}, "line-height": {"value": "20", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}, "Body-2": {"size": {"value": "{typography.size.2}", "type": "float"}, "line-height": {"value": "18", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}, "Body-3": {"size": {"value": "{typography.size.1}", "type": "float"}, "line-height": {"value": "16", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}, "Body-4": {"size": {"value": "{typography.size.0}", "type": "float"}, "line-height": {"value": "14", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}}, "Label": {"Label-1": {"size": {"value": "{typography.size.3}", "type": "float"}, "line-height": {"value": "20", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}, "Label-2": {"size": {"value": "{typography.size.2}", "type": "float"}, "line-height": {"value": "18", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}, "Label-3": {"size": {"value": "{typography.size.1}", "type": "float"}, "line-height": {"value": "16", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}, "Label-4": {"size": {"value": "{typography.size.0}", "type": "float"}, "line-height": {"value": "14", "type": "float"}, "letter-spacing": {"value": "0", "type": "float"}, "family": {"value": "{typography.font.primary}", "type": "string"}}}}}, "semantic-nilo-uikit": {"fill": {"primary": {"value": "{color.primary.800}", "type": "color"}, "primary-hover": {"value": "{color.primary.900}", "type": "color"}, "primary-pressed": {"value": "{color.primary.700}", "type": "color"}, "primary-dark": {"value": "{color.primary.200}", "type": "color"}, "primary-dark-hover": {"value": "{color.primary.300}", "type": "color"}, "primary-dark-pressed": {"value": "{color.primary.100}", "type": "color"}, "secondary": {"value": "{color.neutral.400}", "type": "color"}, "secondary-hover": {"value": "{color.neutral.500}", "type": "color"}, "secondary-pressed": {"value": "{color.neutral.300}", "type": "color"}, "tertiary": {"value": "{color.neutral.200}", "type": "color"}, "tertiary-hover": {"value": "{color.neutral.300}", "type": "color"}, "tertiary-pressed": {"value": "{color.neutral.100}", "type": "color"}, "disabled": {"value": "{color.neutral.500}", "type": "color"}, "quaternary": {"value": "{color.neutral.900}", "type": "color"}, "quaternary-hover": {"value": "{color.neutral.white}", "type": "color"}, "quaternary-pressed": {"value": "{color.neutral.800}", "type": "color"}, "error": {"value": "{color.danger.600}", "type": "color"}, "error-hover": {"value": "{color.danger.700}", "type": "color"}, "error-pressed": {"value": "{color.danger.500}", "type": "color"}, "warning": {"value": "{color.warning.800}", "type": "color"}, "warning-hover": {"value": "{color.warning.900}", "type": "color"}, "warning-pressed": {"value": "{color.warning.700}", "type": "color"}}, "icon": {"primary": {"value": "{color.neutral.100}", "type": "color"}, "secondary": {"value": "{color.neutral.900}", "type": "color"}, "tertiary": {"value": "{color.neutral.600}", "type": "color"}, "quaternary": {"value": "{color.primary.700}", "type": "color"}, "disabled": {"value": "{color.neutral.700}", "type": "color"}, "error": {"value": "{color.danger.600}", "type": "color"}, "warning": {"value": "{color.warning.800}", "type": "color"}, "placeholder": {"value": "{color.neutral.700}", "type": "color"}}, "text": {"primary": {"value": "{color.neutral.100}", "type": "color"}, "primary-hover": {"value": "{color.neutral.200}", "type": "color"}, "secondary": {"value": "{color.neutral.900}", "type": "color"}, "secondary-hover": {"value": "{color.neutral.white}", "type": "color"}, "tertiary": {"value": "{color.neutral.600}", "type": "color"}, "tertiary-hover": {"value": "{color.neutral.700}", "type": "color"}, "quaternary": {"value": "{color.primary.700}", "type": "color"}, "quaternary-hover": {"value": "{color.primary.800}", "type": "color"}, "disabled": {"value": "{color.neutral.700}", "type": "color"}, "error": {"value": "{color.danger.600}", "type": "color"}, "error-hover": {"value": "{color.danger.700}", "type": "color"}, "warning": {"value": "{color.warning.800}", "type": "color"}, "warning-hover": {"value": "{color.warning.900}", "type": "color"}, "placeholder": {"value": "{color.neutral.700}", "type": "color"}}, "border": {"primary": {"value": "{color.primary.800}", "type": "color"}, "secondary": {"value": "{color.neutral.400}", "type": "color"}, "tertiary": {"value": "{color.neutral.300}", "type": "color"}, "quaternary": {"value": "{color.neutral.900}", "type": "color"}, "disabled": {"value": "{color.neutral.500}", "type": "color"}}, "corner-radius": {"none": {"value": "0", "type": "float"}, "xs": {"value": "2", "type": "float"}, "sm": {"value": "4", "type": "float"}, "default": {"value": "8", "type": "float"}, "lg": {"value": "16", "type": "float"}, "full": {"value": "9999", "type": "float"}}, "padding": {"padding-xtra-small": {"value": "{spacing.3}", "type": "float"}, "padding-small": {"value": "{spacing.4}", "type": "float"}, "padding-default": {"value": "{spacing.5}", "type": "float"}, "padding-large": {"value": "{spacing.7}", "type": "float"}, "padding-xtra-large": {"value": "{spacing.9}", "type": "float"}}, "spacing": {"spacing-small": {"value": "{spacing.2}", "type": "float"}, "spacing-default": {"value": "{spacing.3}", "type": "float"}, "spacing-large": {"value": "{spacing.5}", "type": "float"}}, "height": {"height-xtra-small": {"value": "{spacing.7}", "type": "float"}, "height-small": {"value": "{spacing.9}", "type": "float"}, "height-default": {"value": "{spacing.10}", "type": "float"}, "height-large": {"value": "{spacing.11}", "type": "float"}, "height-border": {"value": "{spacing.1}", "type": "float"}}}}